# 📈 STATISTIQUES DE CONSOMMATION - YASSINAPP

## 🎯 Nouvelle Fonctionnalité : Consommation par Semaine

J'ai créé un système complet de **statistiques de consommation** basé sur vos données de stock quotidiennes.

## 📊 Comment ça Fonctionne

### 🔢 Calcul Automatique

**Formule :** `Consommation = Stock Initial + Entrées - Stock Final`

**Exemple concret :**
```
Lundi - Pâte à pizza:
- Stock Initial: 25,0 kg
- Entrées: 10,0 kg (livraison)
- Stock Final: 30,0 kg (inventaire soir)
- Consommation = 25,0 + 10,0 - 30,0 = 5,0 kg consommé
```

### 📅 Périodes Analysées

1. **📅 Cette Semaine** : Lundi à Dimanche courant
2. **📆 Ce Mois** : Du 1er au dernier jour du mois
3. **📊 Historique** : 4 dernières semaines avec comparaison

## 📱 Interface de Consommation

```
┌─────────────────────────────────────┐
│ [← Retour]  Consommation      [🔄]  │
├─────────────────────────────────────┤
│ [<PERSON><PERSON><PERSON>] [<PERSON><PERSON>] [Historique]       │
├─────────────────────────────────────┤
│ 📅 Cette Semaine                    │
│ 15 jul - 21 jul                     │
│ 5 jours de données                  │
├─────────────────────────────────────┤
│ Pâte à pizza (kg)        17,0 consommé│
│ Mozzarella (kg)           4,5 consommé│
│ Sauce tomate (L)          8,2 consommé│
│ Pepperoni (kg)            3,1 consommé│
│ Olives (kg)               2,3 consommé│
└─────────────────────────────────────┘
```

## 🚀 Comment Accéder

### 📱 Navigation Simple

1. **Lancez l'application** : `npx expo start`
2. **Connexion** : admin / 123
3. **Écran principal** → **"📈 Consommation"**

### 🔄 Fonctionnalités

- **3 onglets** : Semaine, Mois, Historique
- **Bouton 🔄** : Actualise les calculs
- **Données temps réel** : Basées sur votre stock quotidien

## 📊 Exemple d'Utilisation Complète

### 📝 Semaine Type (5 jours de données)

**Lundi :**
- Pâte : 25,0 + 10,0 - 30,0 = **5,0 kg consommé**
- Mozzarella : 15,0 + 5,0 - 18,0 = **2,0 kg consommé**

**Mardi :**
- Pâte : 30,0 + 5,0 - 32,0 = **3,0 kg consommé**
- Mozzarella : 18,0 + 0,0 - 17,0 = **1,0 kg consommé**

**Mercredi :**
- Pâte : 32,0 + 0,0 - 28,0 = **4,0 kg consommé**
- Mozzarella : 17,0 + 2,0 - 18,5 = **0,5 kg consommé**

**Jeudi :**
- Pâte : 28,0 + 8,0 - 33,0 = **3,0 kg consommé**
- Mozzarella : 18,5 + 0,0 - 18,0 = **0,5 kg consommé**

**Vendredi :**
- Pâte : 33,0 + 0,0 - 31,0 = **2,0 kg consommé**
- Mozzarella : 18,0 + 1,0 - 18,5 = **0,5 kg consommé**

### 📈 Résultat Affiché

**Consommation Semaine :**
- **Pâte à pizza** : 5,0 + 3,0 + 4,0 + 3,0 + 2,0 = **17,0 kg**
- **Mozzarella** : 2,0 + 1,0 + 0,5 + 0,5 + 0,5 = **4,5 kg**

## 📅 Les 3 Vues Disponibles

### 🗓️ Vue "Semaine"

```
📅 Cette Semaine
15 jul - 21 jul
5 jours de données

Pâte à pizza (kg)        17,0 consommé
Mozzarella (kg)           4,5 consommé
Sauce tomate (L)          8,2 consommé
Pepperoni (kg)            3,1 consommé
Olives (kg)               2,3 consommé
```

### 📆 Vue "Mois"

```
📆 Ce Mois
Juillet 2025
22 jours de données

Pâte à pizza (kg)        68,5 consommé
Mozzarella (kg)          18,2 consommé
Sauce tomate (L)         32,1 consommé
Pepperoni (kg)           12,8 consommé
Olives (kg)               9,4 consommé
```

### 📊 Vue "Historique"

```
📊 Historique par Semaines

Semaine 1 - 1 jul - 7 jul
7 jours
Pâte à pizza: 15,2 kg
Mozzarella: 3,8 kg
Sauce tomate: 6,1 L

Semaine 2 - 8 jul - 14 jul  
7 jours
Pâte à pizza: 18,3 kg
Mozzarella: 4,2 kg
Sauce tomate: 7,5 L

Semaine 3 - 15 jul - 21 jul
5 jours (semaine courante)
Pâte à pizza: 17,0 kg
Mozzarella: 4,5 kg
Sauce tomate: 8,2 L
```

## 🎯 Utilité Pratique

### 📈 Pour Votre Business

1. **Prévoir les Commandes** :
   - Moyenne 17 kg pâte/semaine → Commander 20 kg
   - Moyenne 4,5 kg mozzarella/semaine → Commander 6 kg

2. **Contrôler les Coûts** :
   - Identifier les produits les plus consommés
   - Calculer le coût par semaine/mois
   - Optimiser les recettes

3. **Éviter les Ruptures** :
   - Voir les tendances de consommation
   - Anticiper les besoins
   - Ajuster les stocks

### 📊 Analyse des Tendances

**Exemple d'analyse :**
- Semaine 1 : 15,2 kg pâte
- Semaine 2 : 18,3 kg pâte (+20%)
- Semaine 3 : 17,0 kg pâte (-7%)
- **Conclusion** : Consommation stable ~17 kg/semaine

## 🔄 Synchronisation avec le Stock

### ✅ Automatique

- **Basé sur vos saisies** : Stock quotidien
- **Calcul temps réel** : Dès que vous sauvegardez
- **Historique complet** : Toutes vos données conservées

### ✅ Précision

- **Formule exacte** : Stock Initial + Entrées - Stock Final
- **Données réelles** : Vos inventaires quotidiens
- **Pas d'estimation** : Calculs basés sur vos chiffres

## 🚨 Points Importants

### ⚠️ Pour des Statistiques Précises

1. **Saisissez le stock chaque jour** dans l'écran Stock
2. **Valeurs exactes** : Stock Initial, Entrées, Stock Final
3. **Continuité** : Pas de jours manqués
4. **Synchronisation** : Sauvegardez chaque jour

### ⚠️ Interprétation

- **Stock Final élevé** = Moins de consommation ce jour
- **Entrées importantes** = Peut fausser la consommation du jour
- **Jours manqués** = Statistiques incomplètes

## 📋 Workflow Recommandé

### 🗓️ Quotidien

1. **Matin** : Vérifiez le stock initial (synchronisé automatiquement)
2. **Journée** : Notez les entrées (livraisons)
3. **Soir** : Inventaire et saisie du stock final
4. **Sauvegarde** : Cliquez "💾 Sauver" dans Stock

### 📊 Hebdomadaire

1. **Consultez** "📈 Consommation" → "Semaine"
2. **Analysez** la consommation de chaque produit
3. **Planifiez** les commandes de la semaine suivante
4. **Comparez** avec les semaines précédentes

## 🎉 Avantages de la Fonctionnalité

### ✅ Basé sur Vos Données

- **Pas d'estimation** : Calculs sur vos vrais chiffres
- **Historique réel** : Toutes vos saisies conservées
- **Précision** : Formule mathématique exacte

### ✅ Interface Simple

- **3 clics** : Écran principal → Consommation → Onglet
- **Lecture facile** : Produit et quantité consommée
- **Actualisation** : Bouton 🔄 pour recalculer

### ✅ Utilité Immédiate

- **Aide à la décision** : Combien commander ?
- **Contrôle des coûts** : Quels sont vos plus gros postes ?
- **Optimisation** : Réduire le gaspillage

## 🚀 Test de la Fonctionnalité

### 📱 Procédure de Test

1. **Lancez** : `npx expo start`
2. **Connectez-vous** : admin / 123
3. **Saisissez du stock** : Écran "📦 Stock" pendant quelques jours
4. **Consultez** : Écran "📈 Consommation"
5. **Testez les 3 onglets** : Semaine, Mois, Historique

### ✅ Résultat Attendu

- **Calculs automatiques** de consommation
- **Affichage par période** (semaine, mois, historique)
- **Données basées** sur vos saisies de stock
- **Interface claire** et intuitive

**📈 Vos données de stock deviennent maintenant des statistiques utiles pour optimiser votre business !**
