import { Order, Statistics, DailyStats } from '../types';
import { StorageService } from './StorageService';

export class StatisticsService {
  static async calculateStatistics(): Promise<Statistics> {
    const orders = await StorageService.getOrders();
    const today = new Date();
    
    // Statistiques du jour
    const todayStats = this.calculateDailyStats(orders, today);
    
    // Statistiques de la semaine
    const weekStats = this.calculateWeekStats(orders, today);
    
    // Statistiques du mois
    const monthStats = this.calculateMonthStats(orders, today);
    
    // Statistiques globales
    const allTimeStats = this.calculateAllTimeStats(orders);
    
    const statistics: Statistics = {
      today: todayStats,
      thisWeek: weekStats,
      thisMonth: monthStats,
      allTimeStats
    };
    
    await StorageService.saveStatistics(statistics);
    return statistics;
  }

  private static calculateDailyStats(orders: Order[], date: Date): DailyStats {
    const dayOrders = orders.filter(order => 
      this.isSameDay(order.createdAt, date)
    );

    const totalRevenue = dayOrders.reduce((sum, order) => sum + order.totalAmount, 0);
    const popularPizza = this.getMostPopularPizza(dayOrders);
    const averageOrderValue = dayOrders.length > 0 ? totalRevenue / dayOrders.length : 0;

    return {
      date: date.toISOString().split('T')[0],
      totalOrders: dayOrders.length,
      totalRevenue,
      popularPizza,
      averageOrderValue
    };
  }

  private static calculateWeekStats(orders: Order[], endDate: Date): DailyStats[] {
    const weekStats: DailyStats[] = [];
    
    for (let i = 6; i >= 0; i--) {
      const date = new Date(endDate);
      date.setDate(date.getDate() - i);
      weekStats.push(this.calculateDailyStats(orders, date));
    }
    
    return weekStats;
  }

  private static calculateMonthStats(orders: Order[], endDate: Date): DailyStats[] {
    const monthStats: DailyStats[] = [];
    const startOfMonth = new Date(endDate.getFullYear(), endDate.getMonth(), 1);
    
    for (let date = new Date(startOfMonth); date <= endDate; date.setDate(date.getDate() + 1)) {
      monthStats.push(this.calculateDailyStats(orders, new Date(date)));
    }
    
    return monthStats;
  }

  private static calculateAllTimeStats(orders: Order[]) {
    const totalRevenue = orders.reduce((sum, order) => sum + order.totalAmount, 0);
    const mostPopularPizza = this.getMostPopularPizza(orders);
    const averageOrderValue = orders.length > 0 ? totalRevenue / orders.length : 0;

    return {
      totalOrders: orders.length,
      totalRevenue,
      mostPopularPizza,
      averageOrderValue
    };
  }

  private static getMostPopularPizza(orders: Order[]): string {
    const pizzaCount: { [key: string]: number } = {};
    
    orders.forEach(order => {
      order.items.forEach(item => {
        pizzaCount[item.pizzaName] = (pizzaCount[item.pizzaName] || 0) + item.quantity;
      });
    });

    let mostPopular = '';
    let maxCount = 0;
    
    Object.entries(pizzaCount).forEach(([pizza, count]) => {
      if (count > maxCount) {
        maxCount = count;
        mostPopular = pizza;
      }
    });

    return mostPopular || 'Aucune';
  }

  private static isSameDay(date1: Date, date2: Date): boolean {
    return date1.getDate() === date2.getDate() &&
           date1.getMonth() === date2.getMonth() &&
           date1.getFullYear() === date2.getFullYear();
  }

  static async getRealtimeStats(): Promise<Statistics> {
    return await this.calculateStatistics();
  }
}
