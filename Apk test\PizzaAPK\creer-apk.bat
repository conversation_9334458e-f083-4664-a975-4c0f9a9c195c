@echo off
chcp 65001 >nul
cls

echo ==========================================
echo    📱 CRÉER APK STANDALONE - CAISSE 2.0
echo ==========================================
echo.
echo 🎯 Application: Pizza Caisse Manager
echo 📱 Version: 2.0.0 (Build 37)
echo 🏗️ Type: APK Standalone (sans Expo Go)
echo.
echo ✅ Toutes les fonctionnalités incluses:
echo    ✓ Gestion des commandes
echo    ✓ Gestion du stock avec continuité
echo    ✓ Statistiques avec export PDF rouge
echo    ✓ Interface tactile optimisée
echo    ✓ Saisie avec virgule (12,5)
echo    ✓ Calendrier pour dates
echo.
echo ==========================================

cd /d "C:\Users\<USER>\Documents\AppCaisse\Apk test\PizzaAPK"

echo 📂 Répertoire: %CD%
echo.

echo 🔐 ÉTAPE 1: Connexion à Expo
echo 📧 Email: <EMAIL>
echo 🔑 Mot de passe: Themer@10
echo.
npx expo login

if %errorlevel% neq 0 (
    echo ❌ Erreur de connexion
    echo 💡 Vérifiez votre connexion internet
    pause
    exit /b 1
)

echo.
echo ✅ Connexion réussie !
echo.

echo 🏗️ ÉTAPE 2: Création de l'APK
echo ⏳ Cela peut prendre 10-15 minutes...
echo 📱 Création d'un APK installable directement
echo.

echo 🚀 Lancement de la build...
npx eas build --platform android --profile production

if %errorlevel% neq 0 (
    echo.
    echo ⚠️ EAS Build a échoué, essai avec la méthode alternative...
    echo.
    npx expo build:android -t apk
)

echo.
echo ==========================================
echo 🎉 BUILD TERMINÉE !
echo.
echo 📱 Votre APK standalone est prêt !
echo 🔗 Copiez le lien de téléchargement affiché ci-dessus
echo.
echo 📋 Pour installer sur votre téléphone:
echo 1. Téléchargez l'APK avec le lien
echo 2. Activez "Sources inconnues" dans Paramètres > Sécurité
echo 3. Installez l'APK
echo 4. Lancez l'application "Caisse"
echo.
echo 🚀 Plus besoin d'Expo Go !
echo 📱 Application complète et autonome
echo.
echo ==========================================
pause
