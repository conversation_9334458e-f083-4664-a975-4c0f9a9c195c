import AsyncStorage from '@react-native-async-storage/async-storage';
import { StockItem, DailyStock, StockMovement } from '../types';

const STOCK_KEY = 'pizza_stock';
const STOCK_MOVEMENTS_KEY = 'pizza_stock_movements';

export class StockService {
  // Stock initial par défaut
  private static getDefaultStock(): StockItem[] {
    return [
      {
        id: 'pate',
        name: 'Pâte à pizza',
        unit: 'kg',
        stockInitial: 0,
        entrees: 0,
        sorties: 0,
        stockFinal: 0,
        seuilAlerte: 10
      },
      {
        id: 'mozzarella',
        name: 'Mozza<PERSON>',
        unit: 'kg',
        stockInitial: 0,
        entrees: 0,
        sorties: 0,
        stockFinal: 0,
        seuilAlerte: 5
      },
      {
        id: 'sauce',
        name: 'Sauce tomate',
        unit: 'L',
        stockInitial: 0,
        entrees: 0,
        sorties: 0,
        stockFinal: 0,
        seuilAlerte: 3
      },
      {
        id: 'pepperoni',
        name: '<PERSON><PERSON>',
        unit: 'kg',
        stockInitial: 0,
        entrees: 0,
        sorties: 0,
        stockFinal: 0,
        seuilAlerte: 2
      },
      {
        id: 'basilic',
        name: 'Basilic',
        unit: 'kg',
        stockInitial: 0,
        entrees: 0,
        sorties: 0,
        stockFinal: 0,
        seuilAlerte: 0.5
      },
      {
        id: 'olives',
        name: 'Olives',
        unit: 'kg',
        stockInitial: 0,
        entrees: 0,
        sorties: 0,
        stockFinal: 0,
        seuilAlerte: 1
      }
    ];
  }

  // Obtenir le stock du jour
  static async getTodayStock(): Promise<DailyStock> {
    const today = new Date().toISOString().split('T')[0];
    return this.getStockForDate(today);
  }

  // Obtenir le stock pour une date spécifique
  static async getStockForDate(dateStr: string): Promise<DailyStock> {
    try {
      const stockData = await AsyncStorage.getItem(STOCK_KEY);

      if (stockData) {
        const allStock: DailyStock[] = JSON.parse(stockData);
        const targetStock = allStock.find(stock => stock.date === dateStr);

        if (targetStock) {
          return targetStock;
        }
      }

      // Si pas de stock pour cette date, créer à partir du jour précédent
      return await this.initializeStockForDate(dateStr);
    } catch (error) {
      console.error('Erreur lors de la récupération du stock:', error);
      return {
        date: dateStr,
        items: this.getDefaultStock()
      };
    }
  }

  // Initialiser le stock du jour à partir du stock final d'hier
  private static async initializeTodayStock(): Promise<DailyStock> {
    const today = new Date().toISOString().split('T')[0];
    return this.initializeStockForDate(today);
  }

  // Initialiser le stock pour une date spécifique
  private static async initializeStockForDate(dateStr: string): Promise<DailyStock> {
    try {
      const targetDate = new Date(dateStr);
      const previousDate = new Date(targetDate);
      previousDate.setDate(previousDate.getDate() - 1);
      const previousDateStr = previousDate.toISOString().split('T')[0];

      console.log(`🔄 Initialisation stock pour ${dateStr}, recherche du stock de ${previousDateStr}`);

      const stockData = await AsyncStorage.getItem(STOCK_KEY);

      if (stockData) {
        const allStock: DailyStock[] = JSON.parse(stockData);
        const previousStock = allStock.find(stock => stock.date === previousDateStr);

        if (previousStock) {
          console.log(`✅ Stock trouvé pour ${previousDateStr}, copie du stock final vers stock initial`);
          // Le stock initial de cette date = stock final du jour précédent
          const newStock: DailyStock = {
            date: dateStr,
            items: previousStock.items.map(item => ({
              ...item,
              stockInitial: item.stockFinal, // ✅ Stock final J-1 = Stock initial J
              entrees: 0,
              sorties: 0,
              stockFinal: item.stockFinal // Stock final = stock initial si pas de mouvement
            }))
          };

          await this.saveStockForDate(newStock);
          console.log(`💾 Stock initialisé pour ${dateStr}`);
          return newStock;
        } else {
          console.log(`⚠️ Pas de stock trouvé pour ${previousDateStr}`);
        }
      }

      // Premier jour ou pas de données précédentes, utiliser le stock par défaut
      console.log(`🆕 Création du stock par défaut pour ${dateStr}`);
      const defaultStock: DailyStock = {
        date: dateStr,
        items: this.getDefaultStock()
      };

      await this.saveStockForDate(defaultStock);
      return defaultStock;
    } catch (error) {
      console.error('Erreur lors de l\'initialisation du stock:', error);
      return {
        date: dateStr,
        items: this.getDefaultStock()
      };
    }
  }

  // Sauvegarder le stock du jour
  static async saveTodayStock(todayStock: DailyStock): Promise<void> {
    return this.saveStockForDate(todayStock);
  }

  // Sauvegarder le stock pour une date spécifique
  static async saveStockForDate(stockData: DailyStock): Promise<void> {
    try {
      const allStockData = await AsyncStorage.getItem(STOCK_KEY);
      let allStock: DailyStock[] = [];

      if (allStockData) {
        allStock = JSON.parse(allStockData);
      }

      // Remplacer ou ajouter le stock pour cette date
      const existingIndex = allStock.findIndex(stock => stock.date === stockData.date);
      if (existingIndex >= 0) {
        allStock[existingIndex] = stockData;
      } else {
        allStock.push(stockData);
      }

      await AsyncStorage.setItem(STOCK_KEY, JSON.stringify(allStock));
      console.log(`💾 Stock sauvegardé pour ${stockData.date}`);

      // Mettre à jour automatiquement le stock initial du jour suivant
      await this.updateNextDayStock(stockData);
    } catch (error) {
      console.error('Erreur lors de la sauvegarde du stock:', error);
      throw error;
    }
  }

  // Mettre à jour le stock initial du jour suivant
  private static async updateNextDayStock(currentStock: DailyStock): Promise<void> {
    try {
      const currentDate = new Date(currentStock.date);
      const nextDate = new Date(currentDate);
      nextDate.setDate(currentDate.getDate() + 1);
      const nextDateStr = nextDate.toISOString().split('T')[0];

      const allStockData = await AsyncStorage.getItem(STOCK_KEY);
      if (allStockData) {
        const allStock: DailyStock[] = JSON.parse(allStockData);
        const nextDayStock = allStock.find(stock => stock.date === nextDateStr);

        if (nextDayStock) {
          console.log(`🔄 Mise à jour du stock initial de ${nextDateStr}`);
          // Mettre à jour le stock initial du jour suivant avec le stock final d'aujourd'hui
          nextDayStock.items = nextDayStock.items.map(nextItem => {
            const currentItem = currentStock.items.find(item => item.id === nextItem.id);
            if (currentItem) {
              return {
                ...nextItem,
                stockInitial: currentItem.stockFinal, // ✅ Stock final J = Stock initial J+1
                stockFinal: currentItem.stockFinal + nextItem.entrees - nextItem.sorties // Recalculer
              };
            }
            return nextItem;
          });

          // Sauvegarder sans déclencher une nouvelle mise à jour (éviter la récursion)
          const existingIndex = allStock.findIndex(stock => stock.date === nextDateStr);
          if (existingIndex >= 0) {
            allStock[existingIndex] = nextDayStock;
            await AsyncStorage.setItem(STOCK_KEY, JSON.stringify(allStock));
            console.log(`✅ Stock initial de ${nextDateStr} mis à jour`);
          }
        }
      }
    } catch (error) {
      console.error('Erreur lors de la mise à jour du stock suivant:', error);
    }
  }

  // Fonction simplifiée - pas besoin de fonctions complexes
  // La saisie se fait directement dans l'interface

  // Obtenir les articles en alerte
  static async getAlertItems(): Promise<StockItem[]> {
    try {
      const todayStock = await this.getTodayStock();
      return todayStock.items.filter(item => item.stockFinal <= item.seuilAlerte);
    } catch (error) {
      console.error('Erreur lors de la récupération des alertes:', error);
      return [];
    }
  }

  // Réinitialiser le stock (pour tests)
  static async resetStock(): Promise<void> {
    try {
      await AsyncStorage.removeItem(STOCK_KEY);
      await AsyncStorage.removeItem(STOCK_MOVEMENTS_KEY);
    } catch (error) {
      console.error('Erreur lors de la réinitialisation:', error);
      throw error;
    }
  }
}
