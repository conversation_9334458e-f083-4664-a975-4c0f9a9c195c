import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Alert,
} from 'react-native';
import { Colors, FontSizes, Spacing, BorderRadius, Shadows } from '../styles/theme';
import { OrderDetail } from '../types';

interface OrderDetailScreenProps {
  navigation: any;
  route: {
    params: {
      order: OrderDetail;
      onOrderCancelled?: (orderId: string, orderNumber: string) => void;
    };
  };
}

export const OrderDetailScreen: React.FC<OrderDetailScreenProps> = ({ navigation, route }) => {
  const { order, onOrderCancelled } = route.params;

  const handleCancelOrder = () => {
    Alert.alert(
      'Supprimer la commande',
      `Êtes-vous sûr de vouloir supprimer définitivement la commande #${order.orderNumber} ?\n\nCette action est irréversible et la commande sera supprimée de toutes les statistiques.`,
      [
        { text: 'Non', style: 'cancel' },
        {
          text: 'Oui, supprimer',
          style: 'destructive',
          onPress: async () => {
            // Appeler le callback pour supprimer la commande
            if (onOrderCancelled) {
              await onOrderCancelled(order.id, order.orderNumber);
            }

            Alert.alert(
              'Commande supprimée',
              `La commande #${order.orderNumber} a été supprimée définitivement.`,
              [
                {
                  text: 'OK',
                  onPress: () => navigation.goBack()
                }
              ]
            );
          }
        }
      ]
    );
  };



  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity onPress={() => navigation.goBack()} style={styles.backButton}>
          <Text style={styles.backButtonText}>← Retour</Text>
        </TouchableOpacity>
        <Text style={styles.title}>Commande #{order.orderNumber}</Text>
      </View>

      <ScrollView style={styles.content}>
        {/* Order Date */}
        <View style={styles.section}>
          <View style={styles.dateContainer}>
            <Text style={styles.dateIcon}>📅</Text>
            <View style={styles.dateInfo}>
              <Text style={styles.orderDate}>
                {order.formattedDate} à {order.formattedTime}
              </Text>
            </View>
          </View>
        </View>

        {/* Customer Information */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>👤 Informations Client</Text>
          <View style={styles.customerInfo}>
            <Text style={styles.customerName}>{order.customer.name}</Text>
            <Text style={styles.customerDetail}>📞 {order.customer.phone}</Text>
            {order.customer.address && (
              <Text style={styles.customerDetail}>📍 {order.customer.address}</Text>
            )}
          </View>
        </View>

        {/* Order Items */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>🍕 Articles Commandés</Text>
          {order.items.map((item, index) => (
            <View key={`${item.id}-${index}`} style={styles.orderItem}>
              <View style={styles.itemInfo}>
                <Text style={styles.itemName}>{item.name}</Text>
                {item.isOffer && item.offerItems && (
                  <View style={styles.offerDetails}>
                    {item.offerItems.map((offerItem, idx) => (
                      <Text key={idx} style={styles.offerItemText}>
                        • {offerItem.name}: {offerItem.choice}
                      </Text>
                    ))}
                  </View>
                )}
                {item.specialInstructions && (
                  <Text style={styles.specialInstructions}>
                    💬 {item.specialInstructions}
                  </Text>
                )}
              </View>
              <View style={styles.itemPricing}>
                <Text style={styles.itemQuantity}>x{item.quantity}</Text>
                <Text style={styles.itemPrice}>{item.totalPrice.toFixed(2)} DT</Text>
              </View>
            </View>
          ))}
        </View>

        {/* Payment Information */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>💳 Détails de Paiement</Text>
          
          <View style={styles.paymentRow}>
            <Text style={styles.paymentLabel}>Sous-total</Text>
            <Text style={styles.paymentValue}>{order.subtotalAmount.toFixed(2)} DT</Text>
          </View>
          
          {order.deliveryFee > 0 && (
            <View style={styles.paymentRow}>
              <Text style={styles.paymentLabel}>Frais de livraison</Text>
              <Text style={styles.paymentValue}>{order.deliveryFee.toFixed(2)} DT</Text>
            </View>
          )}
          
          <View style={[styles.paymentRow, styles.totalRow]}>
            <Text style={styles.totalLabel}>TOTAL</Text>
            <Text style={styles.totalValue}>{order.totalAmount.toFixed(2)} DT</Text>
          </View>
          
          <View style={styles.paymentMethodContainer}>
            <Text style={styles.paymentMethodLabel}>Méthode de paiement:</Text>
            <Text style={styles.paymentMethodValue}>{order.paymentMethod || 'Espèces'}</Text>
          </View>
        </View>

        {/* Special Instructions */}
        {order.specialInstructions && (
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>📝 Instructions Spéciales</Text>
            <Text style={styles.instructionsText}>{order.specialInstructions}</Text>
          </View>
        )}

        {/* Order Actions */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>⚡ Actions</Text>
          <View style={styles.actionButtons}>
            <TouchableOpacity
              style={styles.printButton}
              onPress={() => {
                // Logique d'impression du ticket
                Alert.alert('Impression', `Ticket de la commande #${order.orderNumber} envoyé à l'imprimante.`);
              }}
            >
              <Text style={styles.printButtonIcon}>🖨️</Text>
              <Text style={styles.printButtonText}>Imprimer Ticket</Text>
            </TouchableOpacity>

            {/* Bouton de suppression */}
            <TouchableOpacity
              style={styles.cancelButton}
              onPress={handleCancelOrder}
            >
              <Text style={styles.cancelButtonIcon}>🗑️</Text>
              <Text style={styles.cancelButtonText}>Supprimer Commande</Text>
            </TouchableOpacity>
          </View>
        </View>
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.backgroundSolid,
  },
  header: {
    backgroundColor: Colors.secondary,
    paddingTop: 50,
    paddingBottom: 15,
    paddingHorizontal: 16,
    flexDirection: 'row',
    alignItems: 'center',
    borderBottomLeftRadius: 20,
    borderBottomRightRadius: 20,
    ...Shadows.medium,
  },
  backButton: {
    marginRight: 10,
  },
  backButtonText: {
    color: Colors.textOnDark,
    fontSize: FontSizes.md,
    fontWeight: '600',
  },
  title: {
    fontSize: FontSizes.xl,
    fontWeight: 'bold',
    color: Colors.textOnDark,
    flex: 1,
  },
  content: {
    flex: 1,
    padding: Spacing.lg,
  },
  section: {
    backgroundColor: Colors.surface,
    borderRadius: BorderRadius.large,
    padding: Spacing.lg,
    marginBottom: Spacing.lg,
    ...Shadows.medium,
  },
  sectionTitle: {
    fontSize: FontSizes.lg,
    fontWeight: 'bold',
    color: Colors.textPrimary,
    marginBottom: Spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: Colors.borderLight,
    paddingBottom: Spacing.sm,
  },
  dateContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  dateIcon: {
    fontSize: 40,
    marginRight: Spacing.md,
  },
  dateInfo: {
    flex: 1,
  },
  orderDate: {
    fontSize: FontSizes.lg,
    fontWeight: 'bold',
    color: Colors.textPrimary,
  },
  customerInfo: {
    paddingLeft: Spacing.sm,
  },
  customerName: {
    fontSize: FontSizes.lg,
    fontWeight: 'bold',
    color: Colors.textPrimary,
    marginBottom: Spacing.sm,
  },
  customerDetail: {
    fontSize: FontSizes.md,
    color: Colors.textSecondary,
    marginBottom: Spacing.xs,
  },
  orderItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    paddingVertical: Spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: Colors.borderLight,
  },
  itemInfo: {
    flex: 1,
    marginRight: Spacing.md,
  },
  itemName: {
    fontSize: FontSizes.md,
    fontWeight: '700',
    color: Colors.textPrimary,
    marginBottom: Spacing.xs,
  },
  offerDetails: {
    marginTop: Spacing.xs,
    marginBottom: Spacing.xs,
  },
  offerItemText: {
    fontSize: FontSizes.sm,
    color: Colors.textSecondary,
    marginBottom: 2,
    paddingLeft: Spacing.sm,
  },
  specialInstructions: {
    fontSize: FontSizes.sm,
    color: Colors.accent,
    fontStyle: 'italic',
    marginTop: Spacing.xs,
  },
  itemPricing: {
    alignItems: 'flex-end',
  },
  itemQuantity: {
    fontSize: FontSizes.sm,
    color: Colors.textSecondary,
    marginBottom: Spacing.xs,
  },
  itemPrice: {
    fontSize: FontSizes.md,
    fontWeight: 'bold',
    color: Colors.primary,
  },
  paymentRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: Spacing.sm,
  },
  paymentLabel: {
    fontSize: FontSizes.md,
    color: Colors.textSecondary,
  },
  paymentValue: {
    fontSize: FontSizes.md,
    fontWeight: '600',
    color: Colors.textPrimary,
  },
  totalRow: {
    borderTopWidth: 2,
    borderTopColor: Colors.primary,
    marginTop: Spacing.sm,
    paddingTop: Spacing.md,
    backgroundColor: Colors.light,
    marginHorizontal: -Spacing.md,
    paddingHorizontal: Spacing.md,
    borderRadius: BorderRadius.medium,
  },
  totalLabel: {
    fontSize: FontSizes.lg,
    fontWeight: 'bold',
    color: Colors.textPrimary,
  },
  totalValue: {
    fontSize: FontSizes.xl,
    fontWeight: 'bold',
    color: Colors.primary,
  },
  paymentMethodContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: Spacing.md,
    paddingTop: Spacing.md,
    borderTopWidth: 1,
    borderTopColor: Colors.borderLight,
  },
  paymentMethodLabel: {
    fontSize: FontSizes.md,
    color: Colors.textSecondary,
    marginRight: Spacing.sm,
  },
  paymentMethodValue: {
    fontSize: FontSizes.md,
    fontWeight: '600',
    color: Colors.textPrimary,
  },
  instructionsText: {
    fontSize: FontSizes.md,
    color: Colors.textPrimary,
    lineHeight: 22,
    backgroundColor: Colors.light,
    padding: Spacing.md,
    borderRadius: BorderRadius.medium,
    borderLeftWidth: 4,
    borderLeftColor: Colors.accent,
  },
  actionButtons: {
    alignItems: 'center',
    gap: Spacing.lg,
  },
  printButton: {
    backgroundColor: Colors.primary,
    borderRadius: BorderRadius.large,
    padding: Spacing.lg,
    alignItems: 'center',
    minWidth: 200,
    ...Shadows.medium,
  },
  printButtonIcon: {
    fontSize: 32,
    marginBottom: Spacing.sm,
  },
  printButtonText: {
    fontSize: FontSizes.md,
    fontWeight: 'bold',
    color: Colors.textOnPrimary,
    textAlign: 'center',
  },
  cancelButton: {
    backgroundColor: Colors.danger,
    borderRadius: BorderRadius.large,
    padding: Spacing.lg,
    alignItems: 'center',
    minWidth: 200,
    ...Shadows.medium,
  },
  cancelButtonIcon: {
    fontSize: 32,
    marginBottom: Spacing.sm,
  },
  cancelButtonText: {
    fontSize: FontSizes.md,
    fontWeight: 'bold',
    color: Colors.textOnPrimary,
    textAlign: 'center',
  },
});
