import * as Print from 'expo-print';
import * as Sharing from 'expo-sharing';
import { Order } from '../types';

export class PDFService {
  static async generateDailyStatsPDF(orders: Order[], selectedDate: string): Promise<void> {
    try {
      // Filtrer les commandes pour la date sélectionnée
      const targetDate = new Date(selectedDate);
      const dayOrders = orders.filter(order => {
        const orderDate = new Date(order.createdAt);
        return orderDate.toDateString() === targetDate.toDateString();
      });

      // Calculer les statistiques
      const totalOrders = dayOrders.length;
      const totalRevenue = dayOrders.reduce((sum, order) => sum + order.totalAmount, 0);
      const dateStr = targetDate.toLocaleDateString('fr-FR', {
        day: '2-digit',
        month: '2-digit',
        year: 'numeric'
      });

      // Générer le contenu HTML
      const htmlContent = this.generateHTMLContent(dayOrders, totalOrders, totalRevenue, dateStr);

      // Générer le PDF avec Expo Print
      const { uri } = await Print.printToFileAsync({
        html: htmlContent,
        base64: false,
      });

      console.log('📄 PDF généré:', uri);

      // Partager le PDF
      if (await Sharing.isAvailableAsync()) {
        await Sharing.shareAsync(uri, {
          mimeType: 'application/pdf',
          dialogTitle: `Statistiques du ${dateStr}`,
          UTI: 'com.adobe.pdf',
        });
      } else {
        throw new Error('Le partage n\'est pas disponible sur cet appareil');
      }
    } catch (error) {
      console.error('❌ Erreur lors de la génération du PDF:', error);
      throw error;
    }
  }

  private static generateHTMLContent(
    orders: Order[],
    totalOrders: number,
    totalRevenue: number,
    dateStr: string
  ): string {
    const ordersList = orders.map((order, index) => {
      return `
        <div class="order-item">
          <span class="order-number">${index + 1}. Commande #${order.id.slice(-6)}</span>
          <span class="order-amount">${order.totalAmount.toFixed(2)} DT</span>
        </div>
      `;
    }).join('');

    // Récapitulatif en bas de liste
    const summary = orders.length > 0 ? `
      <div class="list-summary">
        <div class="summary-line">
          <span class="summary-label">Total Commandes:</span>
          <span class="summary-value">${totalOrders}</span>
        </div>
        <div class="summary-line">
          <span class="summary-label">Total Montant:</span>
          <span class="summary-value">${totalRevenue.toFixed(2)} DT</span>
        </div>
      </div>
    ` : '';

    return `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="UTF-8">
        <title>Statistiques du ${dateStr}</title>
        <style>
          body {
            font-family: 'Georgia', serif;
            margin: 0;
            padding: 30px;
            color: #1a1a1a;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            line-height: 1.8;
          }
          .container {
            max-width: 850px;
            margin: 0 auto;
            background: linear-gradient(145deg, #ffffff 0%, #f8f9fa 100%);
            padding: 40px;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
            border: 1px solid #e0e6ed;
            position: relative;
            overflow: hidden;
          }
          .container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 5px;
            background: linear-gradient(90deg, #dc2626 0%, #ef4444 50%, #dc2626 100%);
          }
          .header {
            text-align: center;
            margin-bottom: 35px;
            position: relative;
          }
          .company-name {
            font-size: 36px;
            font-weight: bold;
            color: #dc2626;
            margin-bottom: 10px;
            letter-spacing: 2px;
            text-shadow: 2px 2px 4px rgba(220, 38, 38, 0.2);
          }
          .date {
            font-size: 24px;
            font-weight: 600;
            color: #374151;
            margin-bottom: 20px;
            padding: 10px 20px;
            background: linear-gradient(135deg, #fef2f2 0%, #fee2e2 100%);
            border-radius: 15px;
            border: 2px solid #fecaca;
            display: inline-block;
          }
          .stats-container {
            display: flex;
            justify-content: space-around;
            margin-bottom: 35px;
            background: linear-gradient(135deg, #dc2626 0%, #b91c1c 50%, #991b1b 100%);
            padding: 30px;
            border-radius: 20px;
            box-shadow: 0 10px 25px rgba(220, 38, 38, 0.3);
            position: relative;
            overflow: hidden;
          }
          .stats-container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="50" cy="50" r="2" fill="rgba(255,255,255,0.1)"/></svg>') repeat;
            opacity: 0.3;
          }
          .stat-box {
            text-align: center;
            flex: 1;
            position: relative;
            z-index: 1;
          }
          .stat-number {
            font-size: 64px;
            font-weight: bold;
            display: block;
            margin-bottom: 8px;
            color: #ffffff;
            text-shadow: 3px 3px 6px rgba(0,0,0,0.4);
            font-family: 'Arial Black', sans-serif;
          }
          .stat-label {
            font-size: 16px;
            font-weight: 700;
            text-transform: uppercase;
            letter-spacing: 2px;
            color: #fef2f2;
            text-shadow: 1px 1px 2px rgba(0,0,0,0.3);
          }
          .divider {
            width: 3px;
            background: linear-gradient(180deg, transparent 0%, rgba(255,255,255,0.6) 50%, transparent 100%);
            margin: 0 20px;
            border-radius: 2px;
          }
          .orders-section {
            margin-top: 25px;
            flex: 1;
          }
          .section-title {
            font-size: 22px;
            font-weight: bold;
            color: #dc2626;
            margin-bottom: 20px;
            text-align: center;
            position: relative;
            text-transform: uppercase;
            letter-spacing: 1px;
          }
          .section-title:after {
            content: '';
            position: absolute;
            bottom: -8px;
            left: 50%;
            transform: translateX(-50%);
            width: 80px;
            height: 3px;
            background: linear-gradient(90deg, #dc2626 0%, #ef4444 50%, #dc2626 100%);
            border-radius: 2px;
          }
          .orders-list {
            background: linear-gradient(135deg, #fef2f2 0%, #fee2e2 100%);
            padding: 15px;
            border-radius: 15px;
            border: 2px solid #fecaca;
            max-height: 450px;
            overflow-y: auto;
            box-shadow: inset 0 2px 4px rgba(220, 38, 38, 0.1);
          }
          .order-item {
            font-size: 12px;
            margin-bottom: 2px;
            padding: 6px 10px;
            background: linear-gradient(135deg, #ffffff 0%, #f9fafb 100%);
            border-radius: 6px;
            border-left: 3px solid #dc2626;
            box-shadow: 0 2px 4px rgba(220, 38, 38, 0.1);
            display: flex;
            justify-content: space-between;
            align-items: center;
            position: relative;
            overflow: hidden;
          }
          .order-item::before {
            content: '';
            position: absolute;
            top: 0;
            right: 0;
            width: 30px;
            height: 100%;
            background: linear-gradient(90deg, transparent 0%, rgba(220, 38, 38, 0.03) 100%);
          }
          .order-number {
            font-weight: 600;
            color: #374151;
            font-size: 11px;
            position: relative;
            z-index: 1;
          }
          .order-amount {
            font-weight: bold;
            color: #dc2626;
            font-size: 12px;
            text-shadow: 1px 1px 2px rgba(220, 38, 38, 0.2);
            position: relative;
            z-index: 1;
          }
          .no-orders {
            text-align: center;
            font-style: italic;
            color: #dc2626;
            font-size: 22px;
            padding: 50px;
            background: linear-gradient(135deg, #fef2f2 0%, #fee2e2 100%);
            border-radius: 15px;
            border: 2px dashed #fca5a5;
            font-weight: 600;
          }
          .summary-section {
            margin-top: 25px;
            background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%);
            padding: 20px;
            border-radius: 15px;
            box-shadow: 0 8px 16px rgba(220, 38, 38, 0.2);
          }
          .summary-title {
            font-size: 20px;
            font-weight: bold;
            color: #ffffff;
            text-align: center;
            margin-bottom: 15px;
            letter-spacing: 2px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
          }
          .summary-content {
            display: flex;
            justify-content: space-around;
            align-items: center;
          }
          .summary-item {
            text-align: center;
            color: #ffffff;
          }
          .summary-label {
            display: block;
            font-size: 14px;
            font-weight: 600;
            margin-bottom: 5px;
            text-transform: uppercase;
            letter-spacing: 1px;
          }
          .summary-value {
            display: block;
            font-size: 28px;
            font-weight: bold;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.4);
            font-family: 'Arial Black', sans-serif;
          }
          .footer {
            margin-top: 20px;
            text-align: center;
            font-size: 12px;
            color: #6b7280;
            border-top: 1px solid #f3f4f6;
            padding-top: 10px;
            font-style: italic;
          }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <div class="company-name">🍕 CAISSE</div>
            <div class="date">${dateStr}</div>
          </div>

          <div class="stats-container">
            <div class="stat-box">
              <span class="stat-number">${totalOrders}</span>
              <div class="stat-label">Commandes</div>
            </div>
            <div class="divider"></div>
            <div class="stat-box">
              <span class="stat-number">${totalRevenue.toFixed(2)} DT</span>
              <div class="stat-label">Total Dinars</div>
            </div>
          </div>

          <div class="orders-section">
            <div class="section-title">Détail des Commandes</div>
            <div class="orders-list">
              ${ordersList || '<div class="no-orders">Aucune commande pour cette date</div>'}
            </div>
          </div>

          <div class="summary-section">
            <div class="summary-title">RÉCAPITULATIF</div>
            <div class="summary-content">
              <div class="summary-item">
                <span class="summary-label">Total Commandes:</span>
                <span class="summary-value">${totalOrders}</span>
              </div>
              <div class="summary-item">
                <span class="summary-label">Montant Total:</span>
                <span class="summary-value">${totalRevenue.toFixed(2)} DT</span>
              </div>
            </div>
          </div>

          <div class="footer">
            Rapport généré le ${new Date().toLocaleDateString('fr-FR')} à ${new Date().toLocaleTimeString('fr-FR')}
          </div>
        </div>
      </body>
      </html>
    `;
  }
}
