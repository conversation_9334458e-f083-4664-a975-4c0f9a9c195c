import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  Alert,
} from 'react-native';
import { PizzaCard } from '../components/PizzaCard';
import { PIZZAS } from '../data/pizzas';
import { OrderItem } from '../types';

interface PizzaListScreenProps {
  navigation: any;
}

export const PizzaListScreen: React.FC<PizzaListScreenProps> = ({ navigation }) => {
  const [cart, setCart] = useState<OrderItem[]>([]);
  const [cartTotal, setCartTotal] = useState(0);

  useEffect(() => {
    const total = cart.reduce((sum, item) => sum + item.totalPrice, 0);
    setCartTotal(total);
  }, [cart]);

  const handleAddToCart = (newItem: OrderItem) => {
    setCart(prevCart => {
      // Vérifier si l'item existe déjà (même pizza, même taille)
      const existingItemIndex = prevCart.findIndex(
        item => item.pizzaId === newItem.pizzaId && item.size === newItem.size
      );

      if (existingItemIndex >= 0) {
        // Mettre à jour la quantité de l'item existant
        const updatedCart = [...prevCart];
        const existingItem = updatedCart[existingItemIndex];
        existingItem.quantity += newItem.quantity;
        existingItem.totalPrice = existingItem.unitPrice * existingItem.quantity;
        return updatedCart;
      } else {
        // Ajouter le nouvel item
        return [...prevCart, newItem];
      }
    });
  };

  const handleViewCart = () => {
    if (cart.length === 0) {
      Alert.alert('Panier vide', 'Ajoutez des pizzas à votre panier avant de continuer.');
      return;
    }
    
    navigation.navigate('OrderForm', { cartItems: cart });
  };

  const handleViewStats = () => {
    navigation.navigate('Statistics');
  };

  const clearCart = () => {
    Alert.alert(
      'Vider le panier',
      'Êtes-vous sûr de vouloir vider le panier ?',
      [
        { text: 'Annuler', style: 'cancel' },
        { 
          text: 'Vider', 
          style: 'destructive',
          onPress: () => setCart([])
        }
      ]
    );
  };

  const renderPizzaCard = ({ item }: { item: any }) => (
    <PizzaCard pizza={item} onAddToOrder={handleAddToCart} />
  );

  return (
    <View style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <Text style={styles.title}>Pizza Caisse Manager</Text>
        <TouchableOpacity style={styles.statsButton} onPress={handleViewStats}>
          <Text style={styles.statsButtonText}>📊 Stats</Text>
        </TouchableOpacity>
      </View>

      {/* Liste des pizzas */}
      <FlatList
        data={PIZZAS}
        renderItem={renderPizzaCard}
        keyExtractor={(item) => item.id}
        showsVerticalScrollIndicator={false}
        contentContainerStyle={styles.listContainer}
      />

      {/* Panier flottant */}
      {cart.length > 0 && (
        <View style={styles.cartContainer}>
          <View style={styles.cartInfo}>
            <Text style={styles.cartText}>
              {cart.length} article{cart.length > 1 ? 's' : ''} - {cartTotal.toFixed(2)}€
            </Text>
            <TouchableOpacity onPress={clearCart}>
              <Text style={styles.clearCartText}>🗑️</Text>
            </TouchableOpacity>
          </View>
          <TouchableOpacity style={styles.cartButton} onPress={handleViewCart}>
            <Text style={styles.cartButtonText}>Voir le panier</Text>
          </TouchableOpacity>
        </View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  header: {
    backgroundColor: '#2c3e50',
    paddingTop: 50,
    paddingBottom: 20,
    paddingHorizontal: 16,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#fff',
  },
  statsButton: {
    backgroundColor: '#3498db',
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 6,
  },
  statsButtonText: {
    color: '#fff',
    fontSize: 14,
    fontWeight: '600',
  },
  listContainer: {
    paddingBottom: 100,
  },
  cartContainer: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    backgroundColor: '#fff',
    padding: 16,
    borderTopWidth: 1,
    borderTopColor: '#ddd',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: -2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 5,
  },
  cartInfo: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  cartText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
  },
  clearCartText: {
    fontSize: 18,
    padding: 4,
  },
  cartButton: {
    backgroundColor: '#e74c3c',
    borderRadius: 8,
    paddingVertical: 12,
    alignItems: 'center',
  },
  cartButtonText: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#fff',
  },
});
