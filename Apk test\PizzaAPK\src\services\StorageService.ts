import AsyncStorage from '@react-native-async-storage/async-storage';
import { Order, Statistics } from '../types';

const ORDERS_KEY = 'pizza_orders';
const STATS_KEY = 'pizza_stats';

export class StorageService {
  // Gestion des commandes
  static async saveOrder(order: Order): Promise<void> {
    try {
      const existingOrders = await this.getOrders();
      const updatedOrders = [...existingOrders, order];
      await AsyncStorage.setItem(ORDERS_KEY, JSON.stringify(updatedOrders));
    } catch (error) {
      console.error('Erreur lors de la sauvegarde de la commande:', error);
      throw error;
    }
  }

  static async getOrders(): Promise<Order[]> {
    try {
      const ordersJson = await AsyncStorage.getItem(ORDERS_KEY);
      if (ordersJson) {
        const orders = JSON.parse(ordersJson);
        // Convertir les dates string en objets Date
        return orders.map((order: any) => ({
          ...order,
          createdAt: new Date(order.createdAt),
          updatedAt: new Date(order.updatedAt)
        }));
      }
      return [];
    } catch (error) {
      console.error('Erreur lors de la récupération des commandes:', error);
      return [];
    }
  }

  static async updateOrderStatus(orderId: string, status: Order['status']): Promise<void> {
    try {
      const orders = await this.getOrders();
      const updatedOrders = orders.map(order =>
        order.id === orderId
          ? { ...order, status }
          : order
      );
      await AsyncStorage.setItem(ORDERS_KEY, JSON.stringify(updatedOrders));
      console.log(`✅ Statut de la commande ${orderId} mis à jour: ${status}`);
    } catch (error) {
      console.error('Erreur lors de la mise à jour du statut:', error);
      throw error;
    }
  }

  static async deleteOrder(orderId: string): Promise<void> {
    try {
      const orders = await this.getOrders();
      const filteredOrders = orders.filter(order => order.id !== orderId);
      await AsyncStorage.setItem(ORDERS_KEY, JSON.stringify(filteredOrders));
      console.log(`✅ Commande ${orderId} supprimée avec succès`);
    } catch (error) {
      console.error('Erreur lors de la suppression de la commande:', error);
      throw error;
    }
  }

  // Gestion des statistiques
  static async saveStatistics(stats: Statistics): Promise<void> {
    try {
      await AsyncStorage.setItem(STATS_KEY, JSON.stringify(stats));
    } catch (error) {
      console.error('Erreur lors de la sauvegarde des statistiques:', error);
      throw error;
    }
  }

  static async getStatistics(): Promise<Statistics | null> {
    try {
      const statsJson = await AsyncStorage.getItem(STATS_KEY);
      return statsJson ? JSON.parse(statsJson) : null;
    } catch (error) {
      console.error('Erreur lors de la récupération des statistiques:', error);
      return null;
    }
  }

  // Utilitaires
  static async clearAllData(): Promise<void> {
    try {
      await AsyncStorage.multiRemove([ORDERS_KEY, STATS_KEY]);
    } catch (error) {
      console.error('Erreur lors de la suppression des données:', error);
      throw error;
    }
  }
}
