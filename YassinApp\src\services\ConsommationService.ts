import { StockService, DailyStock } from './StockService';

export interface ConsommationItem {
  id: string;
  name: string;
  unit: string;
  consommation: number;
  stockInitialMoyen: number;
  stockFinalMoyen: number;
  entreesMoyennes: number;
}

export interface ConsommationPeriode {
  dateDebut: string;
  dateFin: string;
  nombreJours: number;
  items: ConsommationItem[];
  totalConsommation: number;
}

export class ConsommationService {
  
  // Calculer la consommation pour une période
  static async getConsommationPeriode(dateDebut: string, dateFin: string): Promise<ConsommationPeriode> {
    try {
      const stocks = await StockService.getStockHistory();
      
      // Filtrer les stocks dans la période
      const stocksPeriode = stocks.filter(stock => 
        stock.date >= dateDebut && stock.date <= dateFin
      ).sort((a, b) => a.date.localeCompare(b.date));

      if (stocksPeriode.length === 0) {
        return {
          dateDebut,
          dateFin,
          nombreJours: 0,
          items: [],
          totalConsommation: 0
        };
      }

      // Calculer la consommation pour chaque produit
      const consommationItems: ConsommationItem[] = [];
      
      // Obtenir la liste des produits du premier jour
      const produitsReference = stocksPeriode[0].items;
      
      for (const produit of produitsReference) {
        const stocksProduitsFiltered = stocksPeriode
          .map(stock => stock.items.find(item => item.id === produit.id))
          .filter(item => item !== undefined);

        if (stocksProduitsFiltered.length > 0) {
          // Calculer la consommation totale
          let consommationTotale = 0;
          let stockInitialTotal = 0;
          let stockFinalTotal = 0;
          let entreesTotal = 0;

          for (let i = 0; i < stocksProduitsFiltered.length; i++) {
            const item = stocksProduitsFiltered[i];
            stockInitialTotal += item.stockInitial;
            stockFinalTotal += item.stockFinal;
            entreesTotal += item.entrees;

            // Consommation = Stock Initial + Entrées - Stock Final
            const consommationJour = item.stockInitial + item.entrees - item.stockFinal;
            consommationTotale += consommationJour;
          }

          consommationItems.push({
            id: produit.id,
            name: produit.name,
            unit: produit.unit,
            consommation: consommationTotale,
            stockInitialMoyen: stockInitialTotal / stocksProduitsFiltered.length,
            stockFinalMoyen: stockFinalTotal / stocksProduitsFiltered.length,
            entreesMoyennes: entreesTotal / stocksProduitsFiltered.length
          });
        }
      }

      // Calculer le total de consommation (somme pondérée ou autre logique selon besoin)
      const totalConsommation = consommationItems.reduce((total, item) => total + item.consommation, 0);

      return {
        dateDebut,
        dateFin,
        nombreJours: stocksPeriode.length,
        items: consommationItems,
        totalConsommation
      };

    } catch (error) {
      console.error('Erreur lors du calcul de consommation:', error);
      throw error;
    }
  }

  // Obtenir la consommation de la semaine courante
  static async getConsommationSemaineCourante(): Promise<ConsommationPeriode> {
    const aujourd_hui = new Date();
    const lundiSemaine = new Date(aujourd_hui);
    lundiSemaine.setDate(aujourd_hui.getDate() - aujourd_hui.getDay() + 1);
    
    const dimancheSemaine = new Date(lundiSemaine);
    dimancheSemaine.setDate(lundiSemaine.getDate() + 6);

    const dateDebut = lundiSemaine.toISOString().split('T')[0];
    const dateFin = dimancheSemaine.toISOString().split('T')[0];

    return this.getConsommationPeriode(dateDebut, dateFin);
  }

  // Obtenir la consommation de la semaine dernière
  static async getConsommationSemaineDerniere(): Promise<ConsommationPeriode> {
    const aujourd_hui = new Date();
    const lundiSemaineDerniere = new Date(aujourd_hui);
    lundiSemaineDerniere.setDate(aujourd_hui.getDate() - aujourd_hui.getDay() + 1 - 7);
    
    const dimancheSemaineDerniere = new Date(lundiSemaineDerniere);
    dimancheSemaineDerniere.setDate(lundiSemaineDerniere.getDate() + 6);

    const dateDebut = lundiSemaineDerniere.toISOString().split('T')[0];
    const dateFin = dimancheSemaineDerniere.toISOString().split('T')[0];

    return this.getConsommationPeriode(dateDebut, dateFin);
  }

  // Obtenir la consommation du mois courant
  static async getConsommationMoisCourant(): Promise<ConsommationPeriode> {
    const aujourd_hui = new Date();
    const premierJourMois = new Date(aujourd_hui.getFullYear(), aujourd_hui.getMonth(), 1);
    const dernierJourMois = new Date(aujourd_hui.getFullYear(), aujourd_hui.getMonth() + 1, 0);

    const dateDebut = premierJourMois.toISOString().split('T')[0];
    const dateFin = dernierJourMois.toISOString().split('T')[0];

    return this.getConsommationPeriode(dateDebut, dateFin);
  }

  // Obtenir les consommations par semaine sur plusieurs semaines
  static async getConsommationParSemaines(nombreSemaines: number = 4): Promise<ConsommationPeriode[]> {
    const consommations: ConsommationPeriode[] = [];
    
    for (let i = 0; i < nombreSemaines; i++) {
      const aujourd_hui = new Date();
      const lundiSemaine = new Date(aujourd_hui);
      lundiSemaine.setDate(aujourd_hui.getDate() - aujourd_hui.getDay() + 1 - (i * 7));
      
      const dimancheSemaine = new Date(lundiSemaine);
      dimancheSemaine.setDate(lundiSemaine.getDate() + 6);

      const dateDebut = lundiSemaine.toISOString().split('T')[0];
      const dateFin = dimancheSemaine.toISOString().split('T')[0];

      const consommation = await this.getConsommationPeriode(dateDebut, dateFin);
      consommations.push(consommation);
    }

    return consommations.reverse(); // Plus ancien en premier
  }

  // Formater une période en texte lisible
  static formatPeriode(consommation: ConsommationPeriode): string {
    const dateDebut = new Date(consommation.dateDebut);
    const dateFin = new Date(consommation.dateFin);
    
    return `${dateDebut.toLocaleDateString('fr-FR', { day: 'numeric', month: 'short' })} - ${dateFin.toLocaleDateString('fr-FR', { day: 'numeric', month: 'short' })}`;
  }
}
