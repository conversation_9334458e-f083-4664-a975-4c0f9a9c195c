import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  Alert,
  ScrollView,
  Dimensions,
} from 'react-native';
import { OFFERS } from '../data/offers';
import { OrderItem } from '../types';
import { Colors, FontSizes, Spacing, BorderRadius, Shadows } from '../styles/theme';

const { width } = Dimensions.get('window');

interface AdminScreenProps {
  navigation: any;
}

export const AdminScreen: React.FC<AdminScreenProps> = ({ navigation }) => {
  const [cart, setCart] = useState<OrderItem[]>([]);
  const [cartTotal, setCartTotal] = useState(0);

  useEffect(() => {
    const total = cart.reduce((sum, item) => sum + item.totalPrice, 0);
    setCartTotal(total);
  }, [cart]);

  const handleViewOffers = () => {
    navigation.navigate('Offers');
  };

  const handleViewClientSpace = () => {
    navigation.navigate('ClientSpace');
  };

  const handleViewProducts = () => {
    navigation.navigate('Products');
  };

  const handleViewCart = () => {
    if (cart.length === 0) {
      Alert.alert('Panier vide', 'Ajoutez des offres à votre panier avant de continuer.');
      return;
    }

    navigation.navigate('OrderForm', { cartItems: cart });
  };

  const handleViewStats = () => {
    navigation.navigate('Statistics');
  };

  const handleStock = () => {
    navigation.navigate('Stock');
  };

  const handleConsommation = () => {
    navigation.navigate('Consommation');
  };

  const handleDataReset = () => {
    navigation.navigate('DataReset');
  };

  const handlePromotion = () => {
    navigation.navigate('Promotion');
  };

  const clearCart = () => {
    Alert.alert(
      'Vider le panier',
      'Êtes-vous sûr de vouloir vider le panier ?',
      [
        { text: 'Annuler', style: 'cancel' },
        { 
          text: 'Vider', 
          style: 'destructive',
          onPress: () => setCart([])
        }
      ]
    );
  };

  // Render main action buttons
  const renderActionButton = ({ icon, title, onPress }: { icon: string, title: string, onPress: () => void }) => (
    <TouchableOpacity style={styles.mainActionButton} onPress={onPress}>
      <Text style={styles.mainActionIcon}>{icon}</Text>
      <Text style={styles.mainActionText}>{title}</Text>
    </TouchableOpacity>
  );

  return (
    <View style={styles.container}>
      {/* Header Admin */}
      <View style={styles.header}>
        <View style={styles.headerTop}>
          <Text style={styles.title}>Admin</Text>
          <TouchableOpacity style={styles.logoutButton} onPress={() => navigation.logout?.()}>
            <Text style={styles.logoutButtonText}>🚪 Déconnexion</Text>
          </TouchableOpacity>
        </View>
        <View style={styles.headerButtons}>
          <TouchableOpacity style={styles.actionButton} onPress={handleViewStats}>
            <Text style={styles.actionButtonText}>📊 Stats</Text>
          </TouchableOpacity>
          <TouchableOpacity style={styles.actionButton} onPress={handleStock}>
            <Text style={styles.actionButtonText}>📦 Stock</Text>
          </TouchableOpacity>
          <TouchableOpacity style={styles.actionButton} onPress={handleConsommation}>
            <Text style={styles.actionButtonText}>📈 Consommation</Text>
          </TouchableOpacity>
          <TouchableOpacity style={styles.actionButton} onPress={handlePromotion}>
            <Text style={styles.actionButtonText}>🎯 Promotion</Text>
          </TouchableOpacity>
        </View>
      </View>

      {/* Main Actions */}
      <ScrollView style={styles.mainActionsContainer} showsVerticalScrollIndicator={false}>
        {/* Boutons principaux */}
        <TouchableOpacity style={styles.primaryActionButton} onPress={handleViewOffers}>
          <Text style={styles.primaryActionIcon}>🍕</Text>
          <Text style={styles.primaryActionTitle}>Nos Offres</Text>
          <Text style={styles.primaryActionSubtitle}>Commandez vos promos préférées</Text>
        </TouchableOpacity>

        <TouchableOpacity style={styles.primaryActionButton} onPress={handleViewClientSpace}>
          <Text style={styles.primaryActionIcon}>👥</Text>
          <Text style={styles.primaryActionTitle}>Espace Client</Text>
          <Text style={styles.primaryActionSubtitle}>Rechercher l'historique des clients</Text>
        </TouchableOpacity>



        {/* Boutons secondaires en grille 2x2 */}
        <View style={styles.secondaryActionsGrid}>
          <TouchableOpacity style={styles.secondaryActionButton} onPress={handleViewStats}>
            <Text style={styles.secondaryActionIcon}>📊</Text>
            <Text style={styles.secondaryActionText}>Statistiques</Text>
          </TouchableOpacity>

          <TouchableOpacity style={styles.secondaryActionButton} onPress={handleStock}>
            <Text style={styles.secondaryActionIcon}>📦</Text>
            <Text style={styles.secondaryActionText}>Stock</Text>
          </TouchableOpacity>

          <TouchableOpacity style={styles.secondaryActionButton} onPress={handleConsommation}>
            <Text style={styles.secondaryActionIcon}>📈</Text>
            <Text style={styles.secondaryActionText}>Consommation</Text>
          </TouchableOpacity>

          <TouchableOpacity style={styles.secondaryActionButton} onPress={handleDataReset}>
            <Text style={styles.secondaryActionIcon}>🗑️</Text>
            <Text style={styles.secondaryActionText}>Nettoyage</Text>
          </TouchableOpacity>

          <TouchableOpacity style={styles.secondaryActionButton} onPress={handlePromotion}>
            <Text style={styles.secondaryActionIcon}>🎯</Text>
            <Text style={styles.secondaryActionText}>Promotions</Text>
          </TouchableOpacity>

          <TouchableOpacity style={styles.secondaryActionButton} onPress={() => navigation.logout()}>
            <Text style={styles.secondaryActionIcon}>🚪</Text>
            <Text style={styles.secondaryActionText}>Déconnexion</Text>
          </TouchableOpacity>
        </View>
      </ScrollView>

      {/* Panier flottant */}
      {cart.length > 0 && (
        <View style={styles.cartContainer}>
          <View style={styles.cartInfo}>
            <Text style={styles.cartText}>
              {cart.length} article{cart.length > 1 ? 's' : ''} - {cartTotal.toFixed(2)} DT
            </Text>
            <TouchableOpacity onPress={clearCart}>
              <Text style={styles.clearCartText}>🗑️</Text>
            </TouchableOpacity>
          </View>
          <TouchableOpacity style={styles.cartButton} onPress={handleViewCart}>
            <Text style={styles.cartButtonText}>Voir le panier</Text>
          </TouchableOpacity>
        </View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.backgroundSolid,
  },
  header: {
    backgroundColor: Colors.secondary,
    paddingTop: 50,
    paddingBottom: 20,
    paddingHorizontal: 16,
    borderBottomLeftRadius: 20,
    borderBottomRightRadius: 20,
    ...Shadows.medium,
  },
  headerTop: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 15,
  },
  title: {
    fontSize: FontSizes.xxxl,
    fontWeight: 'bold',
    color: Colors.textOnDark,
    flex: 1,
  },
  headerButtons: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    gap: 12,
    flexWrap: 'wrap',
  },
  actionButton: {
    backgroundColor: Colors.accent,
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderRadius: BorderRadius.large,
    flex: 1,
    alignItems: 'center',
    minHeight: 48,
    justifyContent: 'center',
    ...Shadows.medium,
    minWidth: 80,
    maxWidth: 120,
  },
  actionButtonText: {
    color: Colors.textOnPrimary,
    fontSize: FontSizes.sm,
    fontWeight: '700',
    textAlign: 'center',
  },
  logoutButton: {
    backgroundColor: Colors.primary,
    paddingHorizontal: 16,
    paddingVertical: 10,
    borderRadius: BorderRadius.large,
    minHeight: 44,
    justifyContent: 'center',
    ...Shadows.medium,
  },
  logoutButtonText: {
    color: Colors.textOnPrimary,
    fontSize: FontSizes.sm,
    fontWeight: '700',
    textAlign: 'center',
  },
  mainActionsContainer: {
    flex: 1,
    padding: Spacing.lg,
    paddingTop: Spacing.xl,
  },
  primaryActionButton: {
    backgroundColor: Colors.primary,
    padding: Spacing.xl,
    borderRadius: 20,
    alignItems: 'center',
    marginBottom: Spacing.xl,
    ...Shadows.large,
    minHeight: 160,
    justifyContent: 'center',
    borderWidth: 2,
    borderColor: 'rgba(255, 255, 255, 0.2)',
  },
  primaryActionIcon: {
    fontSize: 60,
    marginBottom: Spacing.md,
    textShadowColor: 'rgba(0, 0, 0, 0.3)',
    textShadowOffset: { width: 2, height: 2 },
    textShadowRadius: 4,
  },
  primaryActionTitle: {
    fontSize: FontSizes.xxl + 2,
    fontWeight: 'bold',
    color: Colors.textOnPrimary,
    textAlign: 'center',
    marginBottom: Spacing.sm,
    textShadowColor: 'rgba(0, 0, 0, 0.3)',
    textShadowOffset: { width: 1, height: 1 },
    textShadowRadius: 2,
  },
  primaryActionSubtitle: {
    fontSize: FontSizes.md,
    color: Colors.textOnPrimary,
    textAlign: 'center',
    opacity: 0.95,
    fontWeight: '500',
  },
  secondaryActionsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    gap: 12,
  },
  secondaryActionButton: {
    backgroundColor: Colors.surface,
    width: (width - 72) / 2,
    padding: Spacing.lg,
    borderRadius: 16,
    alignItems: 'center',
    marginBottom: Spacing.md,
    ...Shadows.medium,
    minHeight: 110,
    justifyContent: 'center',
    borderWidth: 1,
    borderColor: Colors.borderLight,
  },
  secondaryActionIcon: {
    fontSize: 36,
    marginBottom: Spacing.sm,
  },
  secondaryActionText: {
    fontSize: FontSizes.md,
    fontWeight: '700',
    color: Colors.textPrimary,
    textAlign: 'center',
  },
  cartContainer: {
    position: 'absolute',
    bottom: 0,
    left: 12,
    right: 12,
    backgroundColor: Colors.surface,
    padding: Spacing.lg,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    borderWidth: 1,
    borderColor: Colors.borderLight,
    ...Shadows.large,
    marginBottom: 0,
  },
  cartInfo: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: Spacing.md,
    paddingHorizontal: Spacing.sm,
  },
  cartText: {
    fontSize: FontSizes.md,
    fontWeight: '700',
    color: Colors.textPrimary,
  },
  clearCartText: {
    fontSize: FontSizes.xl,
    padding: Spacing.sm,
    backgroundColor: Colors.light,
    borderRadius: BorderRadius.round,
    overflow: 'hidden',
  },
  cartButton: {
    backgroundColor: Colors.primary,
    borderRadius: 16,
    paddingVertical: Spacing.lg,
    alignItems: 'center',
    ...Shadows.medium,
  },
  cartButtonText: {
    fontSize: FontSizes.md,
    fontWeight: 'bold',
    color: Colors.textOnPrimary,
  },
});
