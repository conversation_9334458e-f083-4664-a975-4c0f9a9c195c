import AsyncStorage from '@react-native-async-storage/async-storage';

export interface StockItem {
  id: string;
  name: string;
  unit: string;
  stockInitial: number;
  entrees: number;
  stockFinal: number;
  seuilAlerte: number;
  category: string;
}

export interface DailyStock {
  date: string;
  items: StockItem[];
  isFinalized: boolean;
}

export class StockService {
  private static STORAGE_KEY = 'daily_stocks';

  // Obtenir le stock par défaut
  private static getDefaultStock(): StockItem[] {
    return [
      // Ingrédients de base
      {
        id: 'pate',
        name: 'Pâte à pizza',
        unit: 'kg',
        stockInitial: 0,
        entrees: 0,
        stockFinal: 0,
        seuilAlerte: 10,
        category: 'Ingrédients'
      },
      {
        id: 'sauce',
        name: 'Sauce tomate',
        unit: 'L',
        stockInitial: 0,
        entrees: 0,
        stockFinal: 0,
        seuilAlerte: 3,
        category: 'Sauces'
      },
      {
        id: 'creme_fraiche',
        name: '<PERSON>r<PERSON> fraî<PERSON>',
        unit: 'pièce',
        stockInitial: 0,
        entrees: 0,
        stockFinal: 0,
        seuilAlerte: 5,
        category: 'Sauces'
      },

      // Fromages
      {
        id: 'mozzarella',
        name: '<PERSON><PERSON><PERSON>',
        unit: 'kg',
        stockInitial: 0,
        entrees: 0,
        stockFinal: 0,
        seuilAlerte: 5,
        category: 'Fromages'
      },
      {
        id: 'cheddar',
        name: 'Cheddar',
        unit: 'kg',
        stockInitial: 0,
        entrees: 0,
        stockFinal: 0,
        seuilAlerte: 2,
        category: 'Fromages'
      },
      {
        id: 'camembert',
        name: 'Camembert',
        unit: 'kg',
        stockInitial: 0,
        entrees: 0,
        stockFinal: 0,
        seuilAlerte: 1,
        category: 'Fromages'
      },
      {
        id: 'roquefort',
        name: 'Roquefort',
        unit: 'kg',
        stockInitial: 0,
        entrees: 0,
        stockFinal: 0,
        seuilAlerte: 1,
        category: 'Fromages'
      },

      // Viandes
      {
        id: 'viande_hachee',
        name: 'Viande hachée',
        unit: 'kg',
        stockInitial: 0,
        entrees: 0,
        stockFinal: 0,
        seuilAlerte: 3,
        category: 'Viandes'
      },
      {
        id: 'poulet',
        name: 'Poulet',
        unit: 'kg',
        stockInitial: 0,
        entrees: 0,
        stockFinal: 0,
        seuilAlerte: 3,
        category: 'Viandes'
      },
      {
        id: 'jambon',
        name: 'Jambon',
        unit: 'kg',
        stockInitial: 0,
        entrees: 0,
        stockFinal: 0,
        seuilAlerte: 2,
        category: 'Viandes'
      },
      {
        id: 'jambon_fume',
        name: 'Jambon fumé',
        unit: 'kg',
        stockInitial: 0,
        entrees: 0,
        stockFinal: 0,
        seuilAlerte: 2,
        category: 'Viandes'
      },
      {
        id: 'pepperoni',
        name: 'Pepperoni',
        unit: 'kg',
        stockInitial: 0,
        entrees: 0,
        stockFinal: 0,
        seuilAlerte: 2,
        category: 'Viandes'
      },

      // Légumes
      {
        id: 'champignons',
        name: 'Champignons',
        unit: 'kg',
        stockInitial: 0,
        entrees: 0,
        stockFinal: 0,
        seuilAlerte: 2,
        category: 'Légumes'
      },
      {
        id: 'poivron',
        name: 'Poivron',
        unit: 'kg',
        stockInitial: 0,
        entrees: 0,
        stockFinal: 0,
        seuilAlerte: 2,
        category: 'Légumes'
      },
      {
        id: 'oignon',
        name: 'Oignon',
        unit: 'kg',
        stockInitial: 0,
        entrees: 0,
        stockFinal: 0,
        seuilAlerte: 3,
        category: 'Légumes'
      },
      {
        id: 'olives',
        name: 'Olives',
        unit: 'kg',
        stockInitial: 0,
        entrees: 0,
        stockFinal: 0,
        seuilAlerte: 1,
        category: 'Légumes'
      }
    ];
  }

  // Obtenir le stock pour une date donnée
  static async getStockForDate(date: string): Promise<DailyStock> {
    try {
      const stocksData = await AsyncStorage.getItem(this.STORAGE_KEY);
      const stocks: { [key: string]: DailyStock } = stocksData ? JSON.parse(stocksData) : {};

      if (stocks[date]) {
        return stocks[date];
      }

      // Si pas de stock pour cette date, créer un nouveau stock
      const newStock: DailyStock = {
        date,
        items: this.getDefaultStock(),
        isFinalized: false
      };

      // Vérifier s'il y a un stock finalisé du jour précédent
      const previousDate = this.getPreviousDate(date);
      if (stocks[previousDate] && stocks[previousDate].isFinalized) {
        // Copier le stock final du jour précédent comme stock initial
        newStock.items = newStock.items.map(item => {
          const previousItem = stocks[previousDate].items.find(prev => prev.id === item.id);
          if (previousItem) {
            return {
              ...item,
              stockInitial: previousItem.stockFinal,
              stockFinal: previousItem.stockFinal // Initialiser stock final = stock initial
            };
          }
          return item;
        });
      }

      return newStock;
    } catch (error) {
      console.error('Erreur lors du chargement du stock:', error);
      return {
        date,
        items: this.getDefaultStock(),
        isFinalized: false
      };
    }
  }

  // Sauvegarder le stock pour une date
  static async saveStockForDate(stockData: DailyStock): Promise<void> {
    try {
      const stocksData = await AsyncStorage.getItem(this.STORAGE_KEY);
      const stocks: { [key: string]: DailyStock } = stocksData ? JSON.parse(stocksData) : {};

      // Ne pas recalculer le stock final - il est saisi manuellement par l'utilisateur
      stocks[stockData.date] = stockData;
      await AsyncStorage.setItem(this.STORAGE_KEY, JSON.stringify(stocks));

      console.log(`📝 Stock sauvegardé pour ${stockData.date}`);

      // Mettre à jour automatiquement le jour suivant avec le stock final saisi
      const nextDate = this.getNextDate(stockData.date);
      await this.updateNextDayInitialStock(stockData.date, nextDate);
    } catch (error) {
      console.error('Erreur lors de la sauvegarde du stock:', error);
      throw error;
    }
  }

  // Finaliser le stock du jour (verrouiller les modifications)
  static async finalizeStock(date: string): Promise<void> {
    try {
      const stocksData = await AsyncStorage.getItem(this.STORAGE_KEY);
      const stocks: { [key: string]: DailyStock } = stocksData ? JSON.parse(stocksData) : {};

      if (stocks[date]) {
        stocks[date].isFinalized = true;
        await AsyncStorage.setItem(this.STORAGE_KEY, JSON.stringify(stocks));

        // Mettre à jour le stock initial du jour suivant
        const nextDate = this.getNextDate(date);
        await this.updateNextDayInitialStock(date, nextDate);
      }
    } catch (error) {
      console.error('Erreur lors de la finalisation du stock:', error);
      throw error;
    }
  }

  // Mettre à jour le stock initial du jour suivant
  private static async updateNextDayInitialStock(currentDate: string, nextDate: string): Promise<void> {
    try {
      const stocksData = await AsyncStorage.getItem(this.STORAGE_KEY);
      const stocks: { [key: string]: DailyStock } = stocksData ? JSON.parse(stocksData) : {};

      const currentStock = stocks[currentDate];
      if (!currentStock) {
        console.log(`⚠️ Pas de stock trouvé pour ${currentDate}`);
        return;
      }

      // Créer ou mettre à jour le stock du jour suivant
      if (!stocks[nextDate]) {
        stocks[nextDate] = {
          date: nextDate,
          items: this.getDefaultStock(),
          isFinalized: false
        };
        console.log(`📅 Nouveau stock créé pour ${nextDate}`);
      }

      // Compter les items avec stock final > 0
      const itemsWithFinalStock = currentStock.items.filter(item => item.stockFinal > 0);
      console.log(`📊 ${itemsWithFinalStock.length} items avec stock final à synchroniser`);

      // Mettre à jour le stock initial du jour suivant avec le stock final du jour actuel
      stocks[nextDate].items = stocks[nextDate].items.map(item => {
        const currentItem = currentStock.items.find(curr => curr.id === item.id);
        if (currentItem && currentItem.stockFinal > 0) {
          const newStockInitial = currentItem.stockFinal;
          console.log(`🔄 ${item.name}: ${currentItem.stockFinal} → Stock Initial ${nextDate}`);
          return {
            ...item,
            stockInitial: newStockInitial,
            entrees: 0, // Remettre les entrées à zéro
            stockFinal: 0 // Remettre le stock final à zéro pour saisie manuelle
          };
        }
        return item;
      });

      await AsyncStorage.setItem(this.STORAGE_KEY, JSON.stringify(stocks));
      console.log(`✅ Synchronisation terminée: ${currentDate} → ${nextDate}`);
    } catch (error) {
      console.error('Erreur lors de la mise à jour du stock suivant:', error);
      throw error;
    }
  }

  // Obtenir la date précédente
  private static getPreviousDate(dateStr: string): string {
    const date = new Date(dateStr);
    date.setDate(date.getDate() - 1);
    return date.toISOString().split('T')[0];
  }

  // Obtenir la date suivante
  private static getNextDate(dateStr: string): string {
    const date = new Date(dateStr);
    date.setDate(date.getDate() + 1);
    return date.toISOString().split('T')[0];
  }

  // Obtenir les stocks avec alertes
  static async getStocksWithAlerts(): Promise<StockItem[]> {
    try {
      const today = new Date().toISOString().split('T')[0];
      const todayStock = await this.getStockForDate(today);
      
      return todayStock.items.filter(item => item.stockFinal < item.seuilAlerte);
    } catch (error) {
      console.error('Erreur lors de la récupération des alertes:', error);
      return [];
    }
  }

  // Réinitialiser tout le stock
  static async resetAllStock(): Promise<void> {
    try {
      await AsyncStorage.removeItem(this.STORAGE_KEY);
      console.log('Tout le stock a été réinitialisé');
    } catch (error) {
      console.error('Erreur lors de la réinitialisation:', error);
      throw error;
    }
  }

  // Obtenir l'historique des stocks
  static async getStockHistory(): Promise<DailyStock[]> {
    try {
      const stocksData = await AsyncStorage.getItem(this.STORAGE_KEY);
      const stocks: { [key: string]: DailyStock } = stocksData ? JSON.parse(stocksData) : {};

      return Object.values(stocks).sort((a, b) => b.date.localeCompare(a.date));
    } catch (error) {
      console.error('Erreur lors de la récupération de l\'historique:', error);
      return [];
    }
  }

  // Forcer la synchronisation entre deux jours
  static async forceSyncBetweenDays(fromDate: string, toDate: string): Promise<void> {
    try {
      await this.updateNextDayInitialStock(fromDate, toDate);
      console.log(`🔄 Synchronisation forcée: ${fromDate} → ${toDate}`);
    } catch (error) {
      console.error('Erreur lors de la synchronisation forcée:', error);
      throw error;
    }
  }

  // Débloquer un stock finalisé (pour corrections)
  static async unlockStock(date: string): Promise<void> {
    try {
      const stocksData = await AsyncStorage.getItem(this.STORAGE_KEY);
      const stocks: { [key: string]: DailyStock } = stocksData ? JSON.parse(stocksData) : {};

      if (stocks[date]) {
        stocks[date].isFinalized = false;
        await AsyncStorage.setItem(this.STORAGE_KEY, JSON.stringify(stocks));
        console.log(`🔓 Stock du ${date} débloqué pour modification`);
      }
    } catch (error) {
      console.error('Erreur lors du déblocage du stock:', error);
      throw error;
    }
  }

  // Vérifier la continuité entre deux jours
  static async verifierContinuite(dateJ: string, dateJPlus1: string): Promise<boolean> {
    try {
      const stocksData = await AsyncStorage.getItem(this.STORAGE_KEY);
      const stocks: { [key: string]: DailyStock } = stocksData ? JSON.parse(stocksData) : {};

      const stockJ = stocks[dateJ];
      const stockJPlus1 = stocks[dateJPlus1];

      if (!stockJ || !stockJPlus1) {
        console.log(`⚠️ Stocks manquants pour vérification continuité: ${dateJ} ou ${dateJPlus1}`);
        return false;
      }

      let continuiteBonne = true;
      for (const itemJ of stockJ.items) {
        const itemJPlus1 = stockJPlus1.items.find(item => item.id === itemJ.id);
        if (itemJPlus1 && itemJ.stockFinal !== itemJPlus1.stockInitial) {
          console.log(`❌ Continuité rompue pour ${itemJ.name}: Final ${dateJ} = ${itemJ.stockFinal}, Initial ${dateJPlus1} = ${itemJPlus1.stockInitial}`);
          continuiteBonne = false;
        }
      }

      if (continuiteBonne) {
        console.log(`✅ Continuité vérifiée entre ${dateJ} et ${dateJPlus1}`);
      }

      return continuiteBonne;
    } catch (error) {
      console.error('Erreur lors de la vérification de continuité:', error);
      return false;
    }
  }

  // Réparer la continuité entre deux jours
  static async reparerContinuite(dateJ: string, dateJPlus1: string): Promise<void> {
    try {
      console.log(`🔧 Réparation de la continuité ${dateJ} → ${dateJPlus1}`);
      await this.forceSyncBetweenDays(dateJ, dateJPlus1);
      console.log(`✅ Continuité réparée`);
    } catch (error) {
      console.error('Erreur lors de la réparation de continuité:', error);
      throw error;
    }
  }
}
