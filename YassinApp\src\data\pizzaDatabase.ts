// Base de données des pizzas - Ya<PERSON>App
// Cette liste contient toutes les pizzas de base du restaurant

export const BASE_PIZZAS = [
  'MARGUERITA',
  'REINE', 
  'PEPPERONI LOVERS',
  'NEPTUNE',
  'TEXAS CHICKEN',
  'CHICKEN',
  'POULET',
  'BB<PERSON> CHICKEN',
  'COMPAGNARDE',
  'BOSTON',
  'CALIFORNI<PERSON>',
  'CHEESEBURGER',
  'STEAK ROQUEFORT',
  'MEAT LOVERS',
  'WITH THE LOT',
  'CHESSY LOVERS',
  'ALASKA',
  'MR JO PIZZA'
];

// Correspondances pour normaliser les noms de pizzas
export const PIZZA_MAPPINGS: { [key: string]: string } = {
  // Marguerita variations
  'MARGHERITA': 'MARGUERITA',
  'MARGUERITE': 'MARGUERITA',
  'MARGARITA': 'MARGUERI<PERSON>',
  
  // Pepperoni variations
  'PEPPERONI': 'PEPPERONI LOVERS',
  'PEPERONI': 'PEP<PERSON>ERON<PERSON> LOVERS',
  
  // Fromage variations
  'QUATRE FROMAGES': 'CHESSY LOVERS',
  'FROMAGES': 'CHESSY LOVERS',
  'CHEESE': 'CHESSY LOVERS',
  '4 FROMAGES': 'CHESSY LOVERS',
  
  // Végétarienne variations
  'VEGETARIENNE': 'COMPAGNARDE',
  'VEGGIE': 'COMPAGNARDE',
  'VEGETARIAN': 'COMPAGNARDE',
  
  // Fruits de mer variations
  'FRUITS DE MER': 'NEPTUNE',
  'SEAFOOD': 'NEPTUNE',
  'POISSON': 'NEPTUNE',
  
  // Poulet variations
  'POULET BBQ': 'BBQ CHICKEN',
  'CHICKEN BBQ': 'BBQ CHICKEN',
  'BARBECUE CHICKEN': 'BBQ CHICKEN',
  'BARBECUE POULET': 'BBQ CHICKEN',
  
  // Viande variations
  'VIANDE': 'MEAT LOVERS',
  'MEAT': 'MEAT LOVERS',
  'VIANDES': 'MEAT LOVERS',
  
  // Reine variations
  'QUEEN': 'REINE',
  'ROYALE': 'REINE',
  
  // Chicken variations
  'POULET': 'CHICKEN',
  
  // Autres variations communes
  'CHEESEBURGER PIZZA': 'CHEESEBURGER',
  'BURGER': 'CHEESEBURGER',
  'STEAK': 'STEAK ROQUEFORT',
  'ROQUEFORT': 'STEAK ROQUEFORT'
};

// Mots-clés pour identifier les pizzas
export const PIZZA_KEYWORDS = [
  'pizza', 'margherita', 'pepperoni', 'fromage', 'chicken', 
  'poulet', 'meat', 'viande', 'reine', 'neptune', 'bbq',
  'cheese', 'burger', 'steak', 'roquefort', 'alaska',
  'boston', 'california', 'texas', 'compagnarde'
];

// Fonction utilitaire pour nettoyer les noms de pizzas
export const cleanPizzaName = (itemName: string): string | null => {
  if (!itemName) return null;
  
  // Nettoyer le nom : enlever les tailles, "Pizza", etc.
  let cleanName = itemName
    .replace(/\s+(M|L|XL|Small|Medium|Large)$/i, '') // Enlever les tailles
    .replace(/^Pizza\s+/i, '') // Enlever "Pizza" au début
    .replace(/\s+Pizza$/i, '') // Enlever "Pizza" à la fin
    .replace(/^Offre\s+\w+\s*-\s*/i, '') // Enlever "Offre Dimanche -"
    .replace(/^Promo\s+\w+\s*-\s*/i, '') // Enlever "Promo Family -"
    .trim()
    .toUpperCase();

  // Vérifier les correspondances spéciales
  if (PIZZA_MAPPINGS[cleanName]) {
    return PIZZA_MAPPINGS[cleanName];
  }

  // Chercher une correspondance exacte dans la liste de base
  const exactMatch = BASE_PIZZAS.find(pizza => pizza === cleanName);
  if (exactMatch) {
    return exactMatch;
  }

  // Chercher une correspondance partielle
  const partialMatch = BASE_PIZZAS.find(pizza => {
    const pizzaWords = pizza.split(' ');
    const nameWords = cleanName.split(' ');
    
    // Si au moins 50% des mots correspondent
    const matchingWords = pizzaWords.filter(word => 
      nameWords.some(nameWord => 
        nameWord.includes(word) || word.includes(nameWord)
      )
    );
    
    return matchingWords.length >= Math.ceil(pizzaWords.length * 0.5);
  });

  if (partialMatch) {
    return partialMatch;
  }

  // Si aucune correspondance et que ça ressemble à une pizza, retourner le nom nettoyé
  const lowerItemName = itemName.toLowerCase();
  const isPizza = PIZZA_KEYWORDS.some(keyword => lowerItemName.includes(keyword));
  
  if (cleanName.length > 2 && isPizza) {
    return cleanName;
  }

  return null;
};

// Fonction pour obtenir la liste complète des pizzas avec leurs variantes
export const getAllPizzaVariants = (): string[] => {
  const allVariants = new Set<string>();
  
  // Ajouter les pizzas de base
  BASE_PIZZAS.forEach(pizza => allVariants.add(pizza));
  
  // Ajouter les variantes des mappings
  Object.keys(PIZZA_MAPPINGS).forEach(variant => allVariants.add(variant));
  
  return Array.from(allVariants).sort();
};

// Fonction pour vérifier si un item est une pizza
export const isPizzaItem = (itemName: string): boolean => {
  if (!itemName) return false;
  
  const lowerName = itemName.toLowerCase();
  return PIZZA_KEYWORDS.some(keyword => lowerName.includes(keyword));
};
