@echo off
chcp 65001 >nul
cls

echo ==========================================
echo    🔍 VÉRIFICATION DES BUILDS APK
echo ==========================================
echo.
echo 📱 Recherche de vos APK disponibles...
echo.

cd /d "C:\Users\<USER>\Documents\AppCaisse\Apk test\PizzaAPK"

echo 🔐 Vérification de la connexion...
npx expo whoami

echo.
echo 📋 Liste de vos builds récentes:
echo.

npx eas build:list --platform android --limit 5

echo.
echo ==========================================
echo 📱 LIENS DE TÉLÉCHARGEMENT
echo ==========================================
echo.
echo 🔗 Si vous voyez des builds "FINISHED" ci-dessus,
echo    vous pouvez télécharger l'APK avec les liens affichés
echo.
echo 📋 Ou allez sur:
echo    https://expo.dev/accounts/brona/projects
echo.
echo 🔑 Connectez-vous avec:
echo    Email: <EMAIL>
echo    Mot de passe: Themer@10
echo.
echo ✅ Trouvez votre projet et téléchargez l'APK !
echo.
pause
