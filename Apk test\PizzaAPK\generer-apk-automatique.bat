@echo off
chcp 65001 >nul
cls

echo ==========================================
echo    🤖 GÉNÉRATION APK AUTOMATIQUE
echo ==========================================
echo.
echo 📱 Application: Pizza Caisse Manager 2.0
echo 🎯 Génération automatique du lien APK
echo ⏳ Processus entièrement automatisé
echo.
echo ==========================================

cd /d "C:\Users\<USER>\Documents\AppCaisse\Apk test\PizzaAPK"

echo 📂 Répertoire: %CD%
echo.

echo 🧹 Nettoyage du cache...
if exist ".expo" rmdir /s /q ".expo" >nul 2>&1
if exist "node_modules\.cache" rmdir /s /q "node_modules\.cache" >nul 2>&1
echo ✅ Cache nettoyé
echo.

echo 📦 Vérification des dépendances...
if not exist "node_modules" (
    echo 📥 Installation automatique des dépendances...
    npm install --silent
    echo ✅ Dépendances installées
) else (
    echo ✅ Dépendances déjà présentes
)
echo.

echo 🔐 Connexion automatique à Expo...
echo 📧 Email: <EMAIL>
echo.

(
echo <EMAIL>
echo Themer@10
) | npx expo login

if %errorlevel% neq 0 (
    echo ❌ Erreur de connexion automatique
    echo 🔧 Tentative de connexion manuelle...
    npx expo login
)

echo ✅ Connexion réussie !
echo.

echo 🏗️ Démarrage de la génération APK...
echo ⏳ Temps estimé: 10-15 minutes
echo 📱 Génération d'un APK standalone
echo.

echo 🚀 Lancement de la build automatique...
npx eas build --platform android --profile production --non-interactive --wait

if %errorlevel% neq 0 (
    echo.
    echo ⚠️ Tentative avec méthode alternative...
    npx expo build:android -t apk --non-interactive
)

echo.
echo ==========================================
echo 🎉 GÉNÉRATION TERMINÉE !
echo ==========================================
echo.
echo 📱 Votre APK est prêt !
echo.
echo 🔗 LIEN DE TÉLÉCHARGEMENT:
echo    Le lien est affiché ci-dessus ⬆️
echo.
echo 📋 Instructions:
echo 1. Copiez le lien de téléchargement
echo 2. Collez-le dans votre navigateur
echo 3. Téléchargez l'APK sur votre téléphone
echo 4. Installez l'APK (autorisez sources inconnues)
echo 5. Lancez l'application "Caisse"
echo.
echo 🚀 Application standalone prête !
echo 📱 Plus besoin d'Expo Go
echo.
echo ==========================================

echo.
echo 🔍 Récupération du lien de téléchargement...
npx eas build:list --platform android --limit 1 --json > build_info.json

if exist "build_info.json" (
    echo.
    echo 📋 Informations de build sauvegardées dans build_info.json
    echo 🔗 Consultez ce fichier pour le lien direct
)

echo.
echo ✅ PROCESSUS TERMINÉ !
echo 📱 Votre lien APK est disponible ci-dessus
echo.
pause
