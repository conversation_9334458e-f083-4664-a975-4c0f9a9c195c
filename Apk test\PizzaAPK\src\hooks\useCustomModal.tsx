import React, { useState, useCallback } from 'react';
import { CustomModal } from '../components/CustomModal';

interface ModalButton {
  text: string;
  onPress: () => void;
  style?: 'default' | 'cancel' | 'destructive';
}

interface ModalConfig {
  title: string;
  message: string;
  buttons: ModalButton[];
}

export const useCustomModal = () => {
  const [modalConfig, setModalConfig] = useState<ModalConfig | null>(null);
  const [isVisible, setIsVisible] = useState(false);

  const showModal = useCallback((config: ModalConfig) => {
    setModalConfig(config);
    setIsVisible(true);
  }, []);

  const hideModal = useCallback(() => {
    setIsVisible(false);
    setTimeout(() => {
      setModalConfig(null);
    }, 300); // D<PERSON>lai pour l'animation
  }, []);

  // Fonction pour afficher un message d'alerte simple
  const showAlert = useCallback((title: string, message: string, onOk?: () => void) => {
    showModal({
      title,
      message,
      buttons: [
        {
          text: 'OK',
          onPress: () => {
            hideModal();
            onOk?.();
          },
          style: 'default',
        },
      ],
    });
  }, [showModal, hideModal]);

  // Fonction pour afficher une confirmation
  const showConfirm = useCallback((
    title: string,
    message: string,
    onConfirm: () => void,
    onCancel?: () => void,
    confirmText: string = 'OK',
    cancelText: string = 'Annuler'
  ) => {
    showModal({
      title,
      message,
      buttons: [
        {
          text: cancelText,
          onPress: () => {
            hideModal();
            onCancel?.();
          },
          style: 'cancel',
        },
        {
          text: confirmText,
          onPress: () => {
            hideModal();
            onConfirm();
          },
          style: 'default',
        },
      ],
    });
  }, [showModal, hideModal]);

  // Fonction pour afficher une action destructive
  const showDestructiveConfirm = useCallback((
    title: string,
    message: string,
    onConfirm: () => void,
    onCancel?: () => void,
    confirmText: string = 'Supprimer',
    cancelText: string = 'Annuler'
  ) => {
    showModal({
      title,
      message,
      buttons: [
        {
          text: cancelText,
          onPress: () => {
            hideModal();
            onCancel?.();
          },
          style: 'cancel',
        },
        {
          text: confirmText,
          onPress: () => {
            hideModal();
            onConfirm();
          },
          style: 'destructive',
        },
      ],
    });
  }, [showModal, hideModal]);

  // Fonction pour afficher un message de succès de commande
  const showOrderSuccess = useCallback((
    orderDetails: {
      orderId: string;
      customerName: string;
      customerAddress: string;
      customerPhone: string;
      totalAmount: number;
    },
    onGoToStats: () => void,
    onGoToHome: () => void
  ) => {
    const message = `🍕 Commande #${orderDetails.orderId.slice(-6)}
👤 Client: ${orderDetails.customerName}
📍 Adresse: ${orderDetails.customerAddress}
📞 Téléphone: ${orderDetails.customerPhone}
💰 Total: ${orderDetails.totalAmount.toFixed(2)} DT

Le formulaire a été réinitialisé pour une nouvelle commande.`;

    showModal({
      title: '✅ COMMANDE PASSÉE AVEC SUCCÈS !',
      message,
      buttons: [
        {
          text: '🏠 Accueil',
          onPress: () => {
            hideModal();
            onGoToHome();
          },
          style: 'cancel',
        },
        {
          text: '📊 Statistiques',
          onPress: () => {
            hideModal();
            onGoToStats();
          },
          style: 'default',
        },
      ],
    });
  }, [showModal, hideModal]);

  const ModalComponent = modalConfig ? (
    <CustomModal
      visible={isVisible}
      title={modalConfig.title}
      message={modalConfig.message}
      buttons={modalConfig.buttons}
      onClose={hideModal}
    />
  ) : null;

  return {
    showAlert,
    showConfirm,
    showDestructiveConfirm,
    showOrderSuccess,
    showModal,
    hideModal,
    ModalComponent,
  };
};
