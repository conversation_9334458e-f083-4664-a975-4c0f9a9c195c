import React, { useState, useEffect, useCallback } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  RefreshControl,
  Alert,
  TextInput,
  Modal,
} from 'react-native';
import { Calendar } from 'react-native-calendars';
import { StockItem, DailyStock } from '../types';
import { StockService } from '../services/StockService';

interface StockScreenProps {
  navigation: any;
}

export const StockScreen: React.FC<StockScreenProps> = ({ navigation }) => {
  const [stockData, setStockData] = useState<DailyStock | null>(null);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [selectedDate, setSelectedDate] = useState(0); // -1 = J-1, 0 = J, 1 = J+1
  const [customDate, setCustomDate] = useState('');
  const [showDatePicker, setShowDatePicker] = useState(false);
  const [isCustomDateMode, setIsCustomDateMode] = useState(false);
  const [inputValues, setInputValues] = useState<{[key: string]: string}>({});

  const loadStock = useCallback(async (dayOffset: number = 0, specificDate?: string) => {
    try {
      setLoading(true);

      let dateStr: string;

      if (specificDate) {
        // Utiliser la date spécifique
        dateStr = specificDate;
      } else {
        // Calculer la date selon l'offset
        const targetDate = new Date();
        targetDate.setDate(targetDate.getDate() + dayOffset);
        dateStr = targetDate.toISOString().split('T')[0];
      }

      // Charger le stock pour cette date
      const stock = await StockService.getStockForDate(dateStr);
      setStockData(stock);
    } catch (error) {
      console.error('Erreur lors du chargement du stock:', error);
      Alert.alert('Erreur', 'Impossible de charger les données de stock');
    } finally {
      setLoading(false);
    }
  }, []);

  const onRefresh = useCallback(async () => {
    setRefreshing(true);
    if (isCustomDateMode && customDate) {
      await loadStock(0, customDate);
    } else {
      await loadStock(selectedDate);
    }
    setRefreshing(false);
  }, [loadStock, selectedDate, isCustomDateMode, customDate]);

  useEffect(() => {
    // Réinitialiser les valeurs de saisie quand on change de date
    setInputValues({});

    if (isCustomDateMode && customDate) {
      loadStock(0, customDate);
    } else {
      loadStock(selectedDate);
    }
  }, [loadStock, selectedDate, isCustomDateMode, customDate]);

  const changeDate = (offset: number) => {
    setIsCustomDateMode(false);
    setSelectedDate(offset);
  };

  const handleCustomDate = () => {
    setShowDatePicker(true);
  };

  const selectCustomDate = (dateStr: string) => {
    setCustomDate(dateStr);
    setIsCustomDateMode(true);
    setShowDatePicker(false);
  };



  const getDateLabel = () => {
    if (isCustomDateMode && customDate) {
      const date = new Date(customDate);
      const dateStr = date.toLocaleDateString('fr-FR', {
        day: '2-digit',
        month: '2-digit',
        year: 'numeric'
      });
      return `📅 ${dateStr}`;
    }

    const date = new Date();
    date.setDate(date.getDate() + selectedDate);
    const dateStr = date.toLocaleDateString('fr-FR', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric'
    });

    if (selectedDate === -1) return `J-1 (${dateStr})`;
    if (selectedDate === 0) return `Aujourd'hui (${dateStr})`;
    if (selectedDate === 1) return `J+1 (${dateStr})`;
    return dateStr;
  };

  const handleSaveStock = async () => {
    if (!stockData) return;

    try {
      await StockService.saveStockForDate(stockData);
      Alert.alert('Succès', 'Stock sauvegardé avec succès');
    } catch (error) {
      console.error('Erreur lors de la sauvegarde:', error);
      Alert.alert('Erreur', 'Impossible de sauvegarder le stock');
    }
  };

  const handleResetStock = async () => {
    Alert.alert(
      '⚠️ Vider le Stock',
      'Êtes-vous sûr de vouloir vider complètement tout le stock ? Cette action est irréversible.',
      [
        {
          text: 'Annuler',
          style: 'cancel'
        },
        {
          text: 'Vider',
          style: 'destructive',
          onPress: async () => {
            try {
              await StockService.resetStock();
              setInputValues({}); // Vider les valeurs de saisie
              await loadStock(selectedDate); // Recharger
              Alert.alert('✅ Stock Vidé', 'Tout le stock a été vidé. Vous pouvez maintenant commencer la saisie.');
            } catch (error) {
              console.error('Erreur lors de la remise à zéro:', error);
              Alert.alert('❌ Erreur', 'Impossible de vider le stock');
            }
          }
        }
      ]
    );
  };

  const getInputKey = (itemId: string, field: keyof StockItem) => `${itemId}_${field}`;

  const getDisplayValue = (itemId: string, field: keyof StockItem, actualValue: number) => {
    const inputKey = getInputKey(itemId, field);
    // Si l'utilisateur est en train de taper, afficher sa saisie
    if (inputValues[inputKey] !== undefined) {
      return inputValues[inputKey];
    }
    // Sinon afficher la valeur numérique formatée
    return actualValue.toString();
  };

  const updateStockValue = (itemId: string, field: keyof StockItem, value: string) => {
    if (!stockData) return;

    const inputKey = getInputKey(itemId, field);

    // Sauvegarder la valeur tapée par l'utilisateur (avec virgule si elle existe)
    setInputValues(prev => ({ ...prev, [inputKey]: value }));

    // Remplacer la virgule par un point pour la conversion numérique
    const cleanValue = value.replace(',', '.');

    // Permettre les nombres avec jusqu'à 3 décimales
    const numValue = parseFloat(cleanValue) || 0;
    const roundedValue = Math.round(numValue * 1000) / 1000; // Arrondir à 3 décimales

    console.log(`📝 Mise à jour ${field} pour ${itemId}: ${value} → ${roundedValue}`);

    const updatedItems = stockData.items.map(item => {
      if (item.id === itemId) {
        const updatedItem = { ...item, [field]: roundedValue };

        // TOUJOURS recalculer le stock final
        const stockFinal = updatedItem.stockInitial + updatedItem.entrees - updatedItem.sorties;
        updatedItem.stockFinal = Math.round(stockFinal * 1000) / 1000; // Arrondir à 3 décimales

        console.log(`🧮 Calcul pour ${item.name}: ${updatedItem.stockInitial} + ${updatedItem.entrees} - ${updatedItem.sorties} = ${updatedItem.stockFinal}`);

        return updatedItem;
      }
      return item;
    });

    setStockData({ ...stockData, items: updatedItems });
  };

  const handleInputBlur = (itemId: string, field: keyof StockItem) => {
    const inputKey = getInputKey(itemId, field);
    // Quand l'utilisateur quitte le champ, effacer la valeur de saisie
    // pour revenir à l'affichage numérique standard
    setInputValues(prev => {
      const newValues = { ...prev };
      delete newValues[inputKey];
      return newValues;
    });
  };

  const formatDate = (dateStr: string) => {
    const date = new Date(dateStr);
    return new Intl.DateTimeFormat('fr-FR', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric'
    }).format(date);
  };

  if (loading) {
    return (
      <View style={styles.container}>
        <View style={styles.header}>
          <TouchableOpacity onPress={() => navigation.goBack()}>
            <Text style={styles.backButton}>← Retour</Text>
          </TouchableOpacity>
          <Text style={styles.title}>Gestion du Stock</Text>
        </View>
        <View style={styles.loadingContainer}>
          <Text style={styles.loadingText}>Chargement du stock...</Text>
        </View>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity onPress={() => navigation.goBack()}>
          <Text style={styles.backButton}>← Retour</Text>
        </TouchableOpacity>
        <Text style={styles.title}>Gestion du Stock</Text>
        <View style={styles.headerButtons}>
          <TouchableOpacity style={styles.resetButton} onPress={handleResetStock}>
            <Text style={styles.resetButtonText}>🗑️ Vider</Text>
          </TouchableOpacity>
          <TouchableOpacity style={styles.saveButton} onPress={handleSaveStock}>
            <Text style={styles.saveButtonText}>💾 Sauvegarder</Text>
          </TouchableOpacity>
        </View>
      </View>

      <ScrollView
        style={styles.content}
        refreshControl={<RefreshControl refreshing={refreshing} onRefresh={onRefresh} />}
      >
        {/* Filtrage par jour */}
        <View style={styles.dateFilterContainer}>
          <TouchableOpacity
            style={[styles.dateFilterButton, selectedDate === -1 && !isCustomDateMode && styles.dateFilterActive]}
            onPress={() => changeDate(-1)}
          >
            <Text style={[styles.dateFilterText, selectedDate === -1 && !isCustomDateMode && styles.dateFilterTextActive]}>
              J-1
            </Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={[styles.dateFilterButton, selectedDate === 0 && !isCustomDateMode && styles.dateFilterActive]}
            onPress={() => changeDate(0)}
          >
            <Text style={[styles.dateFilterText, selectedDate === 0 && !isCustomDateMode && styles.dateFilterTextActive]}>
              Aujourd'hui
            </Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={[styles.dateFilterButton, selectedDate === 1 && !isCustomDateMode && styles.dateFilterActive]}
            onPress={() => changeDate(1)}
          >
            <Text style={[styles.dateFilterText, selectedDate === 1 && !isCustomDateMode && styles.dateFilterTextActive]}>
              J+1
            </Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={[styles.dateFilterButton, styles.customDateButton, isCustomDateMode && styles.dateFilterActive]}
            onPress={handleCustomDate}
          >
            <Text style={[styles.dateFilterText, isCustomDateMode && styles.dateFilterTextActive]}>
              📅 Date
            </Text>
          </TouchableOpacity>
        </View>

        {/* Date du stock */}
        <View style={styles.dateContainer}>
          <Text style={styles.dateText}>{getDateLabel()}</Text>
        </View>

        {/* En-tête du tableau */}
        <View style={styles.tableHeader}>
          <Text style={[styles.tableHeaderText, styles.productColumn]}>Produit</Text>
          <Text style={[styles.tableHeaderText, styles.numberColumn]}>Stock Initial</Text>
          <Text style={[styles.tableHeaderText, styles.numberColumn]}>Entrées</Text>
          <Text style={[styles.tableHeaderText, styles.numberColumn]}>Consommation</Text>
          <Text style={[styles.tableHeaderText, styles.numberColumn]}>Stock Final</Text>
        </View>

        {/* Lignes du tableau */}
        {stockData?.items.map((item) => (
          <View key={item.id} style={styles.tableRow}>
            {/* Nom du produit */}
            <View style={styles.productColumn}>
              <Text style={styles.productName}>{item.name}</Text>
              <Text style={styles.productUnit}>({item.unit})</Text>
            </View>

            {/* Stock Initial */}
            <View style={styles.numberColumn}>
              <TextInput
                style={styles.numberInput}
                value={getDisplayValue(item.id, 'stockInitial', item.stockInitial)}
                onChangeText={(value) => updateStockValue(item.id, 'stockInitial', value)}
                onBlur={() => handleInputBlur(item.id, 'stockInitial')}
                keyboardType="decimal-pad"
                placeholder="0,000"
                selectTextOnFocus={true}
              />
            </View>

            {/* Entrées */}
            <View style={styles.numberColumn}>
              <TextInput
                style={[styles.numberInput, styles.entreesInput]}
                value={getDisplayValue(item.id, 'entrees', item.entrees)}
                onChangeText={(value) => updateStockValue(item.id, 'entrees', value)}
                onBlur={() => handleInputBlur(item.id, 'entrees')}
                keyboardType="decimal-pad"
                placeholder="0,000"
                selectTextOnFocus={true}
              />
            </View>

            {/* Consommation */}
            <View style={styles.numberColumn}>
              <TextInput
                style={[styles.numberInput, styles.sortiesInput]}
                value={getDisplayValue(item.id, 'sorties', item.sorties)}
                onChangeText={(value) => updateStockValue(item.id, 'sorties', value)}
                onBlur={() => handleInputBlur(item.id, 'sorties')}
                keyboardType="decimal-pad"
                placeholder="0,000"
                selectTextOnFocus={true}
              />
            </View>

            {/* Stock Final (calculé automatiquement) */}
            <View style={styles.numberColumn}>
              <View style={styles.stockFinalDisplay}>
                <Text style={styles.stockFinalText}>{item.stockFinal.toFixed(3)}</Text>
              </View>
            </View>
          </View>
        ))}

      </ScrollView>

      {/* Modal de sélection de date avec calendrier */}
      <Modal
        animationType="slide"
        transparent={true}
        visible={showDatePicker}
        onRequestClose={() => setShowDatePicker(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={styles.modalContent}>
            <Text style={styles.modalTitle}>Choisir une date</Text>

            <Calendar
              onDayPress={(day) => selectCustomDate(day.dateString)}
              markedDates={{
                [customDate]: { selected: true, selectedColor: '#3498db' }
              }}
              theme={{
                backgroundColor: '#ffffff',
                calendarBackground: '#ffffff',
                textSectionTitleColor: '#b6c1cd',
                selectedDayBackgroundColor: '#3498db',
                selectedDayTextColor: '#ffffff',
                todayTextColor: '#3498db',
                dayTextColor: '#2d4150',
                textDisabledColor: '#d9e1e8',
                dotColor: '#00adf5',
                selectedDotColor: '#ffffff',
                arrowColor: '#3498db',
                disabledArrowColor: '#d9e1e8',
                monthTextColor: '#2d4150',
                indicatorColor: '#3498db',
                textDayFontWeight: '300',
                textMonthFontWeight: 'bold',
                textDayHeaderFontWeight: '300',
                textDayFontSize: 16,
                textMonthFontSize: 16,
                textDayHeaderFontSize: 13
              }}
            />

            <TouchableOpacity
              style={styles.cancelButton}
              onPress={() => setShowDatePicker(false)}
            >
              <Text style={styles.cancelButtonText}>Fermer</Text>
            </TouchableOpacity>
          </View>
        </View>
      </Modal>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  header: {
    backgroundColor: '#2c3e50',
    paddingTop: 50,
    paddingBottom: 20,
    paddingHorizontal: 16,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  backButton: {
    color: '#3498db',
    fontSize: 16,
    fontWeight: '600',
  },
  title: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#fff',
    flex: 1,
    textAlign: 'center',
  },
  headerButtons: {
    flexDirection: 'row',
    gap: 8,
  },
  resetButton: {
    backgroundColor: '#e74c3c',
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 6,
  },
  resetButtonText: {
    color: '#fff',
    fontSize: 14,
    fontWeight: '600',
  },
  saveButton: {
    backgroundColor: '#27ae60',
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 6,
  },
  saveButtonText: {
    color: '#fff',
    fontSize: 14,
    fontWeight: '600',
  },
  content: {
    flex: 1,
    padding: 16,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    fontSize: 16,
    color: '#666',
  },
  dateFilterContainer: {
    flexDirection: 'row',
    marginBottom: 16,
    gap: 8,
  },
  dateFilterButton: {
    flex: 1,
    paddingVertical: 10,
    paddingHorizontal: 12,
    borderRadius: 8,
    borderWidth: 2,
    borderColor: '#3498db',
    backgroundColor: '#fff',
    alignItems: 'center',
  },
  dateFilterActive: {
    backgroundColor: '#3498db',
  },
  dateFilterText: {
    fontSize: 14,
    fontWeight: '600',
    color: '#3498db',
  },
  dateFilterTextActive: {
    color: '#fff',
  },
  customDateButton: {
    borderColor: '#e67e22',
  },
  dateContainer: {
    backgroundColor: '#34495e',
    padding: 12,
    borderRadius: 8,
    marginBottom: 16,
  },
  dateText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
    textAlign: 'center',
  },
  // Styles du tableau
  tableHeader: {
    flexDirection: 'row',
    backgroundColor: '#34495e',
    paddingVertical: 12,
    paddingHorizontal: 8,
    borderRadius: 8,
    marginBottom: 2,
  },
  tableHeaderText: {
    color: '#fff',
    fontSize: 12,
    fontWeight: 'bold',
    textAlign: 'center',
  },
  tableRow: {
    flexDirection: 'row',
    backgroundColor: '#fff',
    paddingVertical: 8,
    paddingHorizontal: 8,
    marginBottom: 2,
    alignItems: 'center',
    borderRadius: 4,
  },
  productColumn: {
    flex: 2,
    paddingRight: 8,
  },
  numberColumn: {
    flex: 1,
    alignItems: 'center',
  },
  productName: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#2c3e50',
  },
  productUnit: {
    fontSize: 12,
    color: '#666',
  },
  numberInput: {
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 4,
    paddingHorizontal: 8,
    paddingVertical: 6,
    fontSize: 14,
    textAlign: 'center',
    backgroundColor: '#fff',
    minWidth: 60,
  },
  entreesInput: {
    borderColor: '#27ae60',
    backgroundColor: '#f8fff8',
  },
  sortiesInput: {
    borderColor: '#e74c3c',
    backgroundColor: '#fff8f8',
  },
  stockFinalDisplay: {
    backgroundColor: '#3498db',
    paddingHorizontal: 8,
    paddingVertical: 6,
    borderRadius: 4,
    minWidth: 60,
  },
  stockFinalText: {
    color: '#fff',
    fontSize: 14,
    fontWeight: 'bold',
    textAlign: 'center',
  },
  // Styles du modal
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContent: {
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 20,
    width: '90%',
    maxWidth: 400,
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#2c3e50',
    marginBottom: 20,
    textAlign: 'center',
  },

  cancelButton: {
    padding: 12,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#ddd',
    alignItems: 'center',
    marginTop: 10,
  },
  cancelButtonText: {
    fontSize: 16,
    color: '#666',
    fontWeight: '600',
  },
});
