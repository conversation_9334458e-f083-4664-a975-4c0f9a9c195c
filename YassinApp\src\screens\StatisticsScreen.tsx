import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Dimensions,
  FlatList,
  Alert,
  Modal,
} from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { Calendar } from 'react-native-calendars';
import { Colors, FontSizes, Spacing, BorderRadius, Shadows } from '../styles/theme';
import { Statistics, DailyStats, Order, OrderDetail } from '../types';
import { BASE_PIZZAS, cleanPizzaName as cleanPizzaNameFromDB } from '../data/pizzaDatabase';

interface StatisticsScreenProps {
  navigation: any;
}

const { width } = Dimensions.get('window');

export const StatisticsScreen: React.FC<StatisticsScreenProps> = ({ navigation }) => {
  const [selectedView, setSelectedView] = useState<'overview' | 'daily'>('daily');
  const [dailyStats, setDailyStats] = useState<DailyStats[]>([]);
  const [filteredDailyStats, setFilteredDailyStats] = useState<DailyStats[]>([]);
  const [selectedDate, setSelectedDate] = useState<string>(new Date().toISOString().split('T')[0]); // Aujourd'hui par défaut
  const [showCalendar, setShowCalendar] = useState(false);
  const [selectedMonth, setSelectedMonth] = useState<string>(new Date().toISOString().slice(0, 7)); // YYYY-MM
  const [showMonthPicker, setShowMonthPicker] = useState(false);
  const [statistics, setStatistics] = useState<Statistics>({
    totalOrders: 0,
    totalRevenue: 0,
    averageOrderValue: 0,
    todayOrders: 0,
    monthlyRevenue: 0,
    popularPizzas: [],
  });

  useEffect(() => {
    loadRealOrders();
  }, []);

  // Recharger les données périodiquement
  useEffect(() => {
    const interval = setInterval(() => {
      loadRealOrders();
    }, 2000); // Recharger toutes les 2 secondes

    return () => clearInterval(interval);
  }, []);

  useEffect(() => {
    filterDataByDate();
  }, [selectedDate, dailyStats]);

  useEffect(() => {
    // Recalculer les stats d'ensemble quand le mois change
    if (dailyStats.length > 0) {
      const allOrders = dailyStats.flatMap(day => day.orders);
      calculateOverviewStats(allOrders);
    }
  }, [selectedMonth, dailyStats]);

  const filterDataByDate = () => {
    const filtered = dailyStats
      .filter(day => day.date === selectedDate)
      .map(day => {
        // Filtrer les commandes annulées
        const activeOrders = day.orders.filter(order => order.status !== 'Annulée');

        return {
          ...day,
          orders: activeOrders,
          totalOrders: activeOrders.length,
          totalRevenue: activeOrders.reduce((sum, order) => sum + order.totalAmount, 0),
          averageOrderValue: activeOrders.length > 0
            ? activeOrders.reduce((sum, order) => sum + order.totalAmount, 0) / activeOrders.length
            : 0
        };
      })
      .filter(day => day.orders.length > 0); // Ne garder que les jours avec des commandes actives

    setFilteredDailyStats(filtered);
  };

  const loadRealOrders = async () => {
    try {
      // Charger les commandes sauvegardées
      const savedOrders = await AsyncStorage.getItem('orders');
      const orders: Order[] = savedOrders ? JSON.parse(savedOrders) : [];

      // Grouper les commandes par date
      const groupedByDate: { [key: string]: Order[] } = {};

      orders.forEach(order => {
        const orderDate = order.date.split('T')[0]; // Extraire la date (YYYY-MM-DD)
        if (!groupedByDate[orderDate]) {
          groupedByDate[orderDate] = [];
        }
        groupedByDate[orderDate].push(order);
      });

      // Convertir en format DailyStats
      const dailyStatsData: DailyStats[] = Object.keys(groupedByDate)
        .sort((a, b) => b.localeCompare(a)) // Trier par date décroissante
        .map(date => {
          const dayOrders = groupedByDate[date].filter(order => order.status !== 'Annulée');
          const totalRevenue = dayOrders.reduce((sum, order) => sum + order.totalAmount, 0);

          return {
            date,
            orders: dayOrders,
            totalOrders: dayOrders.length,
            totalRevenue,
            averageOrderValue: dayOrders.length > 0 ? totalRevenue / dayOrders.length : 0
          };
        });

      setDailyStats(dailyStatsData);

      // Calculer les statistiques d'ensemble
      calculateOverviewStats(orders);

      // Si aucune commande, afficher un message
      if (orders.length === 0) {
        console.log('Aucune commande trouvée. Commencez par passer une commande.');
      }

    } catch (error) {
      console.error('Erreur lors du chargement des commandes:', error);
      setDailyStats([]);
    }
  };

  const calculateOverviewStats = (allOrders: Order[]) => {
    // Filtrer les commandes du mois sélectionné
    const monthOrders = allOrders.filter(order => {
      const orderMonth = order.date.slice(0, 7); // YYYY-MM
      return orderMonth === selectedMonth;
    });



    // Calculer les totaux du mois
    const totalRevenue = monthOrders.reduce((sum, order) => sum + order.totalAmount, 0);
    const averageOrderValue = monthOrders.length > 0 ? totalRevenue / monthOrders.length : 0;

    // Calculer les pizzas populaires du mois
    const pizzaCount: { [key: string]: number } = {};

    monthOrders.forEach(order => {
      order.items.forEach(item => {
        // Si c'est une offre, extraire les pizzas des choix
        if (item.isOffer && item.offerItems) {
          item.offerItems.forEach(offerItem => {
            if (offerItem.name && offerItem.name.toLowerCase().includes('pizza')) {
              const cleanName = cleanPizzaNameFromDB(offerItem.choice || 'Pizza');
              if (cleanName) {
                pizzaCount[cleanName] = (pizzaCount[cleanName] || 0) + item.quantity;
              }
            }
          });
        } else {
          // Pizza normale - nettoyer et identifier
          const cleanName = cleanPizzaNameFromDB(item.name);
          if (cleanName) {
            pizzaCount[cleanName] = (pizzaCount[cleanName] || 0) + item.quantity;
          }
        }
      });
    });

    // Trier les pizzas par popularité
    const popularPizzas = Object.entries(pizzaCount)
      .map(([name, count]) => ({ name, count }))
      .sort((a, b) => b.count - a.count)
      .slice(0, 5); // Top 5

    // Mettre à jour les statistiques
    setStatistics({
      totalOrders: monthOrders.length,
      totalRevenue,
      averageOrderValue,
      todayOrders: 0, // Plus utilisé mais gardé pour compatibilité
      monthlyRevenue: totalRevenue,
      popularPizzas,
    });
  };

  const resetAllData = async () => {
    Alert.alert(
      'Réinitialiser les données',
      'Êtes-vous sûr de vouloir supprimer toutes les commandes ? Cette action est irréversible.',
      [
        { text: 'Annuler', style: 'cancel' },
        {
          text: 'Oui, supprimer tout',
          style: 'destructive',
          onPress: async () => {
            try {
              await AsyncStorage.removeItem('orders');
              await AsyncStorage.removeItem('orderCounter');
              setDailyStats([]);
              setFilteredDailyStats([]);
              Alert.alert('Données supprimées', 'Toutes les commandes ont été supprimées. L\'application est maintenant à zéro.');
            } catch (error) {
              console.error('Erreur lors de la suppression:', error);
              Alert.alert('Erreur', 'Impossible de supprimer les données.');
            }
          }
        }
      ]
    );
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    const today = new Date();
    const yesterday = new Date(today);
    yesterday.setDate(yesterday.getDate() - 1);

    if (date.toDateString() === today.toDateString()) {
      return "Aujourd'hui";
    } else if (date.toDateString() === yesterday.toDateString()) {
      return "Hier";
    } else {
      return date.toLocaleDateString('fr-FR', {
        weekday: 'long',
        day: 'numeric',
        month: 'long'
      });
    }
  };

  const formatTime = (dateString: string) => {
    return new Date(dateString).toLocaleTimeString('fr-FR', {
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const handleOrderPress = (order: Order, date: string) => {
    const orderDetail: OrderDetail = {
      ...order,
      formattedDate: formatDate(date),
      formattedTime: formatTime(order.date)
    };
    navigation.navigate('OrderDetail', {
      order: orderDetail,
      onOrderCancelled: async (orderId: string, orderNumber: string) => {
        try {
          // Supprimer la commande de AsyncStorage
          const savedOrders = await AsyncStorage.getItem('orders');
          const orders: Order[] = savedOrders ? JSON.parse(savedOrders) : [];

          // Filtrer pour supprimer la commande annulée
          const updatedOrders = orders.filter(order => order.id !== orderId);

          // Sauvegarder la liste mise à jour
          await AsyncStorage.setItem('orders', JSON.stringify(updatedOrders));

          // Recharger les données depuis AsyncStorage
          await loadRealOrders();

          console.log(`Commande #${orderNumber} supprimée définitivement`);
        } catch (error) {
          console.error('Erreur lors de la suppression de la commande:', error);
        }
      }
    });
  };

  const handleCancelOrder = async (orderId: string, orderNumber: string) => {
    Alert.alert(
      'Annuler la commande',
      `Êtes-vous sûr de vouloir supprimer définitivement la commande #${orderNumber} ?\n\nCette action est irréversible.`,
      [
        { text: 'Non', style: 'cancel' },
        {
          text: 'Oui, supprimer',
          style: 'destructive',
          onPress: async () => {
            try {
              // Supprimer la commande de AsyncStorage
              const savedOrders = await AsyncStorage.getItem('orders');
              const orders: Order[] = savedOrders ? JSON.parse(savedOrders) : [];

              // Filtrer pour supprimer la commande annulée
              const updatedOrders = orders.filter(order => order.id !== orderId);

              // Sauvegarder la liste mise à jour
              await AsyncStorage.setItem('orders', JSON.stringify(updatedOrders));

              // Recharger les données depuis AsyncStorage
              await loadRealOrders();

              Alert.alert('Commande supprimée', `La commande #${orderNumber} a été supprimée définitivement.`);
            } catch (error) {
              console.error('Erreur lors de la suppression:', error);
              Alert.alert('Erreur', 'Impossible de supprimer la commande.');
            }
          }
        }
      ]
    );
  };

  const getTotalSummary = () => {
    const totalOrders = filteredDailyStats.reduce((sum, day) => sum + day.totalOrders, 0);
    const totalRevenue = filteredDailyStats.reduce((sum, day) => sum + day.totalRevenue, 0);
    return { totalOrders, totalRevenue };
  };



  const StatCard = ({ title, value, subtitle, color = Colors.primary }: {
    title: string;
    value: string;
    subtitle?: string;
    color?: string;
  }) => (
    <View style={[styles.statCard, { borderLeftColor: color }]}>
      <Text style={styles.statTitle}>{title}</Text>
      <Text style={[styles.statValue, { color }]}>{value}</Text>
      {subtitle && <Text style={styles.statSubtitle}>{subtitle}</Text>}
    </View>
  );

  const ViewButton = ({ view, label }: { view: 'overview' | 'daily'; label: string }) => (
    <TouchableOpacity
      style={[
        styles.viewButton,
        selectedView === view && styles.viewButtonActive
      ]}
      onPress={() => setSelectedView(view)}
    >
      <Text
        style={[
          styles.viewButtonText,
          selectedView === view && styles.viewButtonTextActive
        ]}
      >
        {label}
      </Text>
    </TouchableOpacity>
  );



  const OrderCard = ({ order, date }: { order: Order; date: string }) => (
    <TouchableOpacity
      style={styles.orderCard}
      onPress={() => handleOrderPress(order, date)}
    >
      <View style={styles.orderHeader}>
        <View style={styles.orderInfo}>
          <Text style={styles.orderNumber}>#{order.orderNumber}</Text>
          <Text style={styles.orderTime}>{formatTime(order.date)}</Text>
        </View>
      </View>

      <View style={styles.orderDetails}>
        <Text style={styles.customerName}>👤 {order.customer.name}</Text>
        <Text style={styles.orderItems}>
          {order.items.length} article{order.items.length > 1 ? 's' : ''}
        </Text>
      </View>

      <View style={styles.orderFooter}>
        <View style={styles.priceBreakdown}>
          <Text style={styles.subtotal}>Sous-total: {order.subtotalAmount.toFixed(2)} DT</Text>
          {order.deliveryFee > 0 && (
            <Text style={styles.delivery}>Livraison: {order.deliveryFee.toFixed(2)} DT</Text>
          )}
        </View>
        <Text style={styles.totalAmount}>{order.totalAmount.toFixed(2)} DT</Text>
      </View>
    </TouchableOpacity>
  );

  const DateFilter = () => {
    const today = new Date().toISOString().split('T')[0];
    const isToday = selectedDate === today;

    const handleDateSelection = (date: string) => {
      setSelectedDate(date);
      setShowCalendar(false);
    };

    const openCalendar = () => {
      setShowCalendar(true);
    };

    return (
      <View style={styles.filterContainer}>
        <Text style={styles.filterLabel}>📅 Sélectionner une date :</Text>
        <View style={styles.dateFilterButtons}>
          <TouchableOpacity
            style={[
              styles.dateFilterButton,
              isToday && styles.dateFilterButtonActive
            ]}
            onPress={() => handleDateSelection(today)}
          >
            <Text
              style={[
                styles.dateFilterText,
                isToday && styles.dateFilterTextActive
              ]}
            >
              📅 Aujourd'hui
            </Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={[
              styles.dateFilterButton,
              !isToday && styles.dateFilterButtonActive
            ]}
            onPress={openCalendar}
          >
            <Text
              style={[
                styles.dateFilterText,
                !isToday && styles.dateFilterTextActive
              ]}
            >
              🗓️ {isToday ? 'Choisir date' : formatDate(selectedDate)}
            </Text>
          </TouchableOpacity>
        </View>
      </View>
    );
  };

  const CalendarModal = () => (
    <Modal
      visible={showCalendar}
      transparent={true}
      animationType="slide"
      onRequestClose={() => setShowCalendar(false)}
    >
      <View style={styles.modalOverlay}>
        <View style={styles.calendarContainer}>
          <View style={styles.calendarHeader}>
            <Text style={styles.calendarTitle}>Choisir une date</Text>
            <TouchableOpacity
              style={styles.closeButton}
              onPress={() => setShowCalendar(false)}
            >
              <Text style={styles.closeButtonText}>✕</Text>
            </TouchableOpacity>
          </View>

          <Calendar
            onDayPress={(day) => {
              setSelectedDate(day.dateString);
              setShowCalendar(false);
            }}
            markedDates={{
              [selectedDate]: {
                selected: true,
                selectedColor: Colors.primary,
                selectedTextColor: Colors.textOnPrimary
              }
            }}
            maxDate={new Date().toISOString().split('T')[0]}
            minDate={'2025-07-01'}
            theme={{
              backgroundColor: Colors.surface,
              calendarBackground: Colors.surface,
              textSectionTitleColor: Colors.textPrimary,
              selectedDayBackgroundColor: Colors.primary,
              selectedDayTextColor: Colors.textOnPrimary,
              todayTextColor: Colors.primary,
              dayTextColor: Colors.textPrimary,
              textDisabledColor: Colors.textSecondary,
              dotColor: Colors.primary,
              selectedDotColor: Colors.textOnPrimary,
              arrowColor: Colors.primary,
              monthTextColor: Colors.textPrimary,
              indicatorColor: Colors.primary,
              textDayFontFamily: 'System',
              textMonthFontFamily: 'System',
              textDayHeaderFontFamily: 'System',
              textDayFontWeight: '400',
              textMonthFontWeight: 'bold',
              textDayHeaderFontWeight: '600',
              textDayFontSize: 16,
              textMonthFontSize: 18,
              textDayHeaderFontSize: 14
            }}
          />

          <View style={styles.calendarActions}>
            <TouchableOpacity
              style={styles.todayButton}
              onPress={() => {
                const today = new Date().toISOString().split('T')[0];
                setSelectedDate(today);
                setShowCalendar(false);
              }}
            >
              <Text style={styles.todayButtonText}>Aujourd'hui</Text>
            </TouchableOpacity>
          </View>
        </View>
      </View>
    </Modal>
  );

  const generateAvailableMonths = () => {
    const months = [];
    const currentDate = new Date();

    // Ajouter les 12 derniers mois à partir du mois actuel
    for (let i = 0; i < 12; i++) {
      const date = new Date(currentDate.getFullYear(), currentDate.getMonth() - i, 1);
      const monthString = date.toISOString().slice(0, 7);
      months.push(monthString);
    }

    // Ajouter aussi les 6 prochains mois
    for (let i = 1; i <= 6; i++) {
      const date = new Date(currentDate.getFullYear(), currentDate.getMonth() + i, 1);
      const monthString = date.toISOString().slice(0, 7);
      months.push(monthString);
    }

    // Trier par ordre décroissant (plus récent en premier)
    return months.sort((a, b) => b.localeCompare(a));
  };

  const MonthFilter = () => {
    const currentMonth = new Date().toISOString().slice(0, 7);
    const isCurrentMonth = selectedMonth === currentMonth;

    const openMonthPicker = () => {
      const months = generateAvailableMonths();

      Alert.alert(
        'Choisir un mois',
        'Sélectionnez le mois à consulter :',
        [
          ...months.map(month => ({
            text: formatMonth(month),
            onPress: () => setSelectedMonth(month)
          })),
          { text: 'Annuler', style: 'cancel' }
        ]
      );
    };

    return (
      <View style={styles.filterContainer}>
        <Text style={styles.filterLabel}>📅 Filtrer par mois :</Text>
        <View style={styles.dateFilterButtons}>
          <TouchableOpacity
            style={[
              styles.dateFilterButton,
              isCurrentMonth && styles.dateFilterButtonActive
            ]}
            onPress={() => setSelectedMonth(currentMonth)}
          >
            <Text
              style={[
                styles.dateFilterText,
                isCurrentMonth && styles.dateFilterTextActive
              ]}
            >
              📅 Ce mois
            </Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={[
              styles.dateFilterButton,
              !isCurrentMonth && styles.dateFilterButtonActive
            ]}
            onPress={openMonthPicker}
          >
            <Text
              style={[
                styles.dateFilterText,
                !isCurrentMonth && styles.dateFilterTextActive
              ]}
            >
              🗓️ {isCurrentMonth ? 'Autre mois' : formatMonth(selectedMonth)}
            </Text>
          </TouchableOpacity>
        </View>
      </View>
    );
  };

  const formatMonth = (monthString: string) => {
    const [year, month] = monthString.split('-');
    const monthNames = [
      'Janvier', 'Février', 'Mars', 'Avril', 'Mai', 'Juin',
      'Juillet', 'Août', 'Septembre', 'Octobre', 'Novembre', 'Décembre'
    ];
    return `${monthNames[parseInt(month) - 1]} ${year}`;
  };

  const SummaryCard = () => {
    const { totalOrders, totalRevenue } = getTotalSummary();
    return (
      <View style={styles.summaryCard}>
        <Text style={styles.summaryTitle}>📊 Stat du jour</Text>
        <View style={styles.summaryContent}>
          <View style={styles.summaryItem}>
            <Text style={styles.summaryLabel}>Total Commandes</Text>
            <Text style={styles.summaryValue}>{totalOrders}</Text>
          </View>
          <View style={styles.summaryItem}>
            <Text style={styles.summaryLabel}>Total Revenus</Text>
            <Text style={styles.summaryValueAmount}>{totalRevenue.toFixed(2)} DT</Text>
          </View>
        </View>
      </View>
    );
  };

  const DaySection = ({ dayStats }: { dayStats: DailyStats }) => (
    <View style={styles.daySection}>
      <View style={styles.dayHeader}>
        <Text style={styles.dayTitle}>{formatDate(dayStats.date)}</Text>
        <View style={styles.dayStats}>
          <Text style={styles.dayStatsText}>
            {dayStats.totalOrders} commande{dayStats.totalOrders > 1 ? 's' : ''} • {dayStats.totalRevenue.toFixed(2)} DT
          </Text>
        </View>
      </View>

      {dayStats.orders.map((order) => (
        <OrderCard key={order.id} order={order} date={dayStats.date} />
      ))}
    </View>
  );

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity onPress={() => navigation.goBack()} style={styles.backButton}>
          <Text style={styles.backButtonText}>← Retour</Text>
        </TouchableOpacity>
        <Text style={styles.title}>Statistiques</Text>
      </View>

      {/* View Selection */}
      <View style={styles.viewContainer}>
        <ViewButton view="daily" label="📅 Commandes par jour" />
        <ViewButton view="overview" label="📊 Vue d'ensemble" />
      </View>

      {selectedView === 'daily' ? (
        <ScrollView style={styles.content}>
          <View style={styles.dailyHeader}>
            <Text style={styles.dailyTitle}>Historique des Commandes</Text>
            <Text style={styles.dailySubtitle}>Cliquez sur une commande pour voir les détails</Text>

            <View style={styles.actionButtonsContainer}>
              <TouchableOpacity style={styles.refreshButton} onPress={loadRealOrders}>
                <Text style={styles.refreshButtonText}>🔄 Actualiser</Text>
              </TouchableOpacity>

              <TouchableOpacity style={styles.resetButton} onPress={resetAllData}>
                <Text style={styles.resetButtonText}>🗑️ Réinitialiser</Text>
              </TouchableOpacity>
            </View>
          </View>

          <DateFilter />
          <SummaryCard />

          {filteredDailyStats.length > 0 ? (
            filteredDailyStats.map((dayStats) => (
              <DaySection key={dayStats.date} dayStats={dayStats} />
            ))
          ) : (
            <View style={styles.noDataContainer}>
              <Text style={styles.noDataIcon}>📋</Text>
              <Text style={styles.noDataTitle}>Aucune commande trouvée</Text>
              <Text style={styles.noDataText}>
                {dailyStats.length === 0
                  ? "Aucune commande n'a encore été passée.\nCommencez par créer une commande depuis l'écran principal."
                  : "Aucune commande pour cette date.\nSélectionnez une autre date ou passez une nouvelle commande."
                }
              </Text>
              <TouchableOpacity
                style={styles.goToOrderButton}
                onPress={() => navigation.navigate('Admin')}
              >
                <Text style={styles.goToOrderButtonText}>➕ Passer une commande</Text>
              </TouchableOpacity>
            </View>
          )}
        </ScrollView>
      ) : (
        <ScrollView style={styles.content}>
          <MonthFilter />

          {/* Main Statistics */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>📊 Statistiques - {formatMonth(selectedMonth)}</Text>

            <View style={styles.statsGrid}>
              <StatCard
                title="Total Commandes"
                value={statistics.totalOrders.toString()}
                subtitle={formatMonth(selectedMonth)}
                color={Colors.primary}
              />

              <StatCard
                title="Chiffre d'affaires"
                value={`${statistics.totalRevenue.toFixed(2)} DT`}
                subtitle={formatMonth(selectedMonth)}
                color={Colors.success}
              />

              <StatCard
                title="Panier moyen"
                value={`${statistics.averageOrderValue.toFixed(2)} DT`}
                subtitle="par commande"
                color={Colors.accent}
              />
            </View>
          </View>

          {/* Popular Pizzas */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Pizzas populaires</Text>

            {statistics.popularPizzas.map((pizza, index) => (
              <View key={pizza.name} style={styles.pizzaRankItem}>
                <View style={styles.rankBadge}>
                  <Text style={styles.rankNumber}>{index + 1}</Text>
                </View>

                <View style={styles.pizzaInfo}>
                  <Text style={styles.pizzaName}>{pizza.name}</Text>
                  <Text style={styles.pizzaCount}>{pizza.count} commandes</Text>
                </View>

                <View style={styles.progressBarContainer}>
                  <View
                    style={[
                      styles.progressBar,
                      {
                        width: `${(pizza.count / statistics.popularPizzas[0].count) * 100}%`,
                        backgroundColor: index === 0 ? Colors.primary : Colors.lightGray
                      }
                    ]}
                  />
                </View>
              </View>
            ))}
          </View>


        </ScrollView>
      )}

      <CalendarModal />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.backgroundSolid,
  },
  header: {
    backgroundColor: Colors.secondary,
    paddingTop: 50,
    paddingBottom: 15,
    paddingHorizontal: 16,
    flexDirection: 'row',
    alignItems: 'center',
    borderBottomLeftRadius: 20,
    borderBottomRightRadius: 20,
    ...Shadows.medium,
  },
  backButton: {
    marginRight: 10,
  },
  backButtonText: {
    color: Colors.textOnDark,
    fontSize: FontSizes.md,
    fontWeight: '600',
  },
  title: {
    fontSize: FontSizes.xl,
    fontWeight: 'bold',
    color: Colors.textOnDark,
    flex: 1,
  },
  content: {
    flex: 1,
    padding: Spacing.lg,
  },
  viewContainer: {
    flexDirection: 'row',
    margin: Spacing.lg,
    backgroundColor: Colors.surface,
    borderRadius: BorderRadius.large,
    padding: Spacing.xs,
    ...Shadows.small,
  },
  viewButton: {
    flex: 1,
    paddingVertical: Spacing.md,
    paddingHorizontal: Spacing.md,
    borderRadius: BorderRadius.medium,
    alignItems: 'center',
  },
  viewButtonActive: {
    backgroundColor: Colors.primary,
  },
  viewButtonText: {
    fontSize: FontSizes.sm,
    fontWeight: '600',
    color: Colors.textSecondary,
  },
  viewButtonTextActive: {
    color: Colors.textOnPrimary,
  },
  dailyHeader: {
    marginBottom: Spacing.lg,
    alignItems: 'center',
  },
  dailyTitle: {
    fontSize: FontSizes.xl,
    fontWeight: 'bold',
    color: Colors.textPrimary,
    marginBottom: Spacing.xs,
  },
  dailySubtitle: {
    fontSize: FontSizes.md,
    color: Colors.textSecondary,
    textAlign: 'center',
    marginBottom: Spacing.md,
  },
  actionButtonsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    gap: Spacing.md,
  },
  refreshButton: {
    backgroundColor: Colors.accent,
    paddingHorizontal: Spacing.lg,
    paddingVertical: Spacing.md,
    borderRadius: BorderRadius.large,
    alignItems: 'center',
    flex: 1,
    ...Shadows.medium,
  },
  refreshButtonText: {
    color: Colors.textOnPrimary,
    fontSize: FontSizes.sm,
    fontWeight: '600',
  },
  resetButton: {
    backgroundColor: Colors.danger,
    paddingHorizontal: Spacing.lg,
    paddingVertical: Spacing.md,
    borderRadius: BorderRadius.large,
    alignItems: 'center',
    flex: 1,
    ...Shadows.medium,
  },
  resetButtonText: {
    color: Colors.textOnPrimary,
    fontSize: FontSizes.sm,
    fontWeight: '600',
  },
  filterContainer: {
    backgroundColor: Colors.surface,
    borderRadius: BorderRadius.large,
    padding: Spacing.lg,
    marginBottom: Spacing.lg,
    ...Shadows.small,
  },
  filterLabel: {
    fontSize: FontSizes.md,
    fontWeight: '600',
    color: Colors.textPrimary,
    marginBottom: Spacing.md,
  },
  dateFilterButtons: {
    flexDirection: 'row',
    gap: Spacing.md,
  },
  dateFilterButton: {
    backgroundColor: Colors.light,
    paddingHorizontal: Spacing.lg,
    paddingVertical: Spacing.md,
    borderRadius: BorderRadius.large,
    borderWidth: 1,
    borderColor: Colors.borderLight,
    flex: 1,
    alignItems: 'center',
  },
  dateFilterButtonActive: {
    backgroundColor: Colors.primary,
    borderColor: Colors.primary,
  },
  dateFilterText: {
    fontSize: FontSizes.sm,
    fontWeight: '600',
    color: Colors.textPrimary,
  },
  dateFilterTextActive: {
    color: Colors.textOnPrimary,
  },
  summaryCard: {
    backgroundColor: Colors.surface,
    borderRadius: BorderRadius.large,
    padding: Spacing.lg,
    marginBottom: Spacing.lg,
    ...Shadows.medium,
    borderLeftWidth: 4,
    borderLeftColor: Colors.success,
  },
  summaryTitle: {
    fontSize: FontSizes.lg,
    fontWeight: 'bold',
    color: Colors.textPrimary,
    marginBottom: Spacing.md,
  },
  summaryContent: {
    flexDirection: 'row',
    justifyContent: 'space-around',
  },
  summaryItem: {
    alignItems: 'center',
  },
  summaryLabel: {
    fontSize: FontSizes.sm,
    color: Colors.textSecondary,
    marginBottom: Spacing.xs,
  },
  summaryValue: {
    fontSize: FontSizes.xl,
    fontWeight: 'bold',
    color: Colors.primary,
  },
  summaryValueAmount: {
    fontSize: FontSizes.xl,
    fontWeight: 'bold',
    color: Colors.success,
  },
  noDataContainer: {
    backgroundColor: Colors.surface,
    borderRadius: BorderRadius.large,
    padding: Spacing.xl,
    alignItems: 'center',
    marginBottom: Spacing.lg,
    ...Shadows.small,
  },
  noDataIcon: {
    fontSize: 48,
    marginBottom: Spacing.md,
  },
  noDataTitle: {
    fontSize: FontSizes.lg,
    fontWeight: 'bold',
    color: Colors.textPrimary,
    marginBottom: Spacing.md,
    textAlign: 'center',
  },
  noDataText: {
    fontSize: FontSizes.md,
    color: Colors.textSecondary,
    textAlign: 'center',
    lineHeight: 22,
    marginBottom: Spacing.lg,
  },
  goToOrderButton: {
    backgroundColor: Colors.primary,
    paddingHorizontal: Spacing.xl,
    paddingVertical: Spacing.md,
    borderRadius: BorderRadius.large,
    ...Shadows.medium,
  },
  goToOrderButtonText: {
    color: Colors.textOnPrimary,
    fontSize: FontSizes.md,
    fontWeight: '600',
  },
  daySection: {
    marginBottom: Spacing.xl,
  },
  dayHeader: {
    backgroundColor: Colors.surface,
    borderRadius: BorderRadius.large,
    padding: Spacing.lg,
    marginBottom: Spacing.md,
    ...Shadows.small,
  },
  dayTitle: {
    fontSize: FontSizes.lg,
    fontWeight: 'bold',
    color: Colors.textPrimary,
    marginBottom: Spacing.xs,
  },
  dayStats: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  dayStatsText: {
    fontSize: FontSizes.md,
    color: Colors.textSecondary,
    fontWeight: '600',
  },
  orderCard: {
    backgroundColor: Colors.surface,
    borderRadius: BorderRadius.large,
    padding: Spacing.lg,
    marginBottom: Spacing.md,
    ...Shadows.medium,
    borderLeftWidth: 4,
    borderLeftColor: Colors.primary,
  },
  orderHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: Spacing.md,
  },
  orderInfo: {
    flex: 1,
  },
  orderNumber: {
    fontSize: FontSizes.lg,
    fontWeight: 'bold',
    color: Colors.textPrimary,
    marginBottom: Spacing.xs,
  },
  orderTime: {
    fontSize: FontSizes.sm,
    color: Colors.textSecondary,
  },
  orderDetails: {
    marginBottom: Spacing.md,
  },
  customerName: {
    fontSize: FontSizes.md,
    color: Colors.textPrimary,
    marginBottom: Spacing.xs,
  },
  orderItems: {
    fontSize: FontSizes.sm,
    color: Colors.textSecondary,
  },
  orderFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-end',
    borderTopWidth: 1,
    borderTopColor: Colors.borderLight,
    paddingTop: Spacing.md,
  },
  priceBreakdown: {
    flex: 1,
  },
  subtotal: {
    fontSize: FontSizes.sm,
    color: Colors.textSecondary,
    marginBottom: Spacing.xs,
  },
  delivery: {
    fontSize: FontSizes.sm,
    color: Colors.textSecondary,
  },
  totalAmount: {
    fontSize: FontSizes.xl,
    fontWeight: 'bold',
    color: Colors.primary,
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  calendarContainer: {
    backgroundColor: Colors.surface,
    borderRadius: BorderRadius.large,
    margin: Spacing.lg,
    maxWidth: 350,
    width: '90%',
    ...Shadows.large,
  },
  calendarHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: Spacing.lg,
    borderBottomWidth: 1,
    borderBottomColor: Colors.borderLight,
  },
  calendarTitle: {
    fontSize: FontSizes.lg,
    fontWeight: 'bold',
    color: Colors.textPrimary,
  },
  closeButton: {
    width: 32,
    height: 32,
    borderRadius: BorderRadius.round,
    backgroundColor: Colors.light,
    justifyContent: 'center',
    alignItems: 'center',
  },
  closeButtonText: {
    fontSize: FontSizes.lg,
    color: Colors.textSecondary,
    fontWeight: 'bold',
  },
  calendarActions: {
    padding: Spacing.lg,
    borderTopWidth: 1,
    borderTopColor: Colors.borderLight,
    alignItems: 'center',
  },
  todayButton: {
    backgroundColor: Colors.accent,
    paddingHorizontal: Spacing.xl,
    paddingVertical: Spacing.md,
    borderRadius: BorderRadius.large,
    ...Shadows.small,
  },
  todayButtonText: {
    color: Colors.textOnPrimary,
    fontSize: FontSizes.md,
    fontWeight: '600',
  },
  section: {
    backgroundColor: Colors.surface,
    borderRadius: BorderRadius.large,
    padding: Spacing.md,
    marginBottom: Spacing.lg,
    ...Shadows.small,
  },
  sectionTitle: {
    fontSize: FontSizes.lg,
    fontWeight: 'bold',
    color: Colors.textPrimary,
    marginBottom: Spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: Colors.border,
    paddingBottom: Spacing.xs,
  },
  statsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  statCard: {
    width: (width - 48) / 2 - 8,
    backgroundColor: Colors.light,
    borderRadius: BorderRadius.medium,
    padding: Spacing.md,
    marginBottom: Spacing.sm,
    borderLeftWidth: 4,
  },
  statTitle: {
    fontSize: FontSizes.sm,
    color: Colors.textSecondary,
    marginBottom: Spacing.xs,
  },
  statValue: {
    fontSize: FontSizes.xl,
    fontWeight: 'bold',
    marginBottom: Spacing.xs,
  },
  statSubtitle: {
    fontSize: FontSizes.xs,
    color: Colors.textSecondary,
  },
  pizzaRankItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: Spacing.sm,
    borderBottomWidth: 1,
    borderBottomColor: Colors.border,
  },
  rankBadge: {
    width: 30,
    height: 30,
    borderRadius: BorderRadius.round,
    backgroundColor: Colors.primary,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: Spacing.md,
  },
  rankNumber: {
    color: Colors.textOnPrimary,
    fontSize: FontSizes.sm,
    fontWeight: 'bold',
  },
  pizzaInfo: {
    flex: 1,
  },
  pizzaName: {
    fontSize: FontSizes.md,
    fontWeight: '600',
    color: Colors.textPrimary,
  },
  pizzaCount: {
    fontSize: FontSizes.sm,
    color: Colors.textSecondary,
  },
  progressBarContainer: {
    width: 60,
    height: 4,
    backgroundColor: Colors.border,
    borderRadius: 2,
    overflow: 'hidden',
  },
  progressBar: {
    height: '100%',
    borderRadius: 2,
  },

  performanceGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  performanceItem: {
    width: (width - 48) / 2 - 8,
    alignItems: 'center',
    paddingVertical: Spacing.md,
  },
  performanceLabel: {
    fontSize: FontSizes.sm,
    color: Colors.textSecondary,
    textAlign: 'center',
    marginBottom: Spacing.xs,
  },
  performanceValue: {
    fontSize: FontSizes.lg,
    fontWeight: 'bold',
    color: Colors.textPrimary,
  },
});
