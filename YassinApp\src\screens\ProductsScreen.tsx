import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Alert,
  SafeAreaView,
} from 'react-native';
import { RootStackParamList } from '../types';
import { Colors, FontSizes, Spacing, BorderRadius, Shadows } from '../styles/theme';
import { ALL_PRODUCTS, getProductsByCategory, Product } from '../data/products';
import { CartService } from '../services/CartService';

interface ProductsScreenProps {
  navigation: {
    navigate: (screen: keyof RootStackParamList, params?: any) => void;
    goBack: () => void;
  };
}

export const ProductsScreen: React.FC<ProductsScreenProps> = ({ navigation }) => {
  const [selectedCategory, setSelectedCategory] = useState<string>('pizza');
  const [selectedProduct, setSelectedProduct] = useState<Product | null>(null);
  const [selectedSize, setSelectedSize] = useState<string>('');
  const [cart, setCart] = useState<any[]>([]);
  const [cartTotal, setCartTotal] = useState(0);

  // Charger le panier et calculer le total
  useEffect(() => {
    const loadCart = async () => {
      try {
        const cartItems = await CartService.getCart();
        setCart(cartItems);
        const total = cartItems.reduce((sum: number, item: any) => sum + (item.price * item.quantity), 0);
        setCartTotal(total);
      } catch (error) {
        console.error('Erreur lors du chargement du panier:', error);
      }
    };

    loadCart();
  }, []);

  const categories = [
    { id: 'pizza', name: 'Pizzas', icon: '🍕' },
    { id: 'drink', name: 'Boissons', icon: '🥤' },
    { id: 'side', name: 'Accompagnements', icon: '🍟' },
    { id: 'dessert', name: 'Desserts', icon: '🍰' }
  ];

  const handleSelectProduct = (product: Product) => {
    setSelectedProduct(product);
    setSelectedSize('');
    
    // Si le produit n'a qu'une seule taille, la sélectionner automatiquement
    if (product.sizes && product.sizes.length === 1) {
      setSelectedSize(product.sizes[0]);
    }
  };

  const handleAddToCart = async () => {
    if (!selectedProduct || !selectedSize) {
      Alert.alert('Sélection incomplète', 'Veuillez sélectionner un produit et une taille');
      return;
    }

    const price = selectedProduct.prices[selectedSize];
    if (!price) {
      Alert.alert('Erreur', 'Prix non trouvé pour cette taille');
      return;
    }

    // Créer l'item pour le panier
    const cartItem = {
      id: `${selectedProduct.id}-${selectedSize}`,
      name: `${selectedProduct.name} ${selectedSize}`,
      price: price,
      quantity: 1,
      category: selectedProduct.category,
      size: selectedSize
    };

    try {
      await CartService.addItem(cartItem);

      // Recharger le panier
      const updatedCart = await CartService.getCart();
      setCart(updatedCart);
      const total = updatedCart.reduce((sum: number, item: any) => sum + (item.price * item.quantity), 0);
      setCartTotal(total);

      Alert.alert(
        'Ajouté au panier',
        `${cartItem.name} a été ajouté au panier`,
        [
          { text: 'Continuer', style: 'default' },
          { text: 'Voir le panier', onPress: () => navigation.navigate('OrderForm') }
        ]
      );

      // Réinitialiser la sélection
      setSelectedProduct(null);
      setSelectedSize('');
    } catch (error) {
      console.error('Erreur lors de l\'ajout au panier:', error);
      Alert.alert('Erreur', 'Impossible d\'ajouter l\'article au panier');
    }
  };

  const clearCart = () => {
    Alert.alert(
      'Vider le panier',
      'Êtes-vous sûr de vouloir vider le panier ?',
      [
        { text: 'Annuler', style: 'cancel' },
        {
          text: 'Vider',
          style: 'destructive',
          onPress: async () => {
            try {
              await CartService.clearCart();
              setCart([]);
              setCartTotal(0);
            } catch (error) {
              console.error('Erreur lors du vidage du panier:', error);
            }
          }
        }
      ]
    );
  };

  const renderCategoryTabs = () => (
    <View style={styles.categoryTabs}>
      <ScrollView horizontal showsHorizontalScrollIndicator={false}>
        {categories.map((category) => (
          <TouchableOpacity
            key={category.id}
            style={[
              styles.categoryTab,
              selectedCategory === category.id && styles.categoryTabActive
            ]}
            onPress={() => setSelectedCategory(category.id)}
          >
            <Text style={styles.categoryIcon}>{category.icon}</Text>
            <Text
              style={[
                styles.categoryTabText,
                selectedCategory === category.id && styles.categoryTabTextActive
              ]}
            >
              {category.name}
            </Text>
          </TouchableOpacity>
        ))}
      </ScrollView>
    </View>
  );

  const renderProductList = () => {
    const products = getProductsByCategory(selectedCategory);
    
    return (
      <ScrollView style={styles.productList} showsVerticalScrollIndicator={false}>
        {products.map((product) => (
          <TouchableOpacity
            key={product.id}
            style={[
              styles.productCard,
              selectedProduct?.id === product.id && styles.productCardSelected
            ]}
            onPress={() => handleSelectProduct(product)}
          >
            <View style={styles.productHeader}>
              <Text style={styles.productIcon}>{product.image}</Text>
              <View style={styles.productInfo}>
                <Text style={styles.productName}>{product.name}</Text>
                <Text style={styles.productDescription}>{product.description}</Text>
              </View>
            </View>
            
            <View style={styles.productPrices}>
              {product.sizes?.map((size) => (
                <View key={size} style={styles.priceItem}>
                  <Text style={styles.sizeText}>{size}</Text>
                  <Text style={styles.priceText}>{product.prices[size]?.toFixed(2)} DT</Text>
                </View>
              ))}
            </View>
          </TouchableOpacity>
        ))}
      </ScrollView>
    );
  };

  const renderSizeSelection = () => {
    if (!selectedProduct) return null;

    return (
      <View style={styles.sizeSelection}>
        <Text style={styles.sizeSelectionTitle}>
          Choisir la taille pour {selectedProduct.name}
        </Text>
        
        <View style={styles.sizeOptions}>
          {selectedProduct.sizes?.map((size) => (
            <TouchableOpacity
              key={size}
              style={[
                styles.sizeOption,
                selectedSize === size && styles.sizeOptionSelected
              ]}
              onPress={() => setSelectedSize(size)}
            >
              <Text
                style={[
                  styles.sizeOptionText,
                  selectedSize === size && styles.sizeOptionTextSelected
                ]}
              >
                {size}
              </Text>
              <Text
                style={[
                  styles.sizeOptionPrice,
                  selectedSize === size && styles.sizeOptionPriceSelected
                ]}
              >
                {selectedProduct.prices[size]?.toFixed(2)} DT
              </Text>
            </TouchableOpacity>
          ))}
        </View>

        <TouchableOpacity
          style={[
            styles.addButton,
            (!selectedSize) && styles.addButtonDisabled
          ]}
          onPress={handleAddToCart}
          disabled={!selectedSize}
        >
          <Text style={styles.addButtonText}>
            Ajouter au panier
            {selectedSize && selectedProduct.prices[selectedSize] && 
              ` - ${selectedProduct.prices[selectedSize].toFixed(2)} DT`
            }
          </Text>
        </TouchableOpacity>
      </View>
    );
  };

  return (
    <View style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity onPress={() => navigation.goBack()} style={styles.backButton}>
          <Text style={styles.backButtonText}>← Retour</Text>
        </TouchableOpacity>
        <Text style={styles.title}>Nos Produits</Text>
        <TouchableOpacity
          style={styles.cartButton}
          onPress={() => navigation.navigate('OrderForm')}
        >
          <Text style={styles.cartButtonText}>🛒 Panier</Text>
        </TouchableOpacity>
      </View>

      {/* Category Tabs */}
      {renderCategoryTabs()}

      {/* Product List */}
      {renderProductList()}

      {/* Size Selection */}
      {renderSizeSelection()}

      {/* Panier flottant */}
      {cart.length > 0 && (
        <View style={styles.cartContainer}>
          <View style={styles.cartInfo}>
            <Text style={styles.cartText}>
              {cart.length} article{cart.length > 1 ? 's' : ''} - {cartTotal.toFixed(2)} DT
            </Text>
            <TouchableOpacity onPress={clearCart}>
              <Text style={styles.clearCartText}>🗑️</Text>
            </TouchableOpacity>
          </View>
          <TouchableOpacity
            style={styles.cartViewButton}
            onPress={() => navigation.navigate('OrderForm')}
          >
            <Text style={styles.cartViewButtonText}>Voir le panier</Text>
          </TouchableOpacity>
        </View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
    paddingTop: 40, // Espace pour éviter les problèmes d'affichage
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: Spacing.lg,
    paddingVertical: Spacing.lg,
    backgroundColor: Colors.surface,
    borderBottomWidth: 1,
    borderBottomColor: Colors.borderLight,
    minHeight: 60, // Hauteur minimale pour éviter les problèmes
    ...Shadows.small,
  },
  backButton: {
    paddingVertical: Spacing.sm,
    paddingHorizontal: Spacing.md,
  },
  backButtonText: {
    fontSize: FontSizes.md,
    color: Colors.primary,
    fontWeight: '600',
  },
  title: {
    fontSize: FontSizes.xl,
    fontWeight: 'bold',
    color: Colors.textPrimary,
  },
  cartButton: {
    backgroundColor: Colors.primary,
    paddingVertical: Spacing.sm,
    paddingHorizontal: Spacing.md,
    borderRadius: BorderRadius.medium,
  },
  cartButtonText: {
    fontSize: FontSizes.sm,
    fontWeight: 'bold',
    color: Colors.textOnPrimary,
  },
  
  // Category Tabs
  categoryTabs: {
    backgroundColor: Colors.surface,
    paddingVertical: Spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: Colors.borderLight,
  },
  categoryTab: {
    paddingHorizontal: Spacing.lg,
    paddingVertical: Spacing.sm,
    marginHorizontal: Spacing.xs,
    borderRadius: BorderRadius.medium,
    alignItems: 'center',
    backgroundColor: Colors.light,
  },
  categoryTabActive: {
    backgroundColor: Colors.primary,
  },
  categoryIcon: {
    fontSize: FontSizes.lg,
    marginBottom: Spacing.xs,
  },
  categoryTabText: {
    fontSize: FontSizes.sm,
    color: Colors.textSecondary,
    fontWeight: '500',
  },
  categoryTabTextActive: {
    color: Colors.textOnPrimary,
    fontWeight: 'bold',
  },
  
  // Product List
  productList: {
    flex: 1,
    padding: Spacing.md,
    paddingBottom: 100, // Espace pour le panier flottant
  },
  productCard: {
    backgroundColor: Colors.surface,
    borderRadius: BorderRadius.medium,
    padding: Spacing.md,
    marginBottom: Spacing.md,
    borderWidth: 1,
    borderColor: Colors.borderLight,
    ...Shadows.small,
  },
  productCardSelected: {
    borderColor: Colors.primary,
    borderWidth: 2,
  },
  productHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: Spacing.md,
  },
  productIcon: {
    fontSize: 40,
    marginRight: Spacing.md,
  },
  productInfo: {
    flex: 1,
  },
  productName: {
    fontSize: FontSizes.lg,
    fontWeight: 'bold',
    color: Colors.textPrimary,
    marginBottom: Spacing.xs,
  },
  productDescription: {
    fontSize: FontSizes.sm,
    color: Colors.textSecondary,
  },
  productPrices: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  priceItem: {
    alignItems: 'center',
    marginBottom: Spacing.xs,
  },
  sizeText: {
    fontSize: FontSizes.sm,
    color: Colors.textSecondary,
    marginBottom: Spacing.xs,
  },
  priceText: {
    fontSize: FontSizes.md,
    fontWeight: 'bold',
    color: Colors.primary,
  },
  
  // Size Selection
  sizeSelection: {
    backgroundColor: Colors.surface,
    padding: Spacing.lg,
    borderTopWidth: 1,
    borderTopColor: Colors.borderLight,
    ...Shadows.medium,
  },
  sizeSelectionTitle: {
    fontSize: FontSizes.lg,
    fontWeight: 'bold',
    color: Colors.textPrimary,
    marginBottom: Spacing.md,
    textAlign: 'center',
  },
  sizeOptions: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    marginBottom: Spacing.lg,
  },
  sizeOption: {
    flex: 1,
    minWidth: '30%',
    backgroundColor: Colors.light,
    padding: Spacing.md,
    borderRadius: BorderRadius.medium,
    alignItems: 'center',
    marginHorizontal: Spacing.xs,
    marginBottom: Spacing.sm,
    borderWidth: 1,
    borderColor: Colors.borderLight,
  },
  sizeOptionSelected: {
    backgroundColor: Colors.primary,
    borderColor: Colors.primary,
  },
  sizeOptionText: {
    fontSize: FontSizes.md,
    fontWeight: '600',
    color: Colors.textPrimary,
    marginBottom: Spacing.xs,
  },
  sizeOptionTextSelected: {
    color: Colors.textOnPrimary,
  },
  sizeOptionPrice: {
    fontSize: FontSizes.sm,
    color: Colors.textSecondary,
    fontWeight: 'bold',
  },
  sizeOptionPriceSelected: {
    color: Colors.textOnPrimary,
  },
  addButton: {
    backgroundColor: Colors.primary,
    paddingVertical: Spacing.md,
    borderRadius: BorderRadius.medium,
    alignItems: 'center',
  },
  addButtonDisabled: {
    backgroundColor: Colors.borderLight,
  },
  addButtonText: {
    fontSize: FontSizes.md,
    fontWeight: 'bold',
    color: Colors.textOnPrimary,
  },

  // Styles pour le panier flottant
  cartContainer: {
    position: 'absolute',
    bottom: 20,
    left: 20,
    right: 20,
    backgroundColor: Colors.primary,
    borderRadius: BorderRadius.large,
    padding: Spacing.md,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 8,
  },
  cartInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  cartText: {
    color: Colors.textOnPrimary,
    fontSize: FontSizes.md,
    fontWeight: 'bold',
    flex: 1,
  },
  clearCartText: {
    fontSize: FontSizes.lg,
    marginLeft: Spacing.sm,
  },
  cartViewButton: {
    backgroundColor: Colors.textOnPrimary,
    paddingHorizontal: Spacing.md,
    paddingVertical: Spacing.sm,
    borderRadius: BorderRadius.medium,
  },
  cartViewButtonText: {
    color: Colors.primary,
    fontSize: FontSizes.sm,
    fontWeight: 'bold',
  },
});
