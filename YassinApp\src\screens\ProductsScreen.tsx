import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Alert,
  SafeAreaView,
  StatusBar,
  Platform,
} from 'react-native';
import { RootStackParamList } from '../types';
import { Colors, FontSizes, Spacing, BorderRadius, Shadows } from '../styles/theme';
import { ALL_PRODUCTS, getProductsByCategory, Product } from '../data/products';
import { CartService } from '../services/CartService';

interface ProductsScreenProps {
  navigation: {
    navigate: (screen: keyof RootStackParamList, params?: any) => void;
    goBack: () => void;
  };
}

export const ProductsScreen: React.FC<ProductsScreenProps> = ({ navigation }) => {
  const [selectedCategory, setSelectedCategory] = useState<string>('pizza');
  const [selectedProduct, setSelectedProduct] = useState<Product | null>(null);
  const [selectedSize, setSelectedSize] = useState<string>('');
  const [cart, setCart] = useState<any[]>([]);
  const [cartTotal, setCartTotal] = useState(0);

  // Charger le panier et calculer le total
  useEffect(() => {
    const loadCart = async () => {
      try {
        const cartData = CartService.getCart();
        const cartItems = cartData.items || [];
        setCart(cartItems);
        const total = cartItems.reduce((sum: number, item: any) => sum + (item.price * item.quantity), 0);
        setCartTotal(total);
      } catch (error) {
        console.error('Erreur lors du chargement du panier:', error);
        setCart([]);
        setCartTotal(0);
      }
    };

    loadCart();
  }, []);

  const categories = [
    { id: 'pizza', name: 'Pizzas', icon: '🍕' },
    { id: 'drink', name: 'Boissons', icon: '🥤' },
    { id: 'side', name: 'Accompagnements', icon: '🍟' },
    { id: 'dessert', name: 'Desserts', icon: '🍰' }
  ];

  const handleSelectProduct = (product: Product) => {
    setSelectedProduct(product);
    setSelectedSize('');
    
    // Si le produit n'a qu'une seule taille, la sélectionner automatiquement
    if (product.sizes && product.sizes.length === 1) {
      setSelectedSize(product.sizes[0]);
    }
  };

  const handleAddToCart = async () => {
    if (!selectedProduct || !selectedSize) {
      Alert.alert('Sélection incomplète', 'Veuillez sélectionner un produit et une taille');
      return;
    }

    const price = selectedProduct.prices[selectedSize];
    if (!price) {
      Alert.alert('Erreur', 'Prix non trouvé pour cette taille');
      return;
    }

    // Créer l'item pour le panier avec ID unique
    const cartItem = {
      id: `${selectedProduct.id}-${selectedSize}-${Date.now()}`,
      name: `${selectedProduct.name} ${selectedSize}`,
      price: price,
      quantity: 1,
      category: selectedProduct.category,
      size: selectedSize,
      productId: selectedProduct.id
    };

    try {
      await CartService.addNewItem(cartItem);

      // Recharger le panier
      const updatedCartData = CartService.getCart();
      const updatedCart = updatedCartData.items || [];
      setCart(updatedCart);
      const total = updatedCart.reduce((sum: number, item: any) => sum + (item.price * item.quantity), 0);
      setCartTotal(total);

      Alert.alert(
        '✅ Article ajouté !',
        `${cartItem.name} a été ajouté au panier\n\nPanier : ${updatedCart.length} article${updatedCart.length > 1 ? 's' : ''} - ${total.toFixed(2)} DT`,
        [
          { text: 'Continuer les achats', style: 'default' },
          { text: 'Voir le panier', onPress: () => navigation.navigate('OrderForm') }
        ]
      );

      // Réinitialiser la sélection pour permettre d'ajouter d'autres articles
      setSelectedProduct(null);
      setSelectedSize('');
    } catch (error) {
      console.error('Erreur lors de l\'ajout au panier:', error);
      Alert.alert('Erreur', 'Impossible d\'ajouter l\'article au panier');
    }
  };

  const clearCart = () => {
    Alert.alert(
      'Vider le panier',
      'Êtes-vous sûr de vouloir vider le panier ?',
      [
        { text: 'Annuler', style: 'cancel' },
        {
          text: 'Vider',
          style: 'destructive',
          onPress: () => {
            try {
              CartService.clearCart();
              setCart([]);
              setCartTotal(0);
            } catch (error) {
              console.error('Erreur lors du vidage du panier:', error);
            }
          }
        }
      ]
    );
  };

  const renderCategoryTabs = () => (
    <View style={styles.categoryTabs}>
      <ScrollView horizontal showsHorizontalScrollIndicator={false}>
        {categories.map((category) => (
          <TouchableOpacity
            key={category.id}
            style={[
              styles.categoryTab,
              selectedCategory === category.id && styles.categoryTabActive
            ]}
            onPress={() => setSelectedCategory(category.id)}
          >
            <Text style={styles.categoryIcon}>{category.icon}</Text>
            <Text
              style={[
                styles.categoryTabText,
                selectedCategory === category.id && styles.categoryTabTextActive
              ]}
            >
              {category.name}
            </Text>
          </TouchableOpacity>
        ))}
      </ScrollView>
    </View>
  );

  // Fonction pour compter les articles d'un produit dans le panier
  const getProductCount = (productId: string) => {
    return cart.filter(item => item.productId === productId).length;
  };

  // Fonction pour ajouter directement au panier
  const addToCartDirect = async (product: any, size: string) => {
    const price = product.prices[size];
    if (!price) {
      Alert.alert('Erreur', 'Prix non trouvé pour cette taille');
      return;
    }

    const cartItem = {
      id: `${product.id}-${size}-${Date.now()}`,
      name: `${product.name} ${size}`,
      price: price,
      quantity: 1,
      category: product.category,
      size: size,
      productId: product.id
    };

    try {
      console.log('🛒 Tentative d\'ajout au panier:', cartItem);
      await CartService.addNewItem(cartItem);
      console.log('✅ Article ajouté avec succès');

      // Recharger le panier
      const updatedCartData = CartService.getCart();
      console.log('📋 Données du panier après ajout:', updatedCartData);
      const updatedCart = updatedCartData.items || [];
      console.log('📦 Articles dans le panier:', updatedCart.length);
      setCart(updatedCart);
      const total = updatedCart.reduce((sum: number, item: any) => sum + (item.price * item.quantity), 0);
      setCartTotal(total);

      // Feedback simple sans popup
      console.log(`✅ ${cartItem.name} ajouté au panier. Total: ${updatedCart.length} articles, ${total.toFixed(2)} DT`);

    } catch (error) {
      console.error('❌ Erreur lors de l\'ajout au panier:', error);
      Alert.alert('Erreur', 'Impossible d\'ajouter l\'article au panier');
    }
  };

  const renderProductList = () => {
    const products = getProductsByCategory(selectedCategory);

    return (
      <ScrollView style={styles.productList} showsVerticalScrollIndicator={false}>
        {products.map((product) => (
          <View key={product.id} style={styles.productCardNew}>
            {/* Header avec nom et compteur */}
            <View style={styles.productHeaderNew}>
              <View style={styles.productTitleContainer}>
                <Text style={styles.productIcon}>{product.image}</Text>
                <Text style={styles.productNameNew}>{product.name}</Text>
              </View>
              {getProductCount(product.id) > 0 && (
                <View style={styles.countBadge}>
                  <Text style={styles.countText}>{getProductCount(product.id)}</Text>
                </View>
              )}
            </View>

            {/* Description */}
            <Text style={styles.productDescriptionNew}>{product.description}</Text>

            {/* Boutons de taille avec prix */}
            <View style={styles.sizeButtonsContainer}>
              {product.sizes?.map((size) => (
                <TouchableOpacity
                  key={size}
                  style={styles.sizeButton}
                  onPress={() => addToCartDirect(product, size)}
                  activeOpacity={1}
                >
                  <Text style={styles.sizeButtonText}>
                    {size === 'Moyenne' ? 'M' : size === 'Large' ? 'L' : size}
                  </Text>
                  <Text style={styles.sizeButtonPrice}>
                    {product.prices[size]?.toFixed(2)} DT
                  </Text>
                </TouchableOpacity>
              ))}
            </View>
          </View>
        ))}
      </ScrollView>
    );
  };



  return (
    <View style={styles.container}>
      <StatusBar barStyle="dark-content" backgroundColor={Colors.surface} />
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity onPress={() => navigation.goBack()} style={styles.backButton}>
          <Text style={styles.backButtonText}>← Retour</Text>
        </TouchableOpacity>
        <Text style={styles.title}>Nos Produits</Text>
        <TouchableOpacity
          style={styles.cartButton}
          onPress={() => navigation.navigate('OrderForm')}
        >
          <Text style={styles.cartButtonText}>
            🛒 Panier {cart.length > 0 && `(${cart.length})`}
          </Text>
          {cartTotal > 0 && (
            <Text style={styles.cartTotalText}>
              {cartTotal.toFixed(2)} DT
            </Text>
          )}
        </TouchableOpacity>
      </View>

      {/* Category Tabs */}
      {renderCategoryTabs()}

      {/* Product List */}
      {renderProductList()}

      {/* Panier flottant */}
      {cart.length > 0 && (
        <View style={styles.cartContainer}>
          <View style={styles.cartInfo}>
            <Text style={styles.cartText}>
              {cart.length} article{cart.length > 1 ? 's' : ''} - {cartTotal.toFixed(2)} DT
            </Text>
            <TouchableOpacity onPress={clearCart}>
              <Text style={styles.clearCartText}>🗑️</Text>
            </TouchableOpacity>
          </View>
          <TouchableOpacity
            style={styles.cartViewButton}
            onPress={() => navigation.navigate('OrderForm')}
          >
            <Text style={styles.cartViewButtonText}>Voir le panier</Text>
          </TouchableOpacity>
        </View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#E9ECEF', // Gris plus foncé pour éviter le blanc
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: Spacing.lg,
    paddingTop: Platform.OS === 'ios' ? 50 : StatusBar.currentHeight ? StatusBar.currentHeight + 10 : 35,
    paddingBottom: Spacing.lg,
    backgroundColor: Colors.surface,
    borderBottomWidth: 1,
    borderBottomColor: Colors.borderLight,
    minHeight: Platform.OS === 'ios' ? 90 : 70,
    ...Shadows.small,
  },
  backButton: {
    paddingVertical: Spacing.sm,
    paddingHorizontal: Spacing.md,
  },
  backButtonText: {
    fontSize: FontSizes.md,
    color: Colors.primary,
    fontWeight: '600',
  },
  title: {
    fontSize: FontSizes.xl,
    fontWeight: 'bold',
    color: Colors.textPrimary,
  },
  cartButton: {
    backgroundColor: Colors.primary,
    paddingVertical: Spacing.sm,
    paddingHorizontal: Spacing.md,
    borderRadius: BorderRadius.medium,
    alignItems: 'center',
  },
  cartButtonText: {
    fontSize: FontSizes.sm,
    fontWeight: 'bold',
    color: Colors.textOnPrimary,
  },
  cartTotalText: {
    fontSize: FontSizes.xs,
    color: Colors.textOnPrimary,
    fontWeight: 'bold',
    marginTop: 2,
  },
  
  // Category Tabs
  categoryTabs: {
    backgroundColor: Colors.surface,
    paddingVertical: Spacing.md,
    borderBottomWidth: 2,
    borderBottomColor: Colors.border,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  categoryTab: {
    paddingHorizontal: Spacing.lg,
    paddingVertical: Spacing.sm,
    marginHorizontal: Spacing.xs,
    borderRadius: BorderRadius.medium,
    alignItems: 'center',
    backgroundColor: '#F8F9FA',
    borderWidth: 1,
    borderColor: Colors.border,
  },
  categoryTabActive: {
    backgroundColor: Colors.primary,
  },
  categoryIcon: {
    fontSize: FontSizes.lg,
    marginBottom: Spacing.xs,
  },
  categoryTabText: {
    fontSize: FontSizes.sm,
    color: Colors.textSecondary,
    fontWeight: '500',
  },
  categoryTabTextActive: {
    color: Colors.textOnPrimary,
    fontWeight: 'bold',
  },
  
  // Product List
  productList: {
    flex: 1,
    backgroundColor: '#E9ECEF', // Même couleur que le container
    padding: Spacing.md,
    paddingBottom: 100, // Espace pour le panier flottant
  },
  productCard: {
    backgroundColor: Colors.surface,
    borderRadius: BorderRadius.medium,
    padding: Spacing.md,
    marginBottom: Spacing.md,
    borderWidth: 1,
    borderColor: Colors.borderLight,
    ...Shadows.small,
  },
  productCardSelected: {
    borderColor: Colors.primary,
    borderWidth: 2,
  },
  productHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: Spacing.md,
  },
  productIcon: {
    fontSize: 40,
    marginRight: Spacing.md,
  },
  productInfo: {
    flex: 1,
  },
  productName: {
    fontSize: FontSizes.lg,
    fontWeight: 'bold',
    color: Colors.textPrimary,
    marginBottom: Spacing.xs,
  },
  productDescription: {
    fontSize: FontSizes.sm,
    color: Colors.textSecondary,
  },
  productPrices: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  priceItem: {
    alignItems: 'center',
    marginBottom: Spacing.xs,
  },
  sizeText: {
    fontSize: FontSizes.sm,
    color: Colors.textSecondary,
    marginBottom: Spacing.xs,
  },
  priceText: {
    fontSize: FontSizes.md,
    fontWeight: 'bold',
    color: Colors.primary,
  },
  
  // Size Selection
  sizeSelection: {
    backgroundColor: Colors.surface,
    padding: Spacing.lg,
    borderTopWidth: 1,
    borderTopColor: Colors.borderLight,
    ...Shadows.medium,
  },
  sizeSelectionTitle: {
    fontSize: FontSizes.lg,
    fontWeight: 'bold',
    color: Colors.textPrimary,
    marginBottom: Spacing.md,
    textAlign: 'center',
  },
  sizeOptions: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    marginBottom: Spacing.lg,
  },
  sizeOption: {
    flex: 1,
    minWidth: '30%',
    backgroundColor: Colors.light,
    padding: Spacing.md,
    borderRadius: BorderRadius.medium,
    alignItems: 'center',
    marginHorizontal: Spacing.xs,
    marginBottom: Spacing.sm,
    borderWidth: 1,
    borderColor: Colors.borderLight,
  },
  sizeOptionSelected: {
    backgroundColor: Colors.primary,
    borderColor: Colors.primary,
  },
  sizeOptionText: {
    fontSize: FontSizes.md,
    fontWeight: '600',
    color: Colors.textPrimary,
    marginBottom: Spacing.xs,
  },
  sizeOptionTextSelected: {
    color: Colors.textOnPrimary,
  },
  sizeOptionPrice: {
    fontSize: FontSizes.sm,
    color: Colors.textSecondary,
    fontWeight: 'bold',
  },
  sizeOptionPriceSelected: {
    color: Colors.textOnPrimary,
  },
  addButton: {
    backgroundColor: Colors.primary,
    paddingVertical: Spacing.md,
    borderRadius: BorderRadius.medium,
    alignItems: 'center',
  },
  addButtonDisabled: {
    backgroundColor: Colors.borderLight,
  },
  addButtonText: {
    fontSize: FontSizes.md,
    fontWeight: 'bold',
    color: Colors.textOnPrimary,
  },

  // Styles pour le panier flottant
  cartContainer: {
    position: 'absolute',
    bottom: 20,
    left: 20,
    right: 20,
    backgroundColor: Colors.primary,
    borderRadius: BorderRadius.large,
    padding: Spacing.md,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 8,
  },
  cartInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  cartText: {
    color: Colors.textOnPrimary,
    fontSize: FontSizes.md,
    fontWeight: 'bold',
    flex: 1,
  },
  clearCartText: {
    fontSize: FontSizes.lg,
    marginLeft: Spacing.sm,
  },
  cartViewButton: {
    backgroundColor: Colors.textOnPrimary,
    paddingHorizontal: Spacing.md,
    paddingVertical: Spacing.sm,
    borderRadius: BorderRadius.medium,
  },
  cartViewButtonText: {
    color: Colors.primary,
    fontSize: FontSizes.sm,
    fontWeight: 'bold',
  },

  // Nouveaux styles pour l'interface simplifiée
  productCardNew: {
    backgroundColor: Colors.surface,
    borderRadius: BorderRadius.medium,
    padding: Spacing.md,
    marginBottom: Spacing.md,
    borderWidth: 1,
    borderColor: Colors.border,
    ...Shadows.small,
  },
  productHeaderNew: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: Spacing.sm,
  },
  productTitleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  productNameNew: {
    fontSize: FontSizes.lg,
    fontWeight: 'bold',
    color: Colors.textPrimary,
    marginLeft: Spacing.sm,
  },
  productDescriptionNew: {
    fontSize: FontSizes.sm,
    color: Colors.textSecondary,
    marginBottom: Spacing.md,
  },
  countBadge: {
    backgroundColor: Colors.primary,
    borderRadius: BorderRadius.large,
    paddingHorizontal: Spacing.sm,
    paddingVertical: Spacing.xs,
    minWidth: 30,
    alignItems: 'center',
  },
  countText: {
    color: Colors.textOnPrimary,
    fontSize: FontSizes.sm,
    fontWeight: 'bold',
  },
  sizeButtonsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    gap: Spacing.md,
  },
  sizeButton: {
    backgroundColor: Colors.primary,
    borderRadius: BorderRadius.medium,
    paddingVertical: Spacing.md,
    paddingHorizontal: Spacing.lg,
    flex: 1,
    alignItems: 'center',
    minHeight: 60,
    justifyContent: 'center',
    borderWidth: 2,
    borderColor: Colors.primary,
    ...Shadows.small,
  },
  sizeButtonText: {
    color: Colors.textOnPrimary,
    fontSize: FontSizes.lg,
    fontWeight: 'bold',
    marginBottom: Spacing.xs,
  },
  sizeButtonPrice: {
    color: Colors.textOnPrimary,
    fontSize: FontSizes.sm,
    fontWeight: '600',
  },
});
