/**
 * Script de test pour vérifier les boutons
 * À exécuter dans la console du navigateur
 */

// Fonction pour ajouter une commande de test simple
function addTestOrder() {
  try {
    console.log('🧪 Ajout d\'une commande de test...');

    const testOrder = {
      id: 'test-' + Date.now(),
      customer: {
        name: 'Ahmed Test',
        address: '123 Rue Test, Tunis',
        phoneNumber: '12345678'
      },
      items: [
        {
          pizzaId: 'margherita',
          pizzaName: 'Margherita',
          size: 'M',
          quantity: 2,
          unitPrice: 15.00,
          totalPrice: 30.00
        }
      ],
      totalAmount: 30.00,
      status: 'confirmed',
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };

    // Récupérer les commandes existantes
    const existingOrders = JSON.parse(localStorage.getItem('pizza_orders') || '[]');

    // Ajouter la nouvelle commande
    existingOrders.push(testOrder);

    // Sauvegarder
    localStorage.setItem('pizza_orders', JSON.stringify(existingOrders));

    console.log('✅ Commande de test ajoutée:', testOrder);
    console.log('🔄 Rafraîchissez la page pour voir la commande');

    return testOrder;
  } catch (error) {
    console.error('❌ Erreur lors de l\'ajout de la commande de test:', error);
  }
}

// Fonction pour voir toutes les commandes
function showAllOrders() {
  try {
    const orders = JSON.parse(localStorage.getItem('pizza_orders') || '[]');
    console.log('📊 Commandes actuelles:', orders.length);
    orders.forEach((order, index) => {
      console.log(`${index + 1}. ${order.customer?.name || 'Client inconnu'} - ${order.totalAmount} DT - ${order.status}`);
    });
    return orders;
  } catch (error) {
    console.error('❌ Erreur:', error);
  }
}

// Fonction pour nettoyer toutes les commandes
function clearAllOrders() {
  try {
    localStorage.removeItem('pizza_orders');
    console.log('🧹 Toutes les commandes supprimées');
    console.log('🔄 Rafraîchissez la page pour voir les changements');
  } catch (error) {
    console.error('❌ Erreur:', error);
  }
}

// Fonction pour tester les boutons
function testButtons() {
  console.log('🧪 NOUVEAU Test des boutons:');
  console.log('1. Tapez: addTestOrder()');
  console.log('2. Rafraîchissez la page (F5)');
  console.log('3. Allez sur Statistiques');
  console.log('4. Cherchez les logs: "🔄 Rendu de la commande"');
  console.log('5. Cliquez Détails → Log: "🔵 CLIC DÉTAILS"');
  console.log('6. Cliquez Annuler → Log: "🔴 CLIC ANNULER"');
}

// Test direct des fonctions Alert
function testAlert() {
  console.log('🧪 Test direct Alert...');
  if (typeof Alert !== 'undefined') {
    Alert.alert('Test', 'Alert fonctionne!', [{ text: 'OK' }]);
  } else {
    console.error('❌ Alert non disponible');
  }
}

// Rendre les fonctions disponibles globalement
if (typeof window !== 'undefined') {
  window.addTestOrder = addTestOrder;
  window.showAllOrders = showAllOrders;
  window.clearAllOrders = clearAllOrders;
  window.testButtons = testButtons;
  window.testAlert = testAlert;

  console.log('🧪 NOUVEAU Script de test chargé!');
  console.log('📝 Commandes disponibles:');
  console.log('  - addTestOrder() → Ajouter commande test');
  console.log('  - showAllOrders() → Voir toutes les commandes');
  console.log('  - clearAllOrders() → Nettoyer tout');
  console.log('  - testButtons() → Instructions de test');
  console.log('  - testAlert() → Tester Alert directement');
}

// Auto-exécution
if (typeof module === 'undefined') {
  testButtons();
}
