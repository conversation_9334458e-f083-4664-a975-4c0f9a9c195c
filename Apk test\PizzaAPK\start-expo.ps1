# Script PowerShell pour démarrer Expo et générer le QR code
# Version: 1.6.0 - Pizza Caisse Manager

Write-Host "========================================" -ForegroundColor Cyan
Write-Host "   PIZZA CAISSE MANAGER - VERSION 1.6.0" -ForegroundColor Yellow
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""
Write-Host "🍕 Démarrage du serveur Expo..." -ForegroundColor Green
Write-Host "📱 Génération du QR code pour Expo Go" -ForegroundColor Green
Write-Host ""

# Fonction pour afficher les messages avec couleurs
function Write-Status {
    param($Message, $Color = "White")
    Write-Host "⏰ $(Get-Date -Format 'HH:mm:ss') - $Message" -ForegroundColor $Color
}

# Fonction pour tester une commande
function Test-Command {
    param($Command)
    try {
        $null = Get-Command $Command -ErrorAction Stop
        return $true
    } catch {
        return $false
    }
}

# Vérifier le répertoire de travail
$projectPath = "C:\Users\<USER>\Documents\AppCaisse\Apk test\PizzaAPK"
Write-Status "Vérification du répertoire..." "Cyan"

if (-not (Test-Path $projectPath)) {
    Write-Status "❌ Répertoire non trouvé: $projectPath" "Red"
    Write-Host "💡 Vérifiez que le projet existe à cet emplacement" -ForegroundColor Yellow
    Read-Host "Appuyez sur Entrée pour continuer"
    exit 1
}

Set-Location $projectPath
Write-Status "📁 Répertoire: $(Get-Location)" "Green"

# Vérifier Node.js
Write-Status "Vérification de Node.js..." "Cyan"
if (-not (Test-Command "node")) {
    Write-Status "❌ Node.js non trouvé" "Red"
    Write-Host "💡 Installez Node.js depuis https://nodejs.org" -ForegroundColor Yellow
    Read-Host "Appuyez sur Entrée pour continuer"
    exit 1
}

$nodeVersion = node --version
Write-Status "✅ Node.js détecté: $nodeVersion" "Green"

# Vérifier npm
if (-not (Test-Command "npm")) {
    Write-Status "❌ npm non trouvé" "Red"
    Read-Host "Appuyez sur Entrée pour continuer"
    exit 1
}

$npmVersion = npm --version
Write-Status "✅ npm détecté: $npmVersion" "Green"

# Vérifier les dépendances
Write-Status "Vérification des dépendances..." "Cyan"
if (-not (Test-Path "node_modules")) {
    Write-Status "📥 Installation des dépendances..." "Yellow"
    try {
        npm install
        Write-Status "✅ Dépendances installées" "Green"
    } catch {
        Write-Status "❌ Erreur lors de l'installation des dépendances" "Red"
        Write-Host $_.Exception.Message -ForegroundColor Red
        Read-Host "Appuyez sur Entrée pour continuer"
        exit 1
    }
} else {
    Write-Status "✅ Dépendances OK" "Green"
}

# Nettoyer les processus Expo existants
Write-Status "Nettoyage des processus existants..." "Cyan"
try {
    Get-Process | Where-Object { $_.ProcessName -like "*expo*" -or $_.ProcessName -like "*node*" } | Stop-Process -Force -ErrorAction SilentlyContinue
    Start-Sleep -Seconds 2
} catch {
    # Ignorer les erreurs de nettoyage
}

# Démarrer Expo avec plusieurs tentatives
Write-Status "🚀 Démarrage d'Expo..." "Green"
Write-Host ""
Write-Host "📱 Le QR code va apparaître dans quelques secondes..." -ForegroundColor Yellow
Write-Host "⏳ Patientez pendant le chargement..." -ForegroundColor Yellow
Write-Host ""

$attempts = @(
    @{ Command = "npx expo start --clear"; Description = "Expo start normal" },
    @{ Command = "npx expo start --tunnel --clear"; Description = "Expo start avec tunnel" },
    @{ Command = "npx expo start --port 8081 --clear"; Description = "Expo start sur port 8081" },
    @{ Command = "npx expo start --localhost --clear"; Description = "Expo start localhost" }
)

$success = $false
foreach ($attempt in $attempts) {
    Write-Status "🔄 Tentative: $($attempt.Description)..." "Yellow"
    
    try {
        # Lancer la commande en arrière-plan
        $process = Start-Process powershell -ArgumentList "-Command", "cd '$projectPath'; $($attempt.Command)" -WindowStyle Normal -PassThru
        
        # Attendre un peu pour voir si ça démarre
        Start-Sleep -Seconds 10
        
        # Vérifier si le processus est toujours en cours
        if (-not $process.HasExited) {
            Write-Status "✅ Expo démarré avec succès!" "Green"
            $success = $true
            break
        } else {
            Write-Status "❌ Échec de cette tentative" "Red"
        }
    } catch {
        Write-Status "❌ Erreur: $($_.Exception.Message)" "Red"
    }
}

if ($success) {
    Write-Host ""
    Write-Host "========================================" -ForegroundColor Green
    Write-Host "           QR CODE GÉNÉRÉ !" -ForegroundColor Yellow
    Write-Host "========================================" -ForegroundColor Green
    Write-Host ""
    Write-Host "✅ Serveur Expo démarré avec succès !" -ForegroundColor Green
    Write-Host "📱 Scannez le QR code avec Expo Go" -ForegroundColor Cyan
    Write-Host ""
    Write-Host "📋 Instructions:" -ForegroundColor Yellow
    Write-Host "   1. Ouvrez Expo Go sur votre téléphone" -ForegroundColor White
    Write-Host "   2. Scannez le QR code affiché" -ForegroundColor White
    Write-Host "   3. L'application va se charger" -ForegroundColor White
    Write-Host "   4. Testez les nouvelles fonctionnalités" -ForegroundColor White
    Write-Host ""
    Write-Host "🔧 Nouvelles fonctionnalités:" -ForegroundColor Yellow
    Write-Host "   📊 Boutons mieux positionnés" -ForegroundColor Green
    Write-Host "   📱 Manipulation tactile améliorée" -ForegroundColor Green
    Write-Host "   🚪 Déconnexion repositionnée" -ForegroundColor Green
    Write-Host ""
    Write-Host "⚠️  Gardez cette fenêtre ouverte pendant les tests" -ForegroundColor Yellow
} else {
    Write-Host ""
    Write-Host "❌ Toutes les tentatives ont échoué" -ForegroundColor Red
    Write-Host ""
    Write-Host "🔧 Solutions possibles:" -ForegroundColor Yellow
    Write-Host "   1. Redémarrez votre ordinateur" -ForegroundColor White
    Write-Host "   2. Vérifiez votre connexion internet" -ForegroundColor White
    Write-Host "   3. Désactivez temporairement l'antivirus" -ForegroundColor White
    Write-Host "   4. Exécutez en tant qu'administrateur" -ForegroundColor White
    Write-Host "   5. Installez Expo CLI globalement: npm install -g @expo/cli" -ForegroundColor White
}

Write-Host ""
Read-Host "Appuyez sur Entrée pour continuer"
