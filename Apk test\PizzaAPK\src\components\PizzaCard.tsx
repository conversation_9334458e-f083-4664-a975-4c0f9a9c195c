import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Alert,
} from 'react-native';
import { Pizza, PizzaSize, OrderItem } from '../types';
import { Colors, Shadows, BorderRadius, Spacing, FontSizes } from '../styles/colors';

interface PizzaCardProps {
  pizza: Pizza;
  onAddToOrder: (item: OrderItem) => void;
}

export const PizzaCard: React.FC<PizzaCardProps> = ({ pizza, onAddToOrder }) => {
  const [selectedSize, setSelectedSize] = useState<PizzaSize>('M');
  const [quantity, setQuantity] = useState(1);

  const handleAddToOrder = () => {
    const unitPrice = pizza.prices[selectedSize];
    const orderItem: OrderItem = {
      pizzaId: pizza.id,
      pizzaName: pizza.name,
      size: selectedSize,
      quantity,
      unitPrice,
      totalPrice: unitPrice * quantity,
    };

    onAddToOrder(orderItem);
    
    Alert.alert(
      'Ajouté au panier',
      `${quantity}x ${pizza.name} (${selectedSize}) ajouté au panier`,
      [{ text: 'OK' }]
    );
  };

  const incrementQuantity = () => setQuantity(prev => prev + 1);
  const decrementQuantity = () => setQuantity(prev => Math.max(1, prev - 1));

  return (
    <View style={styles.card}>
      <View style={styles.header}>
        <Text style={styles.name}>{pizza.name}</Text>
        <Text style={styles.price}>{pizza.prices[selectedSize].toFixed(2)} DT</Text>
      </View>
      
      <Text style={styles.description}>{pizza.description}</Text>
      
      {/* Sélection de la taille */}
      <View style={styles.sizeContainer}>
        <Text style={styles.sizeLabel}>Taille:</Text>
        <View style={styles.sizeButtons}>
          {(['M', 'L', 'XL'] as PizzaSize[]).map((size) => (
            <TouchableOpacity
              key={size}
              style={[
                styles.sizeButton,
                selectedSize === size && styles.selectedSizeButton,
              ]}
              onPress={() => setSelectedSize(size)}
            >
              <Text
                style={[
                  styles.sizeButtonText,
                  selectedSize === size && styles.selectedSizeButtonText,
                ]}
              >
                {size}
              </Text>
            </TouchableOpacity>
          ))}
        </View>
      </View>

      {/* Contrôle de quantité */}
      <View style={styles.quantityContainer}>
        <Text style={styles.quantityLabel}>Quantité:</Text>
        <View style={styles.quantityControls}>
          <TouchableOpacity style={styles.quantityButton} onPress={decrementQuantity}>
            <Text style={styles.quantityButtonText}>-</Text>
          </TouchableOpacity>
          <Text style={styles.quantityText}>{quantity}</Text>
          <TouchableOpacity style={styles.quantityButton} onPress={incrementQuantity}>
            <Text style={styles.quantityButtonText}>+</Text>
          </TouchableOpacity>
        </View>
      </View>

      {/* Bouton d'ajout */}
      <TouchableOpacity style={styles.addButton} onPress={handleAddToOrder}>
        <Text style={styles.addButtonText}>
          Ajouter au panier - {(pizza.prices[selectedSize] * quantity).toFixed(2)} DT
        </Text>
      </TouchableOpacity>
    </View>
  );
};

const styles = StyleSheet.create({
  card: {
    backgroundColor: Colors.surface,
    borderRadius: BorderRadius.large,
    padding: Spacing.md,
    marginVertical: Spacing.sm,
    marginHorizontal: Spacing.md,
    ...Shadows.medium,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: Spacing.sm,
  },
  name: {
    fontSize: FontSizes.lg,
    fontWeight: 'bold',
    color: Colors.text,
    flex: 1,
  },
  price: {
    fontSize: FontSizes.md,
    fontWeight: 'bold',
    color: Colors.primary,
  },
  description: {
    fontSize: FontSizes.sm,
    color: Colors.textSecondary,
    marginBottom: Spacing.md,
    lineHeight: 20,
  },
  sizeContainer: {
    marginBottom: Spacing.md,
  },
  sizeLabel: {
    fontSize: FontSizes.sm,
    fontWeight: '600',
    color: Colors.text,
    marginBottom: Spacing.sm,
  },
  sizeButtons: {
    flexDirection: 'row',
    gap: Spacing.sm,
  },
  sizeButton: {
    paddingHorizontal: Spacing.md,
    paddingVertical: Spacing.sm,
    borderRadius: BorderRadius.round,
    borderWidth: 1,
    borderColor: Colors.border,
    backgroundColor: Colors.light,
  },
  selectedSizeButton: {
    backgroundColor: Colors.secondary,
    borderColor: Colors.secondary,
  },
  sizeButtonText: {
    fontSize: FontSizes.sm,
    fontWeight: '600',
    color: Colors.textSecondary,
  },
  selectedSizeButtonText: {
    color: Colors.textOnPrimary,
  },
  quantityContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: Spacing.md,
  },
  quantityLabel: {
    fontSize: FontSizes.sm,
    fontWeight: '600',
    color: Colors.text,
  },
  quantityControls: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: Spacing.md,
  },
  quantityButton: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: Colors.secondary,
    justifyContent: 'center',
    alignItems: 'center',
  },
  quantityButtonText: {
    fontSize: FontSizes.lg,
    fontWeight: 'bold',
    color: Colors.textOnPrimary,
  },
  quantityText: {
    fontSize: FontSizes.md,
    fontWeight: '600',
    color: Colors.text,
    minWidth: 24,
    textAlign: 'center',
  },
  addButton: {
    backgroundColor: Colors.success,
    borderRadius: BorderRadius.medium,
    paddingVertical: Spacing.sm + 4,
    alignItems: 'center',
  },
  addButtonText: {
    fontSize: FontSizes.md,
    fontWeight: 'bold',
    color: Colors.textOnPrimary,
  },
});
