@echo off
chcp 65001 >nul
cls

echo ==========================================
echo    📱 OBTENIR LE QR CODE - YASSINAPP
echo ==========================================
echo.
echo 🎯 Pour obtenir le QR code et tester l'application:
echo.
echo 📋 ÉTAPES À SUIVRE:
echo.
echo 1️⃣ OUVREZ UNE NOUVELLE INVITE DE COMMANDE
echo    - Clic droit sur le bureau
echo    - "Ouvrir dans le terminal" ou "Invite de commandes"
echo.
echo 2️⃣ NAVIGUEZ VERS LE DOSSIER
echo    Tapez: cd "C:\Users\<USER>\Documents\AppCaisse\YassinApp"
echo.
echo 3️⃣ LANCEZ EXPO
echo    Tapez: npx expo start
echo.
echo 4️⃣ ATTENDEZ LE QR CODE
echo    - Le QR code apparaîtra dans le terminal
echo    - Il ressemble à un carré de points noirs et blancs
echo.
echo 5️⃣ SCANNEZ AVEC EXPO GO
echo    - Ouvrez l'app "Expo Go" sur votre téléphone
echo    - Scannez le QR code
echo    - L'application YassinApp se lancera
echo.
echo ==========================================
echo.
echo 📱 ALTERNATIVE - NAVIGATEUR WEB:
echo.
echo Si le QR code n'apparaît pas:
echo 1. Dans le terminal, appuyez sur la touche "w"
echo 2. L'application s'ouvrira dans votre navigateur
echo 3. Vous pourrez tester depuis votre ordinateur
echo.
echo ==========================================
echo.
echo 🔑 IDENTIFIANTS DE CONNEXION:
echo    Nom d'utilisateur: admin
echo    Mot de passe: 123
echo.
echo 🍕 FONCTIONNALITÉS À TESTER:
echo    1. Connexion admin
echo    2. Cliquez sur "Nos Offres"
echo    3. Sélectionnez "Offre Dimanche" (22 DT)
echo    4. Choisissez votre pizza
echo    5. Ajoutez au panier
echo    6. Finalisez la commande
echo.
echo ==========================================
echo.
echo 💡 CONSEILS:
echo    - Assurez-vous d'être connecté au même WiFi
echo    - Si ça ne marche pas, essayez: npx expo start --tunnel
echo    - Le QR code change à chaque lancement
echo.
echo ==========================================
echo.
echo 🚀 PRÊT À TESTER YASSINAPP AVEC LES OFFRES !
echo.
pause
