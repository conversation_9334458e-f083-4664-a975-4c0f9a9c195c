@echo off
chcp 65001 >nul
cls

echo ==========================================
echo    📱 BUILD APK STANDALONE - CAISSE 2.0
echo ==========================================
echo.
echo 🎯 Application: Pizza Caisse Manager
echo 📱 Version: 2.0.0 (Build 37)
echo 🏗️ Type: APK Standalone (sans Expo Go)
echo.
echo ✅ Fonctionnalités incluses:
echo    ✓ Gestion des commandes
echo    ✓ Gestion du stock avec continuité
echo    ✓ Statistiques avec export PDF
echo    ✓ Interface tactile optimisée
echo    ✓ Saisie avec virgule (12,5)
echo    ✓ Calendrier pour dates
echo    ✓ PDF avec design rouge professionnel
echo.
echo ==========================================

cd /d "C:\Users\<USER>\Documents\AppCaisse\Apk test\PizzaAPK"

echo 📂 Répertoire: %CD%
echo.

echo 🧹 Nettoyage...
if exist ".expo" rmdir /s /q ".expo" >nul 2>&1
echo ✅ Cache nettoyé
echo.

echo 🔐 Connexion à Expo...
npx eas login

echo.
echo 🏗️ Démarrage de la build APK standalone...
echo ⏳ Temps estimé: 10-15 minutes
echo 📱 Création d'un APK installable directement
echo.

npx eas build --platform android --profile production

echo.
echo ==========================================
echo 🎉 BUILD APK TERMINÉE !
echo.
echo 📱 Votre APK standalone est prêt !
echo 🔗 Lien de téléchargement affiché ci-dessus
echo.
echo 📋 Instructions d'installation:
echo 1. Copiez le lien de téléchargement
echo 2. Téléchargez l'APK sur votre téléphone Android
echo 3. Ouvrez les Paramètres > Sécurité
echo 4. Activez "Sources inconnues" ou "Installer des apps inconnues"
echo 5. Ouvrez le fichier APK téléchargé
echo 6. Appuyez sur "Installer"
echo 7. L'application "Caisse" sera installée !
echo.
echo 🚀 Plus besoin d'Expo Go !
echo 📱 Application standalone complète
echo.
echo ==========================================
pause
