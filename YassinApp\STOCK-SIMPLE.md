# 📦 TABLEAU DE STOCK SIMPLE - YASSINAPP

## 🎯 Interface Simplifiée

Le tableau de stock est maintenant **simple et direct** - vous voyez tout le tableau à remplir sans complications.

## 📱 Interface du Stock

### 🗓️ Navigation Simple

```
┌─────────────────────────────────────┐
│ [← Retour]  Stock du Jour  [💾 Sauver] │
├─────────────────────────────────────┤
│ [← Hier]    Lundi 15 juillet   [<PERSON><PERSON><PERSON> →] │
└─────────────────────────────────────┘
```

### 📊 Tableau Direct

```
┌─────────────┬──────────────┬─────────┬─────────────┐
│   Produit   │ Stock Initial│ Entrées │ Stock Final │
├─────────────┼──────────────┼─────────┼─────────────┤
│ Pâte à pizza│   [____]     │[____]   │   25,000    │
│    (kg)     │              │         │             │
├─────────────┼──────────────┼─────────┼─────────────┤
│ Mozzarella  │   [____]     │[____]   │   10,500    │
│    (kg)     │              │         │             │
├─────────────┼──────────────┼─────────┼─────────────┤
│ Sauce tomate│   [____]     │[____]   │    8,000    │
│    (L)      │              │         │             │
├─────────────┼──────────────┼─────────┼─────────────┤
│ Pepperoni   │   [____]     │[____]   │    5,500    │
│    (kg)     │              │         │             │
├─────────────┼──────────────┼─────────┼─────────────┤
│ Olives      │   [____]     │[____]   │    3,000    │
│    (kg)     │              │         │             │
└─────────────┴──────────────┴─────────┴─────────────┘
```

## 🔢 Comment Utiliser

### ✏️ Saisie Simple

1. **Stock Initial** : Tapez la quantité du début de journée
2. **Entrées** : Tapez les livraisons/achats du jour
3. **Stock Final** : Se calcule automatiquement (Initial + Entrées)

### 📝 Exemple d'Utilisation

**Jour 1 :**
```
Pâte à pizza:
- Stock Initial: [25,0] ← Vous tapez
- Entrées: [10,0] ← Vous tapez
- Stock Final: 35,0 ← Calculé automatiquement
```

**Jour 2 (Automatique) :**
```
Pâte à pizza:
- Stock Initial: [35,0] ← Automatique (= Stock Final Jour 1)
- Entrées: [5,0] ← Vous tapez
- Stock Final: 40,0 ← Calculé automatiquement
```

## 🔄 Synchronisation Automatique

### 📅 Principe Simple

**Stock Final du jour J = Stock Initial du jour J+1**

Cette synchronisation se fait **automatiquement** quand vous sauvegardez.

### 🎯 Workflow Quotidien

1. **Ouvrez le stock** du jour
2. **Remplissez le tableau** :
   - Stock Initial (si pas déjà rempli)
   - Entrées du jour
3. **Cliquez "💾 Sauver"**
4. **Le lendemain** : Stock Initial déjà rempli automatiquement

## 📱 Fonctionnalités

### ⌨️ Saisie Optimisée

- **Virgule acceptée** : Tapez `12,5` directement
- **Clavier numérique** : Automatique sur mobile
- **Placeholder** : `0,000` pour guider la saisie
- **Calcul temps réel** : Stock Final mis à jour instantanément

### 🗓️ Navigation Facile

- **← Hier** : Voir le stock du jour précédent
- **Demain →** : Voir le stock du jour suivant
- **Date affichée** : Jour actuel en français

### 💾 Sauvegarde Simple

- **Un seul bouton** : "💾 Sauver"
- **Synchronisation automatique** : Avec le jour suivant
- **Pas de complications** : Juste sauvegarder et c'est tout

## 🎯 Produits Inclus

Le tableau contient **5 produits essentiels** :

1. **Pâte à pizza** (kg)
2. **Mozzarella** (kg)
3. **Sauce tomate** (L)
4. **Pepperoni** (kg)
5. **Olives** (kg)

## ⚠️ Alertes Visuelles

- **Texte rouge** : Stock final en dessous du seuil
- **Icône ⚠️** : Alerte de stock bas
- **Pas de notifications** : Juste des indicateurs visuels

## 🚀 Test du Tableau

### 📱 Lancement
```cmd
cd "C:\Users\<USER>\Documents\AppCaisse\YassinApp"
npx expo start
```

### 🔑 Connexion
- Nom d'utilisateur : `admin`
- Mot de passe : `123`

### 📦 Test du Stock

1. **Cliquez "Stock"** dans l'écran principal
2. **Vous voyez le tableau** directement
3. **Remplissez les champs** :
   - Stock Initial Pâte : `25,0`
   - Entrées Pâte : `10,0`
   - Vérifiez Stock Final : `35,0`
4. **Sauvegardez** avec "💾 Sauver"
5. **Passez à demain** avec "Demain →"
6. **Vérifiez** : Stock Initial Pâte = `35,0` ✅

## 🎉 Avantages du Tableau Simple

### ✅ Simplicité Maximale
- **Tableau direct** : Tout visible d'un coup
- **Pas de complications** : Juste remplir et sauver
- **Interface claire** : Colonnes bien définies

### ✅ Synchronisation Automatique
- **Pas de ressaisie** : Stock Final J → Stock Initial J+1
- **Continuité garantie** : Entre tous les jours
- **Sauvegarde simple** : Un seul bouton

### ✅ Optimisation Mobile
- **Tableau responsive** : S'adapte à l'écran
- **Saisie tactile** : Clavier numérique
- **Navigation fluide** : Boutons clairs

### ✅ Calcul Automatique
- **Stock Final** : Calculé en temps réel
- **Pas d'erreurs** : Calcul automatique
- **Virgule supportée** : Format français

## 📋 Résumé d'Utilisation

1. **Ouvrez Stock** → Tableau affiché directement
2. **Remplissez** Stock Initial et Entrées
3. **Vérifiez** Stock Final (automatique)
4. **Sauvegardez** avec 💾
5. **Demain** → Stock Initial déjà rempli

**📦 Votre tableau de stock est maintenant simple, direct et automatique !**
