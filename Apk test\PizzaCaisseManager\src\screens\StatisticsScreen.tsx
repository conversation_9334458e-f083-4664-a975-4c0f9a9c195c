import React, { useState, useEffect, useCallback } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  RefreshControl,
  FlatList,
} from 'react-native';
import { Statistics, Order } from '../types';
import { StatisticsService } from '../services/StatisticsService';
import { StorageService } from '../services/StorageService';

interface StatisticsScreenProps {
  navigation: any;
}

export const StatisticsScreen: React.FC<StatisticsScreenProps> = ({ navigation }) => {
  const [statistics, setStatistics] = useState<Statistics | null>(null);
  const [recentOrders, setRecentOrders] = useState<Order[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);

  const loadData = useCallback(async () => {
    try {
      const [stats, orders] = await Promise.all([
        StatisticsService.getRealtimeStats(),
        StorageService.getOrders()
      ]);
      
      setStatistics(stats);
      // Prendre les 10 dernières commandes
      setRecentOrders(orders.slice(-10).reverse());
    } catch (error) {
      console.error('Erreur lors du chargement des données:', error);
    } finally {
      setIsLoading(false);
      setRefreshing(false);
    }
  }, []);

  useEffect(() => {
    loadData();
  }, [loadData]);

  const onRefresh = useCallback(() => {
    setRefreshing(true);
    loadData();
  }, [loadData]);

  const formatDate = (date: Date) => {
    return new Intl.DateTimeFormat('fr-FR', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    }).format(date);
  };

  const getStatusColor = (status: Order['status']) => {
    switch (status) {
      case 'confirmed': return '#3498db';
      case 'preparing': return '#f39c12';
      case 'ready': return '#27ae60';
      case 'delivered': return '#2ecc71';
      default: return '#95a5a6';
    }
  };

  const getStatusText = (status: Order['status']) => {
    switch (status) {
      case 'confirmed': return 'Confirmée';
      case 'preparing': return 'En préparation';
      case 'ready': return 'Prête';
      case 'delivered': return 'Livrée';
      default: return 'En attente';
    }
  };

  const renderOrderItem = ({ item }: { item: Order }) => (
    <View style={styles.orderItem}>
      <View style={styles.orderHeader}>
        <Text style={styles.orderId}>Commande #{item.id}</Text>
        <View style={[styles.statusBadge, { backgroundColor: getStatusColor(item.status) }]}>
          <Text style={styles.statusText}>{getStatusText(item.status)}</Text>
        </View>
      </View>
      <Text style={styles.customerName}>{item.customer.name}</Text>
      <Text style={styles.orderDate}>{formatDate(item.createdAt)}</Text>
      <Text style={styles.orderTotal}>{item.totalAmount.toFixed(2)}€</Text>
    </View>
  );

  if (isLoading) {
    return (
      <View style={[styles.container, styles.centered]}>
        <Text style={styles.loadingText}>Chargement des statistiques...</Text>
      </View>
    );
  }

  return (
    <ScrollView 
      style={styles.container}
      refreshControl={
        <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
      }
    >
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity onPress={() => navigation.goBack()}>
          <Text style={styles.backButton}>← Retour</Text>
        </TouchableOpacity>
        <Text style={styles.title}>Statistiques</Text>
      </View>

      {statistics && (
        <>
          {/* Statistiques du jour */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>📊 Aujourd'hui</Text>
            <View style={styles.statsGrid}>
              <View style={styles.statCard}>
                <Text style={styles.statValue}>{statistics.today.totalOrders}</Text>
                <Text style={styles.statLabel}>Commandes</Text>
              </View>
              <View style={styles.statCard}>
                <Text style={styles.statValue}>{statistics.today.totalRevenue.toFixed(2)}€</Text>
                <Text style={styles.statLabel}>Chiffre d'affaires</Text>
              </View>
              <View style={styles.statCard}>
                <Text style={styles.statValue}>{statistics.today.averageOrderValue.toFixed(2)}€</Text>
                <Text style={styles.statLabel}>Panier moyen</Text>
              </View>
              <View style={styles.statCard}>
                <Text style={styles.statValue}>{statistics.today.popularPizza || 'Aucune'}</Text>
                <Text style={styles.statLabel}>Pizza populaire</Text>
              </View>
            </View>
          </View>

          {/* Statistiques globales */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>🏆 Statistiques globales</Text>
            <View style={styles.statsGrid}>
              <View style={styles.statCard}>
                <Text style={styles.statValue}>{statistics.allTimeStats.totalOrders}</Text>
                <Text style={styles.statLabel}>Total commandes</Text>
              </View>
              <View style={styles.statCard}>
                <Text style={styles.statValue}>{statistics.allTimeStats.totalRevenue.toFixed(2)}€</Text>
                <Text style={styles.statLabel}>CA total</Text>
              </View>
              <View style={styles.statCard}>
                <Text style={styles.statValue}>{statistics.allTimeStats.averageOrderValue.toFixed(2)}€</Text>
                <Text style={styles.statLabel}>Panier moyen</Text>
              </View>
              <View style={styles.statCard}>
                <Text style={styles.statValue}>{statistics.allTimeStats.mostPopularPizza || 'Aucune'}</Text>
                <Text style={styles.statLabel}>Pizza favorite</Text>
              </View>
            </View>
          </View>
        </>
      )}

      {/* Commandes récentes */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>🕒 Commandes récentes</Text>
        {recentOrders.length > 0 ? (
          <FlatList
            data={recentOrders}
            renderItem={renderOrderItem}
            keyExtractor={(item) => item.id}
            scrollEnabled={false}
          />
        ) : (
          <Text style={styles.noOrdersText}>Aucune commande pour le moment</Text>
        )}
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  centered: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  header: {
    backgroundColor: '#2c3e50',
    paddingTop: 50,
    paddingBottom: 20,
    paddingHorizontal: 16,
    flexDirection: 'row',
    alignItems: 'center',
  },
  backButton: {
    color: '#3498db',
    fontSize: 16,
    fontWeight: '600',
    marginRight: 16,
  },
  title: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#fff',
  },
  loadingText: {
    fontSize: 16,
    color: '#666',
  },
  section: {
    backgroundColor: '#fff',
    margin: 16,
    borderRadius: 12,
    padding: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 16,
  },
  statsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 12,
  },
  statCard: {
    flex: 1,
    minWidth: '45%',
    backgroundColor: '#f8f9fa',
    borderRadius: 8,
    padding: 12,
    alignItems: 'center',
  },
  statValue: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#2c3e50',
    marginBottom: 4,
  },
  statLabel: {
    fontSize: 12,
    color: '#666',
    textAlign: 'center',
  },
  orderItem: {
    backgroundColor: '#f8f9fa',
    borderRadius: 8,
    padding: 12,
    marginBottom: 8,
  },
  orderHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  orderId: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#333',
  },
  statusBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  statusText: {
    fontSize: 12,
    fontWeight: '600',
    color: '#fff',
  },
  customerName: {
    fontSize: 16,
    fontWeight: '600',
    color: '#2c3e50',
    marginBottom: 4,
  },
  orderDate: {
    fontSize: 12,
    color: '#666',
    marginBottom: 4,
  },
  orderTotal: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#e74c3c',
  },
  noOrdersText: {
    textAlign: 'center',
    color: '#666',
    fontStyle: 'italic',
    marginTop: 20,
  },
});
