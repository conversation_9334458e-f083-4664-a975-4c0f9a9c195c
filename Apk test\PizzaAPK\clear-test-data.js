/**
 * Script pour nettoyer les données de test
 * À exécuter dans la console du navigateur
 */

// Fonction pour nettoyer les données de test
function clearTestData() {
  try {
    console.log('🧹 Nettoyage des données de test...');
    
    // Récupérer les commandes existantes
    const ordersData = localStorage.getItem('pizza_orders');
    if (!ordersData) {
      console.log('ℹ️ Aucune donnée trouvée');
      return;
    }
    
    const orders = JSON.parse(ordersData);
    console.log('📊 Commandes trouvées:', orders.length);
    
    // Filtrer pour garder seulement les commandes non-test
    const realOrders = orders.filter(order => !order.id.startsWith('test-'));
    
    // Sauvegarder les commandes filtrées
    localStorage.setItem('pizza_orders', JSON.stringify(realOrders));
    
    console.log('✅ Nettoyage terminé');
    console.log('📊 Commandes restantes:', realOrders.length);
    console.log('🗑️ Commandes de test supprimées:', orders.length - realOrders.length);
    
    // Rafraîchir la page pour voir les changements
    if (confirm('Voulez-vous rafraîchir la page pour voir les changements ?')) {
      window.location.reload();
    }
    
  } catch (error) {
    console.error('❌ Erreur lors du nettoyage:', error);
  }
}

// Fonction pour voir les statistiques actuelles
function showCurrentData() {
  try {
    const ordersData = localStorage.getItem('pizza_orders');
    if (!ordersData) {
      console.log('ℹ️ Aucune donnée trouvée');
      return;
    }
    
    const orders = JSON.parse(ordersData);
    const testOrders = orders.filter(order => order.id.startsWith('test-'));
    const realOrders = orders.filter(order => !order.id.startsWith('test-'));
    
    console.log('📊 Statistiques actuelles:');
    console.log('  - Total commandes:', orders.length);
    console.log('  - Commandes de test:', testOrders.length);
    console.log('  - Commandes réelles:', realOrders.length);
    
    if (testOrders.length > 0) {
      console.log('🧪 Commandes de test trouvées:');
      testOrders.forEach(order => {
        console.log(`  - ${order.customer?.name || 'Client inconnu'}: ${order.totalAmount} DT`);
      });
    }
    
  } catch (error) {
    console.error('❌ Erreur:', error);
  }
}

// Rendre les fonctions disponibles globalement
if (typeof window !== 'undefined') {
  window.clearTestData = clearTestData;
  window.showCurrentData = showCurrentData;
  
  console.log('🧹 Script de nettoyage chargé!');
  console.log('📝 Utilisez:');
  console.log('  - clearTestData() pour nettoyer les données de test');
  console.log('  - showCurrentData() pour voir les données actuelles');
}

// Auto-exécution si appelé directement
if (typeof module === 'undefined') {
  showCurrentData();
  console.log('\n💡 Tapez clearTestData() pour nettoyer les données de test');
}
