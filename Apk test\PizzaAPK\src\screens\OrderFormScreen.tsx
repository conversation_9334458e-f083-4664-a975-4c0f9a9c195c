import React, { useState, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TextInput,
  TouchableOpacity,
  ScrollView,
  Alert,
  FlatList,
  KeyboardAvoidingView,
  Platform,
  Keyboard,
  TouchableWithoutFeedback,
} from 'react-native';
import { OrderItem, Order, Customer } from '../types';
import { StorageService } from '../services/StorageService';
import { StatisticsService } from '../services/StatisticsService';
import { useCustomModal } from '../hooks/useCustomModal';

interface OrderFormScreenProps {
  navigation: any;
  route: {
    params: {
      cartItems: OrderItem[];
    };
  };
}

export const OrderFormScreen: React.FC<OrderFormScreenProps> = ({ navigation, route }) => {
  const { cartItems } = route.params;
  const [customerName, setCustomerName] = useState('');
  const [customerAddress, setCustomerAddress] = useState('');
  const [customerPhone, setCustomerPhone] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Références pour la navigation entre les champs
  const addressInputRef = useRef<TextInput>(null);
  const phoneInputRef = useRef<TextInput>(null);
  const scrollViewRef = useRef<ScrollView>(null);

  const { showAlert, showOrderSuccess, ModalComponent } = useCustomModal();

  const totalAmount = cartItems.reduce((sum, item) => sum + item.totalPrice, 0);

  const validateForm = (): boolean => {
    if (!customerName.trim()) {
      showAlert('Erreur', 'Veuillez saisir le nom du client');
      return false;
    }
    if (!customerAddress.trim()) {
      showAlert('Erreur', 'Veuillez saisir l\'adresse du client');
      return false;
    }
    if (!customerPhone.trim()) {
      showAlert('Erreur', 'Veuillez saisir le numéro de téléphone du client');
      return false;
    }

    // Validation basique du numéro de téléphone
    const phoneRegex = /^[0-9+\-\s()]{8,}$/;
    if (!phoneRegex.test(customerPhone.trim())) {
      showAlert('Erreur', 'Veuillez saisir un numéro de téléphone valide');
      return false;
    }

    return true;
  };

  const handleConfirmOrder = async () => {
    if (!validateForm()) return;

    setIsSubmitting(true);

    try {
      const customer: Customer = {
        name: customerName.trim(),
        address: customerAddress.trim(),
        phoneNumber: customerPhone.trim(),
      };

      const order: Order = {
        id: Date.now().toString(),
        customer,
        items: cartItems,
        totalAmount,
        status: 'confirmed',
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      await StorageService.saveOrder(order);
      await StatisticsService.calculateStatistics();

      // Réinitialiser immédiatement le formulaire pour la prochaine commande
      setCustomerName('');
      setCustomerAddress('');
      setCustomerPhone('');

      // Afficher le modal de succès avec navigation
      showOrderSuccess(
        {
          orderId: order.id,
          customerName: customer.name,
          customerAddress: customer.address,
          customerPhone: customer.phone,
          totalAmount: totalAmount,
        },
        () => {
          // Aller aux statistiques
          navigation.navigate('Statistics');
        },
        () => {
          // Retourner à l'accueil pour une nouvelle commande
          navigation.reset({
            index: 0,
            routes: [{ name: 'PizzaList' }],
          });
        }
      );
    } catch (error) {
      console.error('Erreur lors de la confirmation de la commande:', error);
      showAlert('Erreur', 'Une erreur est survenue lors de la confirmation de la commande');
    } finally {
      setIsSubmitting(false);
    }
  };

  const renderCartItem = ({ item }: { item: OrderItem }) => (
    <View style={styles.cartItem}>
      <View style={styles.cartItemInfo}>
        <Text style={styles.cartItemName}>{item.pizzaName}</Text>
        <Text style={styles.cartItemDetails}>
          Taille {item.size} × {item.quantity}
        </Text>
      </View>
      <Text style={styles.cartItemPrice}>{item.totalPrice.toFixed(2)} DT</Text>
    </View>
  );

  return (
    <KeyboardAvoidingView
      style={styles.container}
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      keyboardVerticalOffset={Platform.OS === 'ios' ? 64 : 0}
      enabled
    >
      <TouchableWithoutFeedback onPress={Keyboard.dismiss}>
        <View style={styles.container}>
          <ScrollView
            ref={scrollViewRef}
            style={styles.scrollView}
            contentContainerStyle={styles.scrollContent}
            keyboardShouldPersistTaps="handled"
            showsVerticalScrollIndicator={false}
          >
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity onPress={() => navigation.goBack()}>
          <Text style={styles.backButton}>← Retour</Text>
        </TouchableOpacity>
        <Text style={styles.title}>Finaliser la commande</Text>
      </View>

      {/* Récapitulatif du panier */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Récapitulatif de la commande</Text>
        <FlatList
          data={cartItems}
          renderItem={renderCartItem}
          keyExtractor={(item, index) => `${item.pizzaId}-${item.size}-${index}`}
          scrollEnabled={false}
        />
        <View style={styles.totalContainer}>
          <Text style={styles.totalText}>Total: {totalAmount.toFixed(2)} DT</Text>
        </View>
      </View>

      {/* Formulaire client */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Informations client</Text>
        
        <View style={styles.inputContainer}>
          <Text style={styles.inputLabel}>Nom du client *</Text>
          <TextInput
            style={styles.input}
            value={customerName}
            onChangeText={setCustomerName}
            placeholder="Saisissez le nom du client"
            autoCapitalize="words"
            returnKeyType="next"
            onSubmitEditing={() => addressInputRef.current?.focus()}
            blurOnSubmit={false}
          />
        </View>

        <View style={styles.inputContainer}>
          <Text style={styles.inputLabel}>Adresse *</Text>
          <TextInput
            ref={addressInputRef}
            style={[styles.input, styles.multilineInput]}
            value={customerAddress}
            onChangeText={setCustomerAddress}
            placeholder="Saisissez l'adresse de livraison"
            multiline
            numberOfLines={3}
            autoCapitalize="sentences"
            returnKeyType="next"
            onSubmitEditing={() => phoneInputRef.current?.focus()}
            blurOnSubmit={false}
            onFocus={() => {
              // Scroll vers le champ quand il est focalisé
              setTimeout(() => {
                scrollViewRef.current?.scrollTo({ y: 200, animated: true });
              }, 200);
            }}
          />
        </View>

        <View style={styles.inputContainer}>
          <Text style={styles.inputLabel}>Numéro de téléphone *</Text>
          <TextInput
            ref={phoneInputRef}
            style={styles.input}
            value={customerPhone}
            onChangeText={setCustomerPhone}
            placeholder="Saisissez le numéro de téléphone"
            keyboardType="phone-pad"
            returnKeyType="done"
            onSubmitEditing={Keyboard.dismiss}
            onFocus={() => {
              // Scroll vers le champ quand il est focalisé
              setTimeout(() => {
                scrollViewRef.current?.scrollTo({ y: 400, animated: true });
              }, 200);
            }}
          />
        </View>
      </View>

      {/* Bouton de confirmation */}
      <View style={styles.buttonContainer}>
        <TouchableOpacity
          style={[styles.confirmButton, isSubmitting && styles.disabledButton]}
          onPress={handleConfirmOrder}
          disabled={isSubmitting}
        >
          <Text style={styles.confirmButtonText}>
            {isSubmitting ? 'Confirmation...' : `Confirmer la commande - ${totalAmount.toFixed(2)} DT`}
          </Text>
        </TouchableOpacity>
      </View>
          </ScrollView>
          {ModalComponent}
        </View>
      </TouchableWithoutFeedback>
    </KeyboardAvoidingView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    paddingBottom: 150, // Espace supplémentaire en bas pour éviter que le clavier cache le contenu
    flexGrow: 1,
  },
  header: {
    backgroundColor: '#2c3e50',
    paddingTop: 50,
    paddingBottom: 20,
    paddingHorizontal: 16,
    flexDirection: 'row',
    alignItems: 'center',
  },
  backButton: {
    color: '#3498db',
    fontSize: 16,
    fontWeight: '600',
    marginRight: 16,
  },
  title: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#fff',
  },
  section: {
    backgroundColor: '#fff',
    margin: 16,
    borderRadius: 12,
    padding: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 16,
  },
  cartItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 8,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  cartItemInfo: {
    flex: 1,
  },
  cartItemName: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
  },
  cartItemDetails: {
    fontSize: 14,
    color: '#666',
    marginTop: 2,
  },
  cartItemPrice: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#e74c3c',
  },
  totalContainer: {
    marginTop: 16,
    paddingTop: 16,
    borderTopWidth: 2,
    borderTopColor: '#e74c3c',
    alignItems: 'flex-end',
  },
  totalText: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#e74c3c',
  },
  inputContainer: {
    marginBottom: 16,
  },
  inputLabel: {
    fontSize: 14,
    fontWeight: '600',
    color: '#333',
    marginBottom: 8,
  },
  input: {
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 12,
    fontSize: 16,
    backgroundColor: '#fff',
    minHeight: 48, // Hauteur minimale pour faciliter la sélection tactile
  },
  multilineInput: {
    height: 90,
    textAlignVertical: 'top',
    paddingTop: 12, // Padding supplémentaire pour le texte multiline
  },
  buttonContainer: {
    padding: 16,
  },
  confirmButton: {
    backgroundColor: '#27ae60',
    borderRadius: 8,
    paddingVertical: 16,
    alignItems: 'center',
  },
  disabledButton: {
    backgroundColor: '#95a5a6',
  },
  confirmButtonText: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#fff',
  },
});
