{"compilerOptions": {"target": "es2020", "lib": ["es2020", "dom"], "allowJs": true, "skipLibCheck": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "strict": true, "forceConsistentCasingInFileNames": true, "moduleResolution": "node", "resolveJsonModule": true, "isolatedModules": true, "noEmit": true, "jsx": "react-jsx"}, "include": ["src/**/*", "App.tsx", "index.js"], "exclude": ["node_modules", "android", "ios"]}