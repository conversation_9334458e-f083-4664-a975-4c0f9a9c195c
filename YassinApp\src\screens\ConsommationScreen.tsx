import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Alert,
} from 'react-native';
import { Colors, FontSizes, Spacing, BorderRadius } from '../styles/theme';
import { ConsommationService, ConsommationPeriode } from '../services/ConsommationService';

interface ConsommationScreenProps {
  navigation: any;
}

export const ConsommationScreen: React.FC<ConsommationScreenProps> = ({ navigation }) => {
  const [consommationSemaine, setConsommationSemaine] = useState<ConsommationPeriode | null>(null);
  const [consommationMois, setConsommationMois] = useState<ConsommationPeriode | null>(null);
  const [consommationsParSemaines, setConsommationsParSemaines] = useState<ConsommationPeriode[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [selectedPeriod, setSelectedPeriod] = useState<'semaine' | 'mois' | 'historique'>('semaine');

  useEffect(() => {
    loadConsommations();
  }, []);

  const loadConsommations = async () => {
    try {
      setLoading(true);
      
      // Charger les différentes périodes
      const [semaine, mois, historique] = await Promise.all([
        ConsommationService.getConsommationSemaineCourante(),
        ConsommationService.getConsommationMoisCourant(),
        ConsommationService.getConsommationParSemaines(4)
      ]);

      setConsommationSemaine(semaine);
      setConsommationMois(mois);
      setConsommationsParSemaines(historique);
    } catch (error) {
      console.error('Erreur lors du chargement des consommations:', error);
      Alert.alert('Erreur', 'Impossible de charger les statistiques de consommation');
    } finally {
      setLoading(false);
    }
  };

  const renderConsommationItem = (item: any) => (
    <View key={item.id} style={styles.consommationRow}>
      <View style={styles.productInfo}>
        <Text style={styles.productName}>{item.name}</Text>
        <Text style={styles.productUnit}>({item.unit})</Text>
      </View>
      <View style={styles.consommationInfo}>
        <Text style={styles.consommationValue}>
          {item.consommation.toFixed(1).replace('.', ',')}
        </Text>
        <Text style={styles.consommationLabel}>consommé</Text>
      </View>
    </View>
  );

  const renderPeriodeCard = (consommation: ConsommationPeriode, title: string) => (
    <View style={styles.periodeCard}>
      <Text style={styles.periodeTitle}>{title}</Text>
      <Text style={styles.periodeDate}>
        {ConsommationService.formatPeriode(consommation)}
      </Text>
      <Text style={styles.periodeJours}>
        {consommation.nombreJours} jour{consommation.nombreJours > 1 ? 's' : ''} de données
      </Text>
      
      {consommation.items.length > 0 ? (
        consommation.items.map(renderConsommationItem)
      ) : (
        <Text style={styles.noDataText}>Aucune donnée disponible</Text>
      )}
    </View>
  );

  const renderHistoriqueWeeks = () => (
    <View style={styles.historiqueContainer}>
      <Text style={styles.sectionTitle}>📊 Historique par Semaines</Text>
      {consommationsParSemaines.map((consommation, index) => (
        <View key={index} style={styles.weekCard}>
          <Text style={styles.weekTitle}>
            Semaine {index + 1} - {ConsommationService.formatPeriode(consommation)}
          </Text>
          <Text style={styles.weekDays}>
            {consommation.nombreJours} jour{consommation.nombreJours > 1 ? 's' : ''}
          </Text>
          
          {consommation.items.length > 0 ? (
            <View style={styles.weekItems}>
              {consommation.items.slice(0, 3).map(item => (
                <View key={item.id} style={styles.weekItem}>
                  <Text style={styles.weekItemName}>{item.name}</Text>
                  <Text style={styles.weekItemValue}>
                    {item.consommation.toFixed(1).replace('.', ',')} {item.unit}
                  </Text>
                </View>
              ))}
            </View>
          ) : (
            <Text style={styles.noDataText}>Pas de données</Text>
          )}
        </View>
      ))}
    </View>
  );

  if (loading) {
    return (
      <View style={[styles.container, styles.centered]}>
        <Text style={styles.loadingText}>Calcul des consommations...</Text>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity onPress={() => navigation.goBack()} style={styles.backButton}>
          <Text style={styles.backButtonText}>← Retour</Text>
        </TouchableOpacity>
        <Text style={styles.title}>Consommation</Text>
        <TouchableOpacity style={styles.refreshButton} onPress={loadConsommations}>
          <Text style={styles.refreshButtonText}>🔄</Text>
        </TouchableOpacity>
      </View>

      {/* Sélecteur de période */}
      <View style={styles.periodSelector}>
        <TouchableOpacity 
          style={[styles.periodButton, selectedPeriod === 'semaine' && styles.periodButtonActive]}
          onPress={() => setSelectedPeriod('semaine')}
        >
          <Text style={[styles.periodButtonText, selectedPeriod === 'semaine' && styles.periodButtonTextActive]}>
            Semaine
          </Text>
        </TouchableOpacity>
        <TouchableOpacity 
          style={[styles.periodButton, selectedPeriod === 'mois' && styles.periodButtonActive]}
          onPress={() => setSelectedPeriod('mois')}
        >
          <Text style={[styles.periodButtonText, selectedPeriod === 'mois' && styles.periodButtonTextActive]}>
            Mois
          </Text>
        </TouchableOpacity>
        <TouchableOpacity 
          style={[styles.periodButton, selectedPeriod === 'historique' && styles.periodButtonActive]}
          onPress={() => setSelectedPeriod('historique')}
        >
          <Text style={[styles.periodButtonText, selectedPeriod === 'historique' && styles.periodButtonTextActive]}>
            Historique
          </Text>
        </TouchableOpacity>
      </View>

      {/* Contenu */}
      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {selectedPeriod === 'semaine' && consommationSemaine && 
          renderPeriodeCard(consommationSemaine, '📅 Cette Semaine')
        }
        
        {selectedPeriod === 'mois' && consommationMois && 
          renderPeriodeCard(consommationMois, '📆 Ce Mois')
        }
        
        {selectedPeriod === 'historique' && renderHistoriqueWeeks()}
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
  },
  centered: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    fontSize: FontSizes.lg,
    color: Colors.textSecondary,
  },
  header: {
    backgroundColor: Colors.secondary,
    paddingTop: 50,
    paddingBottom: 15,
    paddingHorizontal: 16,
    flexDirection: 'row',
    alignItems: 'center',
  },
  backButton: {
    marginRight: 10,
  },
  backButtonText: {
    color: Colors.textOnDark,
    fontSize: FontSizes.md,
    fontWeight: '600',
  },
  title: {
    fontSize: FontSizes.xl,
    fontWeight: 'bold',
    color: Colors.textOnDark,
    flex: 1,
  },
  refreshButton: {
    backgroundColor: Colors.accent,
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: BorderRadius.medium,
  },
  refreshButtonText: {
    color: Colors.textOnPrimary,
    fontSize: FontSizes.lg,
  },
  periodSelector: {
    flexDirection: 'row',
    backgroundColor: Colors.surface,
    paddingHorizontal: Spacing.md,
    paddingVertical: Spacing.sm,
  },
  periodButton: {
    flex: 1,
    paddingVertical: Spacing.sm,
    paddingHorizontal: Spacing.md,
    marginHorizontal: Spacing.xs,
    borderRadius: BorderRadius.medium,
    backgroundColor: Colors.light,
    alignItems: 'center',
  },
  periodButtonActive: {
    backgroundColor: Colors.primary,
  },
  periodButtonText: {
    fontSize: FontSizes.sm,
    fontWeight: '600',
    color: Colors.textPrimary,
  },
  periodButtonTextActive: {
    color: Colors.textOnPrimary,
  },
  content: {
    flex: 1,
    padding: Spacing.md,
  },
  periodeCard: {
    backgroundColor: Colors.surface,
    borderRadius: BorderRadius.medium,
    padding: Spacing.md,
    marginBottom: Spacing.md,
    borderWidth: 1,
    borderColor: Colors.border,
  },
  periodeTitle: {
    fontSize: FontSizes.lg,
    fontWeight: 'bold',
    color: Colors.textPrimary,
    marginBottom: Spacing.xs,
  },
  periodeDate: {
    fontSize: FontSizes.md,
    color: Colors.textSecondary,
    marginBottom: Spacing.xs,
  },
  periodeJours: {
    fontSize: FontSizes.sm,
    color: Colors.textSecondary,
    marginBottom: Spacing.md,
  },
  consommationRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: Spacing.sm,
    borderBottomWidth: 1,
    borderBottomColor: Colors.border,
  },
  productInfo: {
    flex: 1,
  },
  productName: {
    fontSize: FontSizes.md,
    fontWeight: '600',
    color: Colors.textPrimary,
  },
  productUnit: {
    fontSize: FontSizes.sm,
    color: Colors.textSecondary,
  },
  consommationInfo: {
    alignItems: 'flex-end',
  },
  consommationValue: {
    fontSize: FontSizes.lg,
    fontWeight: 'bold',
    color: Colors.danger,
  },
  consommationLabel: {
    fontSize: FontSizes.xs,
    color: Colors.textSecondary,
  },
  noDataText: {
    fontSize: FontSizes.md,
    color: Colors.textSecondary,
    textAlign: 'center',
    fontStyle: 'italic',
    paddingVertical: Spacing.lg,
  },
  sectionTitle: {
    fontSize: FontSizes.lg,
    fontWeight: 'bold',
    color: Colors.textPrimary,
    marginBottom: Spacing.md,
  },
  historiqueContainer: {
    marginTop: Spacing.sm,
  },
  weekCard: {
    backgroundColor: Colors.surface,
    borderRadius: BorderRadius.medium,
    padding: Spacing.md,
    marginBottom: Spacing.sm,
    borderWidth: 1,
    borderColor: Colors.border,
  },
  weekTitle: {
    fontSize: FontSizes.md,
    fontWeight: 'bold',
    color: Colors.textPrimary,
  },
  weekDays: {
    fontSize: FontSizes.sm,
    color: Colors.textSecondary,
    marginBottom: Spacing.sm,
  },
  weekItems: {
    marginTop: Spacing.sm,
  },
  weekItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingVertical: Spacing.xs,
  },
  weekItemName: {
    fontSize: FontSizes.sm,
    color: Colors.textPrimary,
  },
  weekItemValue: {
    fontSize: FontSizes.sm,
    fontWeight: '600',
    color: Colors.danger,
  },
});
