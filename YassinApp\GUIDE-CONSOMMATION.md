# 📈 GUIDE DE CONSOMMATION - YASSINAPP

## 🎯 Statistiques de Consommation par Semaine

Avec vos données de stock saisies quotidiennement, vous pouvez maintenant voir combien vous avez consommé par semaine, mois, et suivre l'historique.

## 📊 Comment ça Fonctionne

### 🔢 Calcul de la Consommation

**Formule :** `Consommation = Stock Initial + Entrées - Stock Final`

**Exemple :**
- Stock Initial : 25,0 kg
- Entrées : 10,0 kg  
- Stock Final : 30,0 kg
- **Consommation = 25,0 + 10,0 - 30,0 = 5,0 kg**

### 📅 Périodes Disponibles

1. **Cette Semaine** : Lundi à Dimanche courant
2. **Ce Mois** : Du 1er au dernier jour du mois
3. **Historique** : 4 dernières semaines

## 📱 Interface de Consommation

```
┌─────────────────────────────────────┐
│ [← Retour]  Consommation      [🔄]  │
├─────────────────────────────────────┤
│ [<PERSON><PERSON><PERSON>] [<PERSON><PERSON>] [Historique]       │
├─────────────────────────────────────┤
│ 📅 Cette Semaine                    │
│ 15 jul - 21 jul                     │
│ 5 jours de données                  │
├─────────────────────────────────────┤
│ Pâte à pizza (kg)        5,2 consommé│
│ Mozzarella (kg)          3,1 consommé│
│ Sauce tomate (L)         2,5 consommé│
│ Pepperoni (kg)           1,8 consommé│
│ Olives (kg)              0,9 consommé│
└─────────────────────────────────────┘
```

## 🚀 Comment Accéder

### 📱 Navigation

1. **Lancez l'application** : `npx expo start`
2. **Connexion** : admin / 123
3. **Écran principal** → Cliquez **"📈 Consommation"**

### 🔄 Actualisation

- **Bouton 🔄** : Recalcule les statistiques
- **Automatique** : Se met à jour avec vos nouvelles saisies de stock

## 📊 Exemple d'Utilisation

### 📝 Données de Stock (5 jours)

**Lundi :**
```
Pâte à pizza: Initial 25,0 + Entrées 10,0 - Final 30,0 = Consommation 5,0 kg
Mozzarella:   Initial 15,0 + Entrées  5,0 - Final 18,0 = Consommation 2,0 kg
```

**Mardi :**
```
Pâte à pizza: Initial 30,0 + Entrées  5,0 - Final 32,0 = Consommation 3,0 kg
Mozzarella:   Initial 18,0 + Entrées  0,0 - Final 17,0 = Consommation 1,0 kg
```

**Mercredi :**
```
Pâte à pizza: Initial 32,0 + Entrées  0,0 - Final 28,0 = Consommation 4,0 kg
Mozzarella:   Initial 17,0 + Entrées  2,0 - Final 18,5 = Consommation 0,5 kg
```

**Jeudi :**
```
Pâte à pizza: Initial 28,0 + Entrées  8,0 - Final 33,0 = Consommation 3,0 kg
Mozzarella:   Initial 18,5 + Entrées  0,0 - Final 18,0 = Consommation 0,5 kg
```

**Vendredi :**
```
Pâte à pizza: Initial 33,0 + Entrées  0,0 - Final 31,0 = Consommation 2,0 kg
Mozzarella:   Initial 18,0 + Entrées  1,0 - Final 18,5 = Consommation 0,5 kg
```

### 📈 Résultat Semaine

**Consommation Totale :**
- **Pâte à pizza** : 5,0 + 3,0 + 4,0 + 3,0 + 2,0 = **17,0 kg**
- **Mozzarella** : 2,0 + 1,0 + 0,5 + 0,5 + 0,5 = **4,5 kg**

## 📅 Vues Disponibles

### 🗓️ Vue Semaine

```
📅 Cette Semaine
15 jul - 21 jul
5 jours de données

Pâte à pizza (kg)        17,0 consommé
Mozzarella (kg)           4,5 consommé
Sauce tomate (L)          8,2 consommé
Pepperoni (kg)            3,1 consommé
Olives (kg)               2,3 consommé
```

### 📆 Vue Mois

```
📆 Ce Mois
Juillet 2025
22 jours de données

Pâte à pizza (kg)        68,5 consommé
Mozzarella (kg)          18,2 consommé
Sauce tomate (L)         32,1 consommé
Pepperoni (kg)           12,8 consommé
Olives (kg)               9,4 consommé
```

### 📊 Vue Historique

```
📊 Historique par Semaines

Semaine 1 - 1 jul - 7 jul
7 jours
Pâte à pizza: 15,2 kg
Mozzarella: 3,8 kg
Sauce tomate: 6,1 L

Semaine 2 - 8 jul - 14 jul  
7 jours
Pâte à pizza: 18,3 kg
Mozzarella: 4,2 kg
Sauce tomate: 7,5 L

Semaine 3 - 15 jul - 21 jul
5 jours
Pâte à pizza: 17,0 kg
Mozzarella: 4,5 kg
Sauce tomate: 8,2 L

Semaine 4 - 22 jul - 28 jul
0 jours (semaine courante)
Pas de données
```

## 🎯 Utilité des Statistiques

### 📈 Suivi de Performance

- **Consommation moyenne** par semaine
- **Tendances** d'utilisation des produits
- **Optimisation** des commandes

### 📦 Gestion des Achats

- **Prévoir** les quantités à commander
- **Éviter** les ruptures de stock
- **Réduire** le gaspillage

### 💰 Contrôle des Coûts

- **Identifier** les produits les plus consommés
- **Calculer** les coûts par semaine/mois
- **Optimiser** les recettes

## 🔍 Analyse des Données

### 📊 Exemple d'Analyse

**Consommation Pâte à pizza :**
- Semaine 1 : 15,2 kg
- Semaine 2 : 18,3 kg (+20%)
- Semaine 3 : 17,0 kg (-7%)
- **Moyenne** : 16,8 kg/semaine

**Conclusions :**
- Consommation stable autour de 17 kg/semaine
- Pic en semaine 2 (événement spécial ?)
- Commander 20 kg/semaine pour sécurité

### 📈 Tendances

- **Hausse** : Augmentation de clientèle
- **Baisse** : Période calme ou gaspillage réduit
- **Stabilité** : Consommation maîtrisée

## 🚨 Points d'Attention

### ⚠️ Données Nécessaires

- **Stock quotidien** : Saisissez chaque jour
- **Précision** : Valeurs exactes importantes
- **Continuité** : Pas de jours manqués

### ⚠️ Interprétation

- **Stock Final élevé** : Moins de consommation
- **Entrées importantes** : Fausse la consommation du jour
- **Jours manqués** : Statistiques incomplètes

## 📋 Workflow Recommandé

### 🗓️ Quotidien

1. **Saisissez le stock** chaque jour
2. **Vérifiez** les valeurs avant sauvegarde
3. **Consultez** les statistiques en fin de semaine

### 📊 Hebdomadaire

1. **Analysez** la consommation de la semaine
2. **Comparez** avec les semaines précédentes
3. **Planifiez** les commandes de la semaine suivante

### 📈 Mensuel

1. **Bilan** de consommation du mois
2. **Tendances** sur plusieurs semaines
3. **Ajustements** des quantités de stock

## 🎉 Avantages

### ✅ Visibilité

- **Consommation réelle** basée sur vos données
- **Historique** complet et détaillé
- **Comparaisons** entre périodes

### ✅ Simplicité

- **Calcul automatique** à partir du stock
- **Interface claire** et intuitive
- **Mise à jour** en temps réel

### ✅ Utilité

- **Aide à la décision** pour les commandes
- **Contrôle** des coûts et du gaspillage
- **Optimisation** de la gestion

**📈 Vos données de stock deviennent des statistiques utiles pour votre business !**
