# Base de Données des Pizzas - YassinApp V3.4

## 🍕 Liste des Pizzas de Base Ajoutée

### **Pizzas Officielles du Restaurant**
```
1.  MARGUERITA
2.  REINE
3.  PEPPERONI LOVERS
4.  NEPTUNE
5.  TEXAS CHICKEN
6.  CHICKEN / POULET
7.  BBQ CHICKEN
8.  COMPAGNARDE
9.  BOSTON
10. CALIFORNIA
11. CHEESEBURGER
12. STEAK ROQUEFORT
13. MEAT LOVERS
14. WITH THE LOT
15. CHESSY LOVERS
16. ALASKA
17. MR JO PIZZA
```

## 🔧 Système de Reconnaissance Intelligent

### **1. Correspondances Automatiques**
Le système reconnaît automatiquement les variantes et les normalise :

```javascript
// Exemples de correspondances automatiques
'Margherita' → 'MARGUERITA'
'Pepperoni' → 'PEPPERONI LOVERS'
'Quatre Fromages' → 'CHESSY LOVERS'
'Fruits de Mer' → 'NEPTUNE'
'Végétarienne' → 'COMPAGNARDE'
'Poulet BBQ' → 'BBQ CHICKEN'
'Viande' → 'MEAT LOVERS'
```

### **2. Nettoyage Automatique des Noms**
```javascript
// Le système supprime automatiquement :
'Pizza Margherita L' → 'MARGUERITA'
'Offre Dimanche - Pizza Pepperoni L' → 'PEPPERONI LOVERS'
'Promo Family - Pizza Reine M' → 'REINE'
'BBQ Chicken XL' → 'BBQ CHICKEN'
```

### **3. Reconnaissance Partielle**
```javascript
// Correspondances intelligentes :
'Chicken BBQ' → 'BBQ CHICKEN'
'Barbecue Poulet' → 'BBQ CHICKEN'
'Steak' → 'STEAK ROQUEFORT'
'Burger Pizza' → 'CHEESEBURGER'
```

## 📊 Impact sur les Statistiques

### **Avant (Sans Base de Données)**
```
🍕 Pizzas populaires

1  Pizza Margherita L     ████████ 4 commandes
2  Pepperoni M           ██████   3 commandes  
3  Offre - Quatre Fromages ████   2 commandes
4  BBQ Chicken XL        ██       1 commande
```

### **Après (Avec Base de Données)**
```
🍕 Pizzas populaires

1  MARGUERITA           ████████████ 8 commandes
2  PEPPERONI LOVERS     ██████████   6 commandes  
3  CHESSY LOVERS        ████████     5 commandes
4  BBQ CHICKEN          ██████       4 commandes
5  NEPTUNE              ████         3 commandes
```

## 🎯 Fonctionnalités

### **Reconnaissance Multi-Format**
- **Offres** : Extrait les pizzas des choix d'offres
- **Commandes individuelles** : Reconnaît les pizzas normales
- **Tailles variables** : M, L, XL automatiquement supprimées
- **Langues multiples** : Français et anglais supportés

### **Correspondances Intelligentes**
```javascript
// Variantes de Marguerita
'Margherita', 'Marguerite', 'Margarita' → 'MARGUERITA'

// Variantes de Fromage
'Quatre Fromages', '4 Fromages', 'Cheese' → 'CHESSY LOVERS'

// Variantes de Poulet
'Chicken', 'Poulet' → 'CHICKEN'
'BBQ Chicken', 'Poulet BBQ', 'Barbecue Chicken' → 'BBQ CHICKEN'

// Variantes de Viande
'Meat', 'Viande', 'Viandes' → 'MEAT LOVERS'
```

### **Gestion des Offres**
```javascript
// Extraction automatique des pizzas dans les offres
Offre Dimanche:
- Pizza Large: Margherita → Compte pour 'MARGUERITA'
- Pizza Large: Pepperoni → Compte pour 'PEPPERONI LOVERS'

Promo Family:
- Pizza 1: Reine → Compte pour 'REINE'
- Pizza 2: BBQ Chicken → Compte pour 'BBQ CHICKEN'
```

## 📁 Structure Technique

### **Fichier de Configuration**
```
YassinApp/src/data/pizzaDatabase.ts
```

### **Fonctions Principales**
```javascript
// Liste des pizzas de base
export const BASE_PIZZAS = [...]

// Correspondances pour normaliser
export const PIZZA_MAPPINGS = {...}

// Mots-clés pour identifier les pizzas
export const PIZZA_KEYWORDS = [...]

// Fonction de nettoyage
export const cleanPizzaName = (itemName: string): string | null

// Vérification si c'est une pizza
export const isPizzaItem = (itemName: string): boolean
```

### **Intégration dans les Statistiques**
```javascript
// Dans calculateOverviewStats()
monthOrders.forEach(order => {
  order.items.forEach(item => {
    if (item.isOffer && item.offerItems) {
      // Traiter les offres
      const cleanName = cleanPizzaNameFromDB(offerItem.choice);
    } else {
      // Traiter les pizzas individuelles
      const cleanName = cleanPizzaNameFromDB(item.name);
    }
    
    if (cleanName) {
      pizzaCount[cleanName] = (pizzaCount[cleanName] || 0) + item.quantity;
    }
  });
});
```

## 🚀 Test du Système

### **Étape 1 : Passer des Commandes Variées**
1. **Commande 1** : Offre Dimanche avec Margherita
2. **Commande 2** : Pizza Pepperoni L individuelle
3. **Commande 3** : Promo Family avec Quatre Fromages et BBQ Chicken
4. **Commande 4** : Pizza Fruits de Mer M

### **Étape 2 : Vérifier les Statistiques**
1. **Aller** dans Statistiques → Vue d'ensemble
2. **Faire défiler** jusqu'à "Pizzas populaires"
3. **Vérifier** que les noms sont normalisés :
   - Margherita → MARGUERITA
   - Pepperoni → PEPPERONI LOVERS
   - Quatre Fromages → CHESSY LOVERS
   - BBQ Chicken → BBQ CHICKEN
   - Fruits de Mer → NEPTUNE

### **Étape 3 : Test des Correspondances**
1. **Passer** une commande avec "Pizza Végétarienne"
2. **Vérifier** qu'elle apparaît comme "COMPAGNARDE"
3. **Passer** une commande avec "Cheese Pizza"
4. **Vérifier** qu'elle apparaît comme "CHESSY LOVERS"

## 💡 Avantages

### **Analyse Précise**
✅ **Regroupement intelligent** : Toutes les variantes d'une pizza comptées ensemble  
✅ **Noms standardisés** : Liste officielle du restaurant  
✅ **Statistiques cohérentes** : Pas de doublons ou de variantes séparées  
✅ **Reconnaissance automatique** : Pas besoin de saisie parfaite  

### **Facilité de Maintenance**
✅ **Base de données centralisée** : Un seul fichier à modifier  
✅ **Ajout facile** : Nouvelles pizzas ajoutables facilement  
✅ **Correspondances extensibles** : Nouvelles variantes ajoutables  
✅ **Code réutilisable** : Fonctions utilisables ailleurs  

### **Robustesse**
✅ **Gestion des erreurs** : Noms mal orthographiés reconnus  
✅ **Multi-langues** : Français et anglais supportés  
✅ **Formats variables** : Offres et commandes individuelles  
✅ **Évolutif** : Facilement extensible  

## 🔄 Maintenance

### **Pour Ajouter une Nouvelle Pizza**
1. **Ouvrir** `src/data/pizzaDatabase.ts`
2. **Ajouter** le nom dans `BASE_PIZZAS`
3. **Ajouter** les variantes dans `PIZZA_MAPPINGS` si nécessaire
4. **Sauvegarder** - Les statistiques se mettent à jour automatiquement

### **Pour Ajouter des Correspondances**
1. **Identifier** les variantes courantes
2. **Ajouter** dans `PIZZA_MAPPINGS`
3. **Tester** avec des commandes d'exemple

## 🎉 Résultat Final

Le système de pizzas est maintenant **intelligent et précis** avec :

1. **17 pizzas de base** officielles du restaurant
2. **Reconnaissance automatique** des variantes et fautes de frappe
3. **Nettoyage intelligent** des noms (tailles, préfixes, etc.)
4. **Correspondances étendues** pour toutes les variantes courantes
5. **Statistiques précises** avec regroupement correct
6. **Maintenance facile** via un fichier de configuration

**Les statistiques de pizzas populaires sont maintenant parfaitement précises !** 🍕📊

---

**Version** : YassinApp 3.4  
**Date** : 21 juillet 2025  
**Ajout** : Base de données des pizzas, Reconnaissance intelligente, Correspondances automatiques  
**Statut** : ✅ Système de pizzas complet et fonctionnel
