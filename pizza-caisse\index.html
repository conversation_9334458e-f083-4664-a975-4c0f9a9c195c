<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Pizza Caisse Manager</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Arial', sans-serif;
            background: linear-gradient(135deg, #2c3e50, #34495e);
            color: white;
            min-height: 100vh;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
        }

        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
            color: #e74c3c;
        }

        .nav-buttons {
            display: flex;
            justify-content: center;
            gap: 20px;
            margin-bottom: 30px;
            flex-wrap: wrap;
        }

        .nav-btn {
            background: #e74c3c;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 1rem;
            transition: all 0.3s ease;
        }

        .nav-btn:hover {
            background: #c0392b;
            transform: translateY(-2px);
        }

        .nav-btn.active {
            background: #27ae60;
        }

        .screen {
            display: none;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 12px;
            padding: 30px;
            backdrop-filter: blur(10px);
        }

        .screen.active {
            display: block;
        }

        .pizza-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .pizza-card {
            background: rgba(255, 255, 255, 0.15);
            border-radius: 12px;
            padding: 20px;
            text-align: center;
            transition: transform 0.3s ease;
        }

        .pizza-card:hover {
            transform: translateY(-5px);
        }

        .pizza-card h3 {
            color: #e74c3c;
            margin-bottom: 10px;
            font-size: 1.3rem;
        }

        .pizza-card p {
            margin-bottom: 15px;
            opacity: 0.9;
        }

        .pizza-card .price {
            font-size: 1.2rem;
            font-weight: bold;
            color: #f39c12;
            margin-bottom: 15px;
        }

        .add-btn {
            background: #27ae60;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 0.9rem;
            transition: background 0.3s ease;
        }

        .add-btn:hover {
            background: #229954;
        }

        .cart {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 8px;
            padding: 20px;
            margin-top: 20px;
        }

        .cart h3 {
            color: #e74c3c;
            margin-bottom: 15px;
        }

        .cart-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px 0;
            border-bottom: 1px solid rgba(255, 255, 255, 0.2);
        }

        .cart-total {
            font-size: 1.2rem;
            font-weight: bold;
            color: #f39c12;
            text-align: right;
            margin-top: 15px;
        }

        .order-btn {
            background: #e74c3c;
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 1.1rem;
            width: 100%;
            margin-top: 20px;
            transition: background 0.3s ease;
        }

        .order-btn:hover {
            background: #c0392b;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
        }

        .stat-card {
            background: rgba(255, 255, 255, 0.15);
            border-radius: 12px;
            padding: 20px;
            text-align: center;
        }

        .stat-number {
            font-size: 2rem;
            font-weight: bold;
            color: #e74c3c;
            margin-bottom: 10px;
        }

        .stat-label {
            opacity: 0.9;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 5px;
            color: #ecf0f1;
        }

        .form-group input {
            width: 100%;
            padding: 10px;
            border: none;
            border-radius: 6px;
            background: rgba(255, 255, 255, 0.9);
            color: #2c3e50;
        }

        @media (max-width: 768px) {
            .nav-buttons {
                flex-direction: column;
                align-items: center;
            }

            .pizza-grid {
                grid-template-columns: 1fr;
            }

            .header h1 {
                font-size: 2rem;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🍕 Pizza Caisse Manager</h1>
            <p>Système de gestion de commandes de pizza</p>
        </div>

        <div class="nav-buttons">
            <button class="nav-btn active" onclick="showScreen('pizza-list')">Liste des Pizzas</button>
            <button class="nav-btn" onclick="showScreen('order-form')">Commande</button>
            <button class="nav-btn" onclick="showScreen('statistics')">Statistiques</button>
        </div>

        <!-- Écran Liste des Pizzas -->
        <div id="pizza-list" class="screen active">
            <h2>Menu des Pizzas</h2>
            <div class="pizza-grid" id="pizzaGrid">
                <!-- Les pizzas seront ajoutées dynamiquement -->
            </div>
            
            <div class="cart" id="cart" style="display: none;">
                <h3>🛒 Panier</h3>
                <div id="cartItems"></div>
                <div class="cart-total" id="cartTotal">Total: 0.00 DT</div>
                <button class="order-btn" onclick="showScreen('order-form')">Finaliser la commande</button>
            </div>
        </div>

        <!-- Écran Commande -->
        <div id="order-form" class="screen">
            <h2>Finaliser la commande</h2>
            <div class="form-group">
                <label for="customerName">Nom du client:</label>
                <input type="text" id="customerName" placeholder="Entrez le nom du client">
            </div>
            <div class="form-group">
                <label for="customerPhone">Téléphone:</label>
                <input type="tel" id="customerPhone" placeholder="Entrez le numéro de téléphone">
            </div>
            <div class="form-group">
                <label for="customerAddress">Adresse:</label>
                <input type="text" id="customerAddress" placeholder="Entrez l'adresse de livraison">
            </div>
            
            <div class="cart">
                <h3>Résumé de la commande</h3>
                <div id="orderSummary"></div>
                <div class="cart-total" id="orderTotal">Total: 0.00 DT</div>
                <button class="order-btn" onclick="submitOrder()">Confirmer la commande</button>
            </div>
        </div>

        <!-- Écran Statistiques -->
        <div id="statistics" class="screen">
            <h2>Statistiques</h2>
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-number" id="totalOrders">0</div>
                    <div class="stat-label">Commandes totales</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="totalRevenue">0.00 DT</div>
                    <div class="stat-label">Chiffre d'affaires</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="avgOrderValue">0.00 DT</div>
                    <div class="stat-label">Panier moyen</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="todayOrders">0</div>
                    <div class="stat-label">Commandes aujourd'hui</div>
                </div>
            </div>
        </div>
    </div>

    <script src="app.js"></script>
</body>
</html>
