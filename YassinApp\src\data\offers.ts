import { Offer } from '../types';
import { BASE_PIZZAS } from './pizzaDatabase';

// Fonction pour générer les choix de pizzas avec taille
const generatePizzaChoices = (size: string): string[] => {
  return BASE_PIZZAS.map(pizza => `${pizza} ${size}`);
};

export const OFFERS: Offer[] = [
  {
    id: 'dimanche',
    name: 'Offre Dimanche',
    description: 'Pizza Large au choix',
    price: 22.00,
    image: '🍕',
    category: 'Dimanche',
    items: [
      {
        type: 'pizza',
        name: 'Pizza Large au choix',
        quantity: 1,
        choices: generatePizzaChoices('Large')
      }
    ],
    isActive: true
  },
  {
    id: 'family',
    name: 'Promo Family',
    description: '2 Pizzas L + Nuggets + Coca 33cl',
    price: 45.00,
    image: '👨‍👩‍👧‍👦',
    category: 'Family',
    items: [
      {
        type: 'pizza',
        name: 'Pizza Large au choix',
        quantity: 2,
        choices: generatePizzaChoices('Large')
      },
      {
        type: 'side',
        name: 'Nuggets',
        quantity: 1,
        choices: ['Nuggets (6 pièces)']
      },
      {
        type: 'drink',
        name: 'Coca 33cl',
        quantity: 1,
        choices: ['Coca-Cola 33cl']
      }
    ],
    isActive: true
  },
  {
    id: 'duo',
    name: 'Offre Duo',
    description: '2 Pizzas M + 2 Boissons',
    price: 35.00,
    image: '💑',
    category: 'Duo',
    items: [
      {
        type: 'pizza',
        name: 'Pizza Moyenne au choix',
        quantity: 2,
        choices: generatePizzaChoices('Moyenne')
      },
      {
        type: 'drink',
        name: 'Boisson au choix',
        quantity: 2,
        choices: [
          'Coca-Cola 33cl',
          'Fanta 33cl',
          'Sprite 33cl',
          'Eau 50cl'
        ]
      }
    ],
    isActive: true
  },
  {
    id: 'student',
    name: 'Menu Étudiant',
    description: 'Pizza M + Frites + Boisson',
    price: 18.00,
    image: '🎓',
    category: 'Étudiant',
    items: [
      {
        type: 'pizza',
        name: 'Pizza Moyenne au choix',
        quantity: 1,
        choices: generatePizzaChoices('Moyenne')
      },
      {
        type: 'side',
        name: 'Frites',
        quantity: 1,
        choices: ['Frites portion']
      },
      {
        type: 'drink',
        name: 'Boisson',
        quantity: 1,
        choices: [
          'Coca-Cola 33cl',
          'Fanta 33cl',
          'Sprite 33cl'
        ]
      }
    ],
    isActive: true
  },
  {
    id: 'weekend',
    name: 'Spécial Weekend',
    description: '3 Pizzas L + Dessert',
    price: 65.00,
    image: '🎉',
    category: 'Weekend',
    items: [
      {
        type: 'pizza',
        name: 'Pizza Large au choix',
        quantity: 3,
        choices: generatePizzaChoices('Large')
      },
      {
        type: 'dessert',
        name: 'Dessert au choix',
        quantity: 1,
        choices: [
          'Tiramisu',
          'Panna Cotta',
          'Glace Vanille'
        ]
      }
    ],
    isActive: true
  },
  {
    id: 'express',
    name: 'Menu Express',
    description: 'Pizza S + Boisson',
    price: 12.00,
    image: '⚡',
    category: 'Express',
    items: [
      {
        type: 'pizza',
        name: 'Pizza Petite au choix',
        quantity: 1,
        choices: generatePizzaChoices('Petite')
      },
      {
        type: 'drink',
        name: 'Boisson',
        quantity: 1,
        choices: [
          'Coca-Cola 33cl',
          'Eau 50cl'
        ]
      }
    ],
    isActive: true
  },
  {
    id: 'pizzas-petites',
    name: 'Pizzas Petites',
    description: 'Toutes nos pizzas en taille Petite',
    price: 12.00,
    image: '🍕',
    category: 'Pizzas',
    items: [
      {
        type: 'pizza',
        name: 'Pizza Petite au choix',
        quantity: 1,
        choices: generatePizzaChoices('Petite')
      }
    ],
    isActive: true
  },
  {
    id: 'pizzas-moyennes',
    name: 'Pizzas Moyennes',
    description: 'Toutes nos pizzas en taille Moyenne',
    price: 18.00,
    image: '🍕',
    category: 'Pizzas',
    items: [
      {
        type: 'pizza',
        name: 'Pizza Moyenne au choix',
        quantity: 1,
        choices: generatePizzaChoices('Moyenne')
      }
    ],
    isActive: true
  },
  {
    id: 'pizzas-larges',
    name: 'Pizzas Larges',
    description: 'Toutes nos pizzas en taille Large',
    price: 24.00,
    image: '🍕',
    category: 'Pizzas',
    items: [
      {
        type: 'pizza',
        name: 'Pizza Large au choix',
        quantity: 1,
        choices: generatePizzaChoices('Large')
      }
    ],
    isActive: true
  }
];
