import { Offer } from '../types';
import { BASE_PIZZAS } from './pizzaDatabase';

// Fonction pour générer les choix de pizzas avec taille
const generatePizzaChoices = (size: string): string[] => {
  return BASE_PIZZAS.map(pizza => `${pizza} ${size}`);
};

export const OFFERS: Offer[] = [
  // Carte spéciale "Tous les Produits" (en premier)
  {
    id: 'tous-les-produits',
    name: 'Tous les Produits',
    description: 'Pizzas, accompagnements, boissons... Découvrez notre carte complète !',
    price: 0, // Prix non applicable
    image: '🍕',
    category: 'Navigation',
    items: [], // Pas d'items pour cette carte spéciale
    isActive: true
  },

  // Offres prioritaires
  {
    id: 'offre-family',
    name: 'Offre Family',
    description: '2 pizza large + frite + 6 nuggets + boisson 1L',
    price: 35.00,
    image: '👨‍👩‍👧‍👦',
    category: 'Famille',
    items: [
      {
        type: 'pizza',
        name: 'Pizza Large 1',
        quantity: 1,
        choices: generatePizzaChoices('Large')
      },
      {
        type: 'pizza',
        name: 'Pizza Large 2',
        quantity: 1,
        choices: generatePizzaChoices('Large')
      },
      {
        type: 'side',
        name: 'Frite',
        quantity: 1,
        choices: ['Frites portion']
      },
      {
        type: 'side',
        name: 'Nuggets',
        quantity: 1,
        choices: ['Nuggets (6 pièces)']
      },
      {
        type: 'drink',
        name: 'Boisson 1L',
        quantity: 1,
        choices: ['Boisson 1L']
      }
    ],
    isActive: true
  },
  {
    id: 'offre-solo',
    name: 'Offre Solo',
    description: '1 pizza Large + frite + 4 nuggets + 1 canette',
    price: 19.00,
    image: '🍕',
    category: 'Solo',
    items: [
      {
        type: 'pizza',
        name: 'Pizza Large au choix',
        quantity: 1,
        choices: generatePizzaChoices('Large')
      },
      {
        type: 'side',
        name: 'Frite',
        quantity: 1,
        choices: ['Frites portion']
      },
      {
        type: 'side',
        name: 'Nuggets',
        quantity: 1,
        choices: ['Nuggets (4 pièces)']
      },
      {
        type: 'drink',
        name: 'Canette',
        quantity: 1,
        choices: ['Canette']
      }
    ],
    isActive: true
  },
  {
    id: 'offre-week',
    name: 'Offre Week',
    description: '2 pizza large',
    price: 20.00,
    image: '🍕',
    category: 'Week',
    items: [
      {
        type: 'pizza',
        name: 'Pizza Large 1',
        quantity: 1,
        choices: generatePizzaChoices('Large')
      },
      {
        type: 'pizza',
        name: 'Pizza Large 2',
        quantity: 1,
        choices: generatePizzaChoices('Large')
      }
    ],
    isActive: true
  },

  // Autres offres
  {
    id: 'promo-2-pizza-large',
    name: 'Promo 2 Pizza Large',
    description: '2 Pizza large au choix',
    price: 22.00,
    image: '🍕',
    category: 'Promo',
    items: [
      {
        type: 'pizza',
        name: 'Pizza Large 1',
        quantity: 1,
        choices: generatePizzaChoices('Large')
      },
      {
        type: 'pizza',
        name: 'Pizza Large 2',
        quantity: 1,
        choices: generatePizzaChoices('Large')
      }
    ],
    isActive: true
  },
  {
    id: 'promo-pizza-m',
    name: 'Promo Pizza M',
    description: 'Pizza médium au choix',
    price: 10.00,
    image: '🍕',
    category: 'Promo',
    items: [
      {
        type: 'pizza',
        name: 'Pizza Médium au choix',
        quantity: 1,
        choices: generatePizzaChoices('Moyenne')
      }
    ],
    isActive: true
  },
  {
    id: 'promo-pizza-l',
    name: 'Promo Pizza L',
    description: 'Pizza Large au choix',
    price: 13.00,
    image: '🍕',
    category: 'Promo',
    items: [
      {
        type: 'pizza',
        name: 'Pizza Large au choix',
        quantity: 1,
        choices: generatePizzaChoices('Large')
      }
    ],
    isActive: true
  },
  {
    id: 'mercredi-duo',
    name: 'Mercredi Duo',
    description: '2 pizza large + boisson 1L',
    price: 26.00,
    image: '💑',
    category: 'Spécial Jour',
    items: [
      {
        type: 'pizza',
        name: 'Pizza Large 1',
        quantity: 1,
        choices: generatePizzaChoices('Large')
      },
      {
        type: 'pizza',
        name: 'Pizza Large 2',
        quantity: 1,
        choices: generatePizzaChoices('Large')
      },
      {
        type: 'drink',
        name: 'Boisson 1L',
        quantity: 1,
        choices: ['Boisson 1L']
      }
    ],
    isActive: true
  },
  {
    id: 'promo-chahya-tayba',
    name: 'Promo Chahya Tayba',
    description: '2 pizza large + frite + 6 nuggets + boisson 1L',
    price: 35.00,
    image: '🌟',
    category: 'Spécial',
    items: [
      {
        type: 'pizza',
        name: 'Pizza Large 1',
        quantity: 1,
        choices: generatePizzaChoices('Large')
      },
      {
        type: 'pizza',
        name: 'Pizza Large 2',
        quantity: 1,
        choices: generatePizzaChoices('Large')
      },
      {
        type: 'side',
        name: 'Frite',
        quantity: 1,
        choices: ['Frites portion']
      },
      {
        type: 'side',
        name: 'Nuggets',
        quantity: 1,
        choices: ['Nuggets (6 pièces)']
      },
      {
        type: 'drink',
        name: 'Boisson 1L',
        quantity: 1,
        choices: ['Boisson 1L']
      }
    ],
    isActive: true
  },
  {
    id: 'offre-eid-mubarek',
    name: 'Offre Eid Mubarek',
    description: '2 pizza large + frite + 6 nuggets',
    price: 29.90,
    image: '🌙',
    category: 'Eid',
    items: [
      {
        type: 'pizza',
        name: 'Pizza Large 1',
        quantity: 1,
        choices: generatePizzaChoices('Large')
      },
      {
        type: 'pizza',
        name: 'Pizza Large 2',
        quantity: 1,
        choices: generatePizzaChoices('Large')
      },
      {
        type: 'side',
        name: 'Frite',
        quantity: 1,
        choices: ['Frites portion']
      },
      {
        type: 'side',
        name: 'Nuggets',
        quantity: 1,
        choices: ['Nuggets (6 pièces)']
      }
    ],
    isActive: true
  },
  {
    id: 'offre-vendredi',
    name: 'Offre Vendredi',
    description: '2 pizza médium + frite + 6 nuggets',
    price: 23.90,
    image: '📅',
    category: 'Spécial Jour',
    items: [
      {
        type: 'pizza',
        name: 'Pizza Médium 1',
        quantity: 1,
        choices: generatePizzaChoices('Moyenne')
      },
      {
        type: 'pizza',
        name: 'Pizza Médium 2',
        quantity: 1,
        choices: generatePizzaChoices('Moyenne')
      },
      {
        type: 'side',
        name: 'Frite',
        quantity: 1,
        choices: ['Frites portion']
      },
      {
        type: 'side',
        name: 'Nuggets',
        quantity: 1,
        choices: ['Nuggets (6 pièces)']
      }
    ],
    isActive: true
  },
  {
    id: 'offre-lundi',
    name: 'Offre Lundi',
    description: '1 pizza médium + frite + 4 nuggets + canette',
    price: 19.00,
    image: '📅',
    category: 'Spécial Jour',
    items: [
      {
        type: 'pizza',
        name: 'Pizza Médium au choix',
        quantity: 1,
        choices: generatePizzaChoices('Moyenne')
      },
      {
        type: 'side',
        name: 'Frite',
        quantity: 1,
        choices: ['Frites portion']
      },
      {
        type: 'side',
        name: 'Nuggets',
        quantity: 1,
        choices: ['Nuggets (4 pièces)']
      },
      {
        type: 'drink',
        name: 'Canette',
        quantity: 1,
        choices: ['Canette']
      }
    ],
    isActive: true
  }
];
