import AsyncStorage from '@react-native-async-storage/async-storage';

// Interface pour les articles du panier
export interface CartItem {
  id: string;
  name: string;
  price: number;
  quantity: number;
  category: string;
  size?: string;
  specialInstructions?: string;
  isOffer?: boolean;
  offerItems?: {
    name: string;
    choice: string;
  }[];
}

// Interface pour le panier complet
export interface Cart {
  items: CartItem[];
  totalAmount: number;
  itemCount: number;
}

class CartServiceClass {
  private static instance: CartServiceClass;
  private cart: Cart = {
    items: [],
    totalAmount: 0,
    itemCount: 0
  };

  private constructor() {
    this.loadCart();
  }

  public static getInstance(): CartServiceClass {
    if (!CartServiceClass.instance) {
      CartServiceClass.instance = new CartServiceClass();
    }
    return CartServiceClass.instance;
  }

  // Charger le panier depuis AsyncStorage
  private async loadCart(): Promise<void> {
    try {
      const cartData = await AsyncStorage.getItem('cart');
      if (cartData) {
        this.cart = JSON.parse(cartData);
      }
    } catch (error) {
      console.error('Erreur lors du chargement du panier:', error);
    }
  }

  // Sauvegarder le panier dans AsyncStorage
  private async saveCart(): Promise<void> {
    try {
      await AsyncStorage.setItem('cart', JSON.stringify(this.cart));
    } catch (error) {
      console.error('Erreur lors de la sauvegarde du panier:', error);
    }
  }

  // Calculer les totaux
  private calculateTotals(): void {
    this.cart.totalAmount = this.cart.items.reduce((total, item) => {
      return total + (item.price * item.quantity);
    }, 0);
    
    this.cart.itemCount = this.cart.items.reduce((count, item) => {
      return count + item.quantity;
    }, 0);
  }

  // Ajouter un article au panier
  public async addItem(item: CartItem): Promise<void> {
    try {
      // Vérifier si l'article existe déjà
      const existingItemIndex = this.cart.items.findIndex(
        cartItem => cartItem.id === item.id
      );

      if (existingItemIndex >= 0) {
        // Augmenter la quantité si l'article existe
        this.cart.items[existingItemIndex].quantity += item.quantity;
      } else {
        // Ajouter le nouvel article
        this.cart.items.push(item);
      }

      this.calculateTotals();
      await this.saveCart();
    } catch (error) {
      console.error('Erreur lors de l\'ajout au panier:', error);
      throw error;
    }
  }

  // Supprimer un article du panier
  public async removeItem(itemId: string): Promise<void> {
    try {
      this.cart.items = this.cart.items.filter(item => item.id !== itemId);
      this.calculateTotals();
      await this.saveCart();
    } catch (error) {
      console.error('Erreur lors de la suppression du panier:', error);
      throw error;
    }
  }

  // Mettre à jour la quantité d'un article
  public async updateItemQuantity(itemId: string, quantity: number): Promise<void> {
    try {
      const itemIndex = this.cart.items.findIndex(item => item.id === itemId);
      
      if (itemIndex >= 0) {
        if (quantity <= 0) {
          // Supprimer l'article si la quantité est 0 ou négative
          await this.removeItem(itemId);
        } else {
          this.cart.items[itemIndex].quantity = quantity;
          this.calculateTotals();
          await this.saveCart();
        }
      }
    } catch (error) {
      console.error('Erreur lors de la mise à jour de la quantité:', error);
      throw error;
    }
  }

  // Vider le panier
  public async clearCart(): Promise<void> {
    try {
      this.cart = {
        items: [],
        totalAmount: 0,
        itemCount: 0
      };
      await this.saveCart();
    } catch (error) {
      console.error('Erreur lors du vidage du panier:', error);
      throw error;
    }
  }

  // Obtenir le panier actuel
  public getCart(): Cart {
    return { ...this.cart };
  }

  // Obtenir le nombre d'articles
  public getItemCount(): number {
    return this.cart.itemCount;
  }

  // Obtenir le montant total
  public getTotalAmount(): number {
    return this.cart.totalAmount;
  }

  // Vérifier si le panier est vide
  public isEmpty(): boolean {
    return this.cart.items.length === 0;
  }

  // Obtenir un article spécifique
  public getItem(itemId: string): CartItem | undefined {
    return this.cart.items.find(item => item.id === itemId);
  }

  // Ajouter une offre complète au panier
  public async addOffer(offer: {
    id: string;
    name: string;
    price: number;
    items: { name: string; choice: string }[];
  }): Promise<void> {
    try {
      const cartItem: CartItem = {
        id: `offer-${offer.id}-${Date.now()}`,
        name: offer.name,
        price: offer.price,
        quantity: 1,
        category: 'offer',
        isOffer: true,
        offerItems: offer.items
      };

      await this.addItem(cartItem);
    } catch (error) {
      console.error('Erreur lors de l\'ajout de l\'offre au panier:', error);
      throw error;
    }
  }

  // Obtenir les articles par catégorie
  public getItemsByCategory(category: string): CartItem[] {
    return this.cart.items.filter(item => item.category === category);
  }

  // Obtenir un résumé du panier pour l'affichage
  public getCartSummary(): {
    itemCount: number;
    totalAmount: number;
    categories: { [key: string]: number };
  } {
    const categories: { [key: string]: number } = {};
    
    this.cart.items.forEach(item => {
      if (!categories[item.category]) {
        categories[item.category] = 0;
      }
      categories[item.category] += item.quantity;
    });

    return {
      itemCount: this.cart.itemCount,
      totalAmount: this.cart.totalAmount,
      categories
    };
  }
}

// Exporter l'instance singleton
export const CartService = CartServiceClass.getInstance();
