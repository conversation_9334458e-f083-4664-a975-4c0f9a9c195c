import React, { useState, useEffect } from 'react';
import { View, StyleSheet, StatusBar, Animated, Dimensions, Platform } from 'react-native';
import { AdminScreen } from './src/screens/AdminScreen';
import { LoginScreen } from './src/screens/LoginScreen';
import { OrderFormScreen } from './src/screens/OrderFormScreen';
import { StatisticsScreen } from './src/screens/StatisticsScreen';
import { StockScreen } from './src/screens/StockScreen';
import { ConsommationScreen } from './src/screens/ConsommationScreen';
import { DataResetScreen } from './src/screens/DataResetScreen';
import { PromotionScreen } from './src/screens/PromotionScreen';
import { OffersScreen } from './src/screens/OffersScreen';
import { ClientSpaceScreen } from './src/screens/ClientSpaceScreen';
import { OrderDetailScreen } from './src/screens/OrderDetailScreen';
import { ProductsScreen } from './src/screens/ProductsScreen';
import { Colors } from './src/styles/theme';

type Screen = 'Admin' | 'OrderForm' | 'Statistics' | 'Stock' | 'Consommation' | 'DataReset' | 'Promotion' | 'Offers' | 'ClientSpace' | 'OrderDetail' | 'Products';

function App(): React.JSX.Element {
  const [isLoggedIn, setIsLoggedIn] = useState(false);
  const [currentScreen, setCurrentScreen] = useState<Screen>('Admin');
  const [orderData, setOrderData] = useState<any>(null);
  const fadeAnim = new Animated.Value(1);

  const navigate = (screen: Screen, data?: any) => {
    // Animation de transition
    Animated.sequence([
      Animated.timing(fadeAnim, {
        toValue: 0.3,
        duration: 150,
        useNativeDriver: true,
      }),
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 150,
        useNativeDriver: true,
      }),
    ]).start();

    if (data) setOrderData(data);
    setCurrentScreen(screen);
  };

  const handleLogin = () => {
    setIsLoggedIn(true);
  };

  const handleLogout = () => {
    setIsLoggedIn(false);
    setCurrentScreen('Admin');
  };

  if (!isLoggedIn) {
    return (
      <View style={styles.container}>
        <StatusBar
          barStyle="light-content"
          backgroundColor={Colors.secondary}
          translucent={Platform.OS === 'android'}
        />
        <LoginScreen onLogin={handleLogin} />
      </View>
    );
  }

  const renderScreen = () => {
    const mockNavigation = {
      navigate,
      goBack: () => setCurrentScreen('Admin'),
      logout: handleLogout,
    };

    const mockRoute = {
      params: orderData,
    };

    switch (currentScreen) {
      case 'Admin':
        return <AdminScreen navigation={mockNavigation} />;
      case 'OrderForm':
        return <OrderFormScreen navigation={mockNavigation} route={mockRoute} />;
      case 'Statistics':
        return <StatisticsScreen navigation={mockNavigation} />;
      case 'Stock':
        return <StockScreen navigation={mockNavigation} />;
      case 'Consommation':
        return <ConsommationScreen navigation={mockNavigation} />;
      case 'DataReset':
        return <DataResetScreen navigation={mockNavigation} />;
      case 'Promotion':
        return <PromotionScreen navigation={mockNavigation} />;
      case 'Offers':
        return <OffersScreen navigation={mockNavigation} />;
      case 'ClientSpace':
        return <ClientSpaceScreen navigation={mockNavigation} />;
      case 'Products':
        return <ProductsScreen navigation={mockNavigation} />;
      case 'OrderDetail':
        return <OrderDetailScreen navigation={mockNavigation} route={mockRoute} />;
      default:
        return <AdminScreen navigation={mockNavigation} />;
    }
  };

  return (
    <View style={styles.container}>
      <StatusBar
        barStyle="light-content"
        backgroundColor={Colors.secondary}
        translucent={Platform.OS === 'android'}
      />
      <Animated.View style={[styles.screenContainer, { opacity: fadeAnim }]}>
        {renderScreen()}
      </Animated.View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.backgroundSolid,
  },
  screenContainer: {
    flex: 1,
  },
});

export default App;
