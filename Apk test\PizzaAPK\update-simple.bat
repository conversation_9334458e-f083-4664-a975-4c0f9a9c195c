@echo off
echo ========================================
echo    MISE A JOUR EXPO - ADMIN PIZZA
echo ========================================
echo.
echo 🚀 Demarrage de la mise a jour...
echo 📧 Utilisateur: <EMAIL>
echo 📱 Version: 1.3.0 - Interface Admin
echo.

cd /d "C:\Users\<USER>\Documents\AppCaisse\Apk test\PizzaAPK"

echo 📁 Repertoire de travail: %CD%
echo.

echo 🔓 Deconnexion des comptes existants...
call npx expo logout
echo.

echo 🔐 Connexion a Expo...
echo <EMAIL>| call npx expo login
echo.

echo 🔍 Verification de la connexion...
call npx expo whoami
echo.

echo 📤 Publication de la mise a jour EAS...
call npx eas update --branch main --message "Version 1.3.0 - Interface Admin avec boutons Stock et Promotion"
echo.

echo 🔨 Lancement du build APK...
call npx eas build --platform android --profile production --non-interactive --message "Version 1.3.0 - Interface Admin"
echo.

echo 🌐 Lancement du serveur de developpement...
start "Expo Server" cmd /k "npx expo start --tunnel"
echo.

echo ========================================
echo           SCRIPT TERMINE
echo ========================================
echo.
echo ✅ Mise a jour EAS: Publiee
echo ✅ Build APK: Lance (10-15 minutes)
echo ✅ Serveur: Demarre
echo.
echo 🔗 Verifiez sur: https://expo.dev/accounts/brona
echo.
echo 📱 Pour tester sur Expo Go:
echo    1. Ouvrez Expo Go
echo    2. Scannez le QR code
echo    3. Vous verrez l'interface Admin
echo.
pause
