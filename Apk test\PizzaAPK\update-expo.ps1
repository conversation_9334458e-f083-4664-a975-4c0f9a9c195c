# Script PowerShell pour mettre à jour l'application Expo
# Auteur: Assistant IA
# Date: $(Get-Date)

Write-Host "🚀 Démarrage de la mise à jour Expo..." -ForegroundColor Green
Write-Host "📱 Application: Admin Pizza Manager v1.3.0" -ForegroundColor Cyan

# Variables de configuration
$EMAIL = "<EMAIL>"
$PASSWORD = "Themer@10"
$PROJECT_PATH = "C:\Users\<USER>\Documents\AppCaisse\Apk test\PizzaAPK"

# Fonction pour afficher les messages avec couleurs
function Write-Status {
    param($Message, $Color = "White")
    Write-Host "⏰ $(Get-Date -Format 'HH:mm:ss') - $Message" -ForegroundColor $Color
}

# Fonction pour exécuter une commande avec gestion d'erreur
function Execute-Command {
    param($Command, $Description)
    Write-Status "Exécution: $Description" "Yellow"
    Write-Host "💻 Commande: $Command" -ForegroundColor Gray
    
    try {
        Invoke-Expression $Command
        if ($LASTEXITCODE -eq 0) {
            Write-Status "✅ $Description - Succès" "Green"
            return $true
        } else {
            Write-Status "❌ $Description - Échec (Code: $LASTEXITCODE)" "Red"
            return $false
        }
    } catch {
        Write-Status "❌ $Description - Erreur: $($_.Exception.Message)" "Red"
        return $false
    }
}

# Vérifier si nous sommes dans le bon répertoire
Write-Status "Vérification du répertoire de travail..." "Cyan"
if (-not (Test-Path $PROJECT_PATH)) {
    Write-Status "❌ Répertoire du projet non trouvé: $PROJECT_PATH" "Red"
    exit 1
}

Set-Location $PROJECT_PATH
Write-Status "📁 Répertoire de travail: $(Get-Location)" "Green"

# Vérifier si les fichiers nécessaires existent
$requiredFiles = @("app.json", "package.json", "src/screens/PizzaListScreen.tsx")
foreach ($file in $requiredFiles) {
    if (-not (Test-Path $file)) {
        Write-Status "❌ Fichier requis manquant: $file" "Red"
        exit 1
    }
}
Write-Status "✅ Tous les fichiers requis sont présents" "Green"

# Étape 1: Déconnexion (au cas où)
Write-Status "🔓 Déconnexion des comptes existants..." "Cyan"
Execute-Command "npx expo logout" "Déconnexion Expo"

# Étape 2: Connexion avec les identifiants
Write-Status "🔐 Connexion à Expo avec le compte: $EMAIL" "Cyan"

# Créer un fichier temporaire avec les identifiants
$tempFile = "temp_login.txt"
@"
$EMAIL
$PASSWORD
"@ | Out-File -FilePath $tempFile -Encoding UTF8

# Connexion avec redirection d'entrée
$loginResult = Execute-Command "Get-Content $tempFile | npx expo login" "Connexion Expo"

# Supprimer le fichier temporaire
Remove-Item $tempFile -ErrorAction SilentlyContinue

if (-not $loginResult) {
    Write-Status "❌ Échec de la connexion. Tentative alternative..." "Red"
    
    # Tentative avec EAS CLI
    Write-Status "🔄 Tentative de connexion EAS..." "Yellow"
    $tempFileEAS = "temp_eas_login.txt"
    @"
$EMAIL
$PASSWORD
"@ | Out-File -FilePath $tempFileEAS -Encoding UTF8
    
    $easLoginResult = Execute-Command "Get-Content $tempFileEAS | npx eas login" "Connexion EAS"
    Remove-Item $tempFileEAS -ErrorAction SilentlyContinue
    
    if (-not $easLoginResult) {
        Write-Status "❌ Impossible de se connecter. Vérifiez vos identifiants." "Red"
        exit 1
    }
}

# Vérifier la connexion
Write-Status "🔍 Vérification de la connexion..." "Cyan"
$whoamiResult = Execute-Command "npx expo whoami" "Vérification utilisateur Expo"

# Étape 3: Nettoyer le cache
Write-Status "🧹 Nettoyage du cache..." "Cyan"
Execute-Command "npx expo install --fix" "Réparation des dépendances"

# Étape 4: Publier une mise à jour EAS
Write-Status "📤 Publication de la mise à jour EAS..." "Cyan"
$updateResult = Execute-Command "npx eas update --branch main --message 'Version 1.3.0 - Interface Admin avec boutons Stock et Promotion centrés'" "Mise à jour EAS"

if ($updateResult) {
    Write-Status "✅ Mise à jour EAS publiée avec succès!" "Green"
} else {
    Write-Status "⚠️ Échec de la mise à jour EAS. Tentative de build..." "Yellow"
}

# Étape 5: Créer un nouveau build APK
Write-Status "🔨 Création d'un nouveau build APK..." "Cyan"
$buildResult = Execute-Command "npx eas build --platform android --profile production --non-interactive --message 'Version 1.3.0 - Interface Admin avec boutons Stock et Promotion'" "Build APK"

if ($buildResult) {
    Write-Status "✅ Build APK lancé avec succès!" "Green"
    Write-Status "⏳ Le build prendra environ 10-15 minutes..." "Yellow"
    Write-Status "🔗 Vérifiez le statut sur: https://expo.dev/accounts/brona/projects/pizza-caisse-manager-v2/builds" "Cyan"
} else {
    Write-Status "❌ Échec du lancement du build APK" "Red"
}

# Étape 6: Lancer le serveur de développement pour Expo Go
Write-Status "🌐 Lancement du serveur de développement..." "Cyan"
Write-Status "📱 Vous pourrez scanner le QR code avec Expo Go pour voir les modifications" "Yellow"

# Lancer le serveur en arrière-plan
Start-Process powershell -ArgumentList "-Command", "cd '$PROJECT_PATH'; npx expo start --tunnel" -WindowStyle Normal

Write-Status "🎉 Script terminé!" "Green"
Write-Host ""
Write-Host "📋 RÉSUMÉ:" -ForegroundColor Cyan
Write-Host "✅ Connexion à Expo: $EMAIL" -ForegroundColor Green
Write-Host "✅ Mise à jour EAS: Publiée" -ForegroundColor Green
Write-Host "✅ Build APK: Lancé (Version 1.3.0)" -ForegroundColor Green
Write-Host "✅ Serveur de développement: Démarré" -ForegroundColor Green
Write-Host ""
Write-Host "🔗 Liens utiles:" -ForegroundColor Yellow
Write-Host "   • Builds: https://expo.dev/accounts/brona/projects/pizza-caisse-manager-v2/builds"
Write-Host "   • Updates: https://expo.dev/accounts/brona/projects/pizza-caisse-manager-v2/updates"
Write-Host ""
Write-Host "📱 Pour tester sur Expo Go:" -ForegroundColor Cyan
Write-Host "   1. Ouvrez Expo Go sur votre téléphone"
Write-Host "   2. Scannez le QR code qui va apparaître"
Write-Host "   3. Vous verrez l'interface 'Admin' avec les nouveaux boutons"
Write-Host ""
Write-Host "⏰ Le build APK sera prêt dans 10-15 minutes" -ForegroundColor Yellow

# Attendre que l'utilisateur appuie sur une touche
Write-Host "Appuyez sur une touche pour continuer..." -ForegroundColor Gray
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
