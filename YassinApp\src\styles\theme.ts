export const Colors = {
  primary: '#FF6B35', // Orange moderne et chaleureux pour pizza
  secondary: '#1A1A2E', // Bleu foncé élégant
  accent: '#16213E', // Bleu marine profond
  success: '#28A745', // Vert plus fort
  warning: '#FFC107', // Orange d'avertissement plus vif
  danger: '#DC3545', // Rouge plus fort
  light: '#F8F9FA', // Gris très clair
  dark: '#1A1A2E',
  white: '#FFFFFF',
  black: '#000000',
  gray: '#6C757D',
  lightGray: '#DEE2E6',
  background: '#E9ECEF', // Fond solide gris plus foncé
  backgroundSolid: '#E9ECEF', // Alternative solide
  surface: '#FFFFFF',
  surfaceElevated: '#FAFAFA', // Surface légèrement élevée
  border: '#E9ECEF',
  borderLight: '#F1F3F4',
  textPrimary: '#212529', // Texte principal plus foncé
  textSecondary: '#495057', // Texte secondaire plus foncé
  textOnPrimary: '#FFFFFF',
  textOnDark: '#FFFFFF',
  inputBackground: 'rgba(255, 255, 255, 0.95)',
  cardShadow: 'rgba(0, 0, 0, 0.1)',
  overlay: 'rgba(0, 0, 0, 0.5)',
};

export const FontSizes = {
  xs: 12,
  sm: 14,
  md: 16,
  lg: 18,
  xl: 20,
  xxl: 24,
  xxxl: 28,
};

export const Spacing = {
  xs: 4,
  sm: 8,
  md: 16,
  lg: 24,
  xl: 32,
  xxl: 48,
};

export const BorderRadius = {
  small: 4,
  medium: 8,
  large: 12,
  round: 50,
};

export const Shadows = {
  small: {
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  medium: {
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.15,
    shadowRadius: 4,
    elevation: 4,
  },
  large: {
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.2,
    shadowRadius: 8,
    elevation: 8,
  },
};
