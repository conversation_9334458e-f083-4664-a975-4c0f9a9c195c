import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TextInput,
  TouchableOpacity,
  FlatList,
  Alert,
  ScrollView,
  ActivityIndicator,
} from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { Colors, FontSizes, Spacing, BorderRadius, Shadows } from '../styles/theme';

interface Order {
  id: string;
  orderNumber: number;
  date: string;
  time: string;
  customerName: string;
  customerPhone: string;
  items: any[];
  subtotal: number;
  deliveryFee: number;
  total: number;
  deliveryAddress?: string;
  notes?: string;
}

interface ClientSpaceScreenProps {
  navigation: any;
}

export const ClientSpaceScreen: React.FC<ClientSpaceScreenProps> = ({ navigation }) => {
  const [phoneNumber, setPhoneNumber] = useState('');
  const [clientOrders, setClientOrders] = useState<Order[]>([]);
  const [loading, setLoading] = useState(false);
  const [searchPerformed, setSearchPerformed] = useState(false);

  const searchClientHistory = async () => {
    if (!phoneNumber.trim()) {
      Alert.alert('Erreur', 'Veuillez saisir un numéro de téléphone');
      return;
    }

    setLoading(true);
    try {
      // Charger toutes les commandes
      const ordersData = await AsyncStorage.getItem('orders');
      const allOrders: Order[] = ordersData ? JSON.parse(ordersData) : [];

      // Filtrer par numéro de téléphone
      const clientOrdersFiltered = allOrders.filter(order => 
        order.customerPhone && order.customerPhone.includes(phoneNumber.trim())
      );

      // Trier par date (plus récent en premier)
      clientOrdersFiltered.sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime());

      setClientOrders(clientOrdersFiltered);
      setSearchPerformed(true);

      if (clientOrdersFiltered.length === 0) {
        Alert.alert(
          'Aucun historique',
          `Aucune commande trouvée pour le numéro ${phoneNumber}`
        );
      }
    } catch (error) {
      console.error('Erreur lors de la recherche:', error);
      Alert.alert('Erreur', 'Impossible de charger l\'historique client');
    } finally {
      setLoading(false);
    }
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('fr-FR', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric'
    });
  };

  const renderOrderItem = ({ item }: { item: Order }) => (
    <View style={styles.orderCard}>
      <View style={styles.orderHeader}>
        <Text style={styles.orderNumber}>Commande #{item.orderNumber}</Text>
        <Text style={styles.orderDate}>{formatDate(item.date)}</Text>
      </View>
      
      <View style={styles.orderInfo}>
        <Text style={styles.customerName}>👤 {item.customerName}</Text>
        <Text style={styles.customerPhone}>📞 {item.customerPhone}</Text>
        {item.deliveryAddress && (
          <Text style={styles.deliveryAddress}>📍 {item.deliveryAddress}</Text>
        )}
      </View>

      <View style={styles.orderItems}>
        <Text style={styles.itemsTitle}>Articles commandés :</Text>
        {item.items.map((orderItem, index) => (
          <Text key={index} style={styles.itemText}>
            • {orderItem.name} x{orderItem.quantity} - {orderItem.totalPrice.toFixed(2)} DT
          </Text>
        ))}
      </View>

      <View style={styles.orderTotal}>
        <Text style={styles.subtotal}>Sous-total: {item.subtotal.toFixed(2)} DT</Text>
        {item.deliveryFee > 0 && (
          <Text style={styles.deliveryFee}>Livraison: {item.deliveryFee.toFixed(2)} DT</Text>
        )}
        <Text style={styles.total}>Total: {item.total.toFixed(2)} DT</Text>
      </View>

      {item.notes && (
        <View style={styles.notesSection}>
          <Text style={styles.notesTitle}>Notes :</Text>
          <Text style={styles.notesText}>{item.notes}</Text>
        </View>
      )}
    </View>
  );

  const clearSearch = () => {
    setPhoneNumber('');
    setClientOrders([]);
    setSearchPerformed(false);
  };

  return (
    <View style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity onPress={() => navigation.goBack()} style={styles.backButton} activeOpacity={0.7}>
          <Text style={styles.backButtonText}>← Retour</Text>
        </TouchableOpacity>
        <Text style={styles.title}>👥 Espace Client</Text>
      </View>

      {/* Search Section */}
      <View style={styles.searchSection}>
        <Text style={styles.searchTitle}>Rechercher l'historique d'un client</Text>
        
        <View style={styles.searchContainer}>
          <TextInput
            style={styles.phoneInput}
            placeholder="Numéro de téléphone (ex: 12345678)"
            value={phoneNumber}
            onChangeText={setPhoneNumber}
            keyboardType="phone-pad"
            maxLength={15}
          />
          
          <View style={styles.searchButtons}>
            <TouchableOpacity 
              style={styles.searchButton} 
              onPress={searchClientHistory}
              activeOpacity={0.8}
              disabled={loading}
            >
              {loading ? (
                <ActivityIndicator color={Colors.white} size="small" />
              ) : (
                <Text style={styles.searchButtonText}>🔍 Rechercher</Text>
              )}
            </TouchableOpacity>

            {searchPerformed && (
              <TouchableOpacity 
                style={styles.clearButton} 
                onPress={clearSearch}
                activeOpacity={0.8}
              >
                <Text style={styles.clearButtonText}>🗑️ Effacer</Text>
              </TouchableOpacity>
            )}
          </View>
        </View>
      </View>

      {/* Results Section */}
      {searchPerformed && (
        <View style={styles.resultsSection}>
          <Text style={styles.resultsTitle}>
            📋 Historique ({clientOrders.length} commande{clientOrders.length !== 1 ? 's' : ''})
          </Text>
          
          {clientOrders.length > 0 ? (
            <FlatList
              data={clientOrders}
              renderItem={renderOrderItem}
              keyExtractor={(item) => item.id}
              showsVerticalScrollIndicator={false}
              contentContainerStyle={styles.ordersList}
            />
          ) : (
            <View style={styles.noResultsContainer}>
              <Text style={styles.noResultsText}>
                Aucune commande trouvée pour ce numéro
              </Text>
              <Text style={styles.noResultsSubtext}>
                Vérifiez le numéro ou essayez une recherche partielle
              </Text>
            </View>
          )}
        </View>
      )}

      {/* Instructions */}
      {!searchPerformed && (
        <View style={styles.instructionsContainer}>
          <Text style={styles.instructionsTitle}>💡 Comment utiliser l'espace client</Text>
          <Text style={styles.instructionText}>• Saisissez le numéro de téléphone du client</Text>
          <Text style={styles.instructionText}>• Cliquez sur "Rechercher" pour voir l'historique</Text>
          <Text style={styles.instructionText}>• Vous pouvez faire une recherche partielle</Text>
          <Text style={styles.instructionText}>• L'historique est trié du plus récent au plus ancien</Text>
        </View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: Spacing.md,
    paddingVertical: Spacing.md,
    backgroundColor: Colors.primary,
    ...Shadows.small,
  },
  backButton: {
    marginRight: Spacing.md,
    padding: Spacing.sm,
  },
  backButtonText: {
    color: Colors.white,
    fontSize: FontSizes.md,
    fontWeight: 'bold',
  },
  title: {
    fontSize: FontSizes.xl,
    fontWeight: 'bold',
    color: Colors.white,
    flex: 1,
    textAlign: 'center',
    marginRight: Spacing.xl,
  },
  searchSection: {
    padding: Spacing.md,
    backgroundColor: Colors.white,
    margin: Spacing.md,
    borderRadius: BorderRadius.lg,
    ...Shadows.medium,
  },
  searchTitle: {
    fontSize: FontSizes.lg,
    fontWeight: 'bold',
    color: Colors.textPrimary,
    marginBottom: Spacing.md,
    textAlign: 'center',
  },
  searchContainer: {
    gap: Spacing.md,
  },
  phoneInput: {
    borderWidth: 1,
    borderColor: Colors.border,
    borderRadius: BorderRadius.md,
    padding: Spacing.md,
    fontSize: FontSizes.md,
    backgroundColor: Colors.light,
    minHeight: 48,
  },
  searchButtons: {
    flexDirection: 'row',
    gap: Spacing.sm,
  },
  searchButton: {
    backgroundColor: Colors.primary,
    borderRadius: BorderRadius.md,
    paddingVertical: Spacing.md,
    paddingHorizontal: Spacing.lg,
    alignItems: 'center',
    flex: 1,
    minHeight: 48,
    justifyContent: 'center',
  },
  searchButtonText: {
    color: Colors.white,
    fontSize: FontSizes.md,
    fontWeight: 'bold',
  },
  clearButton: {
    backgroundColor: Colors.error,
    borderRadius: BorderRadius.md,
    paddingVertical: Spacing.md,
    paddingHorizontal: Spacing.lg,
    alignItems: 'center',
    minHeight: 48,
    justifyContent: 'center',
  },
  clearButtonText: {
    color: Colors.white,
    fontSize: FontSizes.md,
    fontWeight: 'bold',
  },
  resultsSection: {
    flex: 1,
    margin: Spacing.md,
    marginTop: 0,
  },
  resultsTitle: {
    fontSize: FontSizes.lg,
    fontWeight: 'bold',
    color: Colors.textPrimary,
    marginBottom: Spacing.md,
    textAlign: 'center',
  },
  ordersList: {
    paddingBottom: Spacing.xl,
  },
  orderCard: {
    backgroundColor: Colors.white,
    borderRadius: BorderRadius.lg,
    padding: Spacing.md,
    marginBottom: Spacing.md,
    ...Shadows.medium,
  },
  orderHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: Spacing.sm,
    paddingBottom: Spacing.sm,
    borderBottomWidth: 1,
    borderBottomColor: Colors.border,
  },
  orderNumber: {
    fontSize: FontSizes.lg,
    fontWeight: 'bold',
    color: Colors.primary,
  },
  orderDate: {
    fontSize: FontSizes.md,
    color: Colors.textSecondary,
  },
  orderInfo: {
    marginBottom: Spacing.sm,
  },
  customerName: {
    fontSize: FontSizes.md,
    fontWeight: 'bold',
    color: Colors.textPrimary,
    marginBottom: Spacing.xs,
  },
  customerPhone: {
    fontSize: FontSizes.md,
    color: Colors.textSecondary,
    marginBottom: Spacing.xs,
  },
  deliveryAddress: {
    fontSize: FontSizes.sm,
    color: Colors.textSecondary,
    fontStyle: 'italic',
  },
  orderItems: {
    marginBottom: Spacing.sm,
    paddingTop: Spacing.sm,
    borderTopWidth: 1,
    borderTopColor: Colors.border,
  },
  itemsTitle: {
    fontSize: FontSizes.md,
    fontWeight: 'bold',
    color: Colors.textPrimary,
    marginBottom: Spacing.xs,
  },
  itemText: {
    fontSize: FontSizes.sm,
    color: Colors.textSecondary,
    marginBottom: Spacing.xs,
    paddingLeft: Spacing.sm,
  },
  orderTotal: {
    paddingTop: Spacing.sm,
    borderTopWidth: 1,
    borderTopColor: Colors.border,
  },
  subtotal: {
    fontSize: FontSizes.sm,
    color: Colors.textSecondary,
    marginBottom: Spacing.xs,
  },
  deliveryFee: {
    fontSize: FontSizes.sm,
    color: Colors.textSecondary,
    marginBottom: Spacing.xs,
  },
  total: {
    fontSize: FontSizes.md,
    fontWeight: 'bold',
    color: Colors.primary,
  },
  notesSection: {
    marginTop: Spacing.sm,
    paddingTop: Spacing.sm,
    borderTopWidth: 1,
    borderTopColor: Colors.border,
  },
  notesTitle: {
    fontSize: FontSizes.sm,
    fontWeight: 'bold',
    color: Colors.textPrimary,
    marginBottom: Spacing.xs,
  },
  notesText: {
    fontSize: FontSizes.sm,
    color: Colors.textSecondary,
    fontStyle: 'italic',
  },
  noResultsContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    padding: Spacing.xl,
  },
  noResultsText: {
    fontSize: FontSizes.lg,
    color: Colors.textSecondary,
    textAlign: 'center',
    marginBottom: Spacing.sm,
  },
  noResultsSubtext: {
    fontSize: FontSizes.md,
    color: Colors.textSecondary,
    textAlign: 'center',
  },
  instructionsContainer: {
    margin: Spacing.md,
    padding: Spacing.md,
    backgroundColor: Colors.light,
    borderRadius: BorderRadius.lg,
    borderLeftWidth: 4,
    borderLeftColor: Colors.primary,
  },
  instructionsTitle: {
    fontSize: FontSizes.lg,
    fontWeight: 'bold',
    color: Colors.textPrimary,
    marginBottom: Spacing.md,
  },
  instructionText: {
    fontSize: FontSizes.md,
    color: Colors.textSecondary,
    marginBottom: Spacing.sm,
    lineHeight: 20,
  },
});
