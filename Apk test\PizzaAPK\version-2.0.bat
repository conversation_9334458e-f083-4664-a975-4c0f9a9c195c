@echo off
chcp 65001 >nul
cls

echo ==========================================
echo    PIZZA CAISSE MANAGER - VERSION 2.0
echo ==========================================
echo.
echo 📅 Version du 6 juillet 2025 - 15h45 + Améliorations
echo 🍕 Application: Pizza Caisse Manager
echo 📱 Version: 2.0.0 (Build 37)
echo.
echo ✅ Fonctionnalités Version 2.0 + PDF LISTE COMPACTE:
echo    - Titre: "Caisse" (centré avec 3 boutons)
echo    - Boutons: Stats + Stock + Déconnexion
echo    - Gestion de stock TABLEAU SIMPLE
echo    - Filtrage par jour: J-1, Aujourd'hui, J+1
echo    - Filtrage par date: CALENDRIER (sélection visuelle)
echo    - Saisie stock: VIRGULE FONCTIONNE (tapez 12,5 et ça marche!)
echo    - Affichage: Garde votre saisie (virgule ou point)
echo    - Précision: 3 chiffres après la virgule
echo    - Stock Final calculé automatiquement
echo    - CONTINUITÉ CORRIGÉE: Stock final J-1 = Stock initial J (automatique)
echo    - Texte: "Consommation" au lieu de "Sorties"
echo    - BOUTON VIDER: Remet tout le stock à zéro pour recommencer
echo    - CALCULS CORRIGÉS: Logs de debug pour vérifier les calculs
echo    - Statistiques: Commandes d'aujourd'hui par défaut
echo    - EXPORT PDF: EN HAUT stats GRANDES + Liste PETITE + Récap en bas
echo    - PDF: Nombres ROUGES en GRAS + "DT" + Liste compacte pour voir tout
echo    - Synchronisation: Données en temps réel
echo.

cd /d "C:\Users\<USER>\Documents\AppCaisse\Apk test\PizzaAPK"

if not exist "package.json" (
    echo ❌ ERREUR: Fichier package.json non trouve
    echo Verifiez que vous etes dans le bon repertoire
    pause
    exit /b 1
)

echo 📁 Repertoire: %CD%
echo.

echo 🧹 Nettoyage du cache...
if exist ".expo" rmdir /s /q ".expo" >nul 2>&1
if exist "node_modules\.cache" rmdir /s /q "node_modules\.cache" >nul 2>&1
echo ✅ Cache nettoye
echo.

echo 📦 Verification des dependances...
if not exist "node_modules" (
    echo 📥 Installation des dependances...
    npm install
)
echo ✅ Dependances OK
echo.

echo 🚀 Demarrage d'Expo - Version 2.0...
echo 📱 Le QR code va apparaitre ci-dessous:
echo ==========================================
echo.

npx expo start --clear

echo.
echo ==========================================
echo Si le QR code n'apparait pas:
echo 1. Fermez cette fenetre
echo 2. Ouvrez cmd en tant qu'administrateur  
echo 3. Tapez: cd "C:\Users\<USER>\Documents\AppCaisse\Apk test\PizzaAPK"
echo 4. Tapez: npx expo start
echo ==========================================
echo.
echo 📱 Une fois sur Expo Go, vous verrez:
echo    ✅ Titre "Pizza Caisse Manager"
echo    ✅ 3 boutons: Stats + Stock + Déconnexion
echo    ✅ Gestion de stock complète
echo    ✅ Stock initial/entrées/sorties/stock final
echo    ✅ Manipulation tactile amelioree
echo.
pause
