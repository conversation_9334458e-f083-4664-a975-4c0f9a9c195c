@echo off
chcp 65001 >nul
cls

echo ==========================================
echo    🚀 BUILD APK FINAL - VERSION 2.0
echo ==========================================
echo.
echo 📱 Version: 2.0.0 (Build 37)
echo.
echo ✅ Fonctionnalités Version 2.0 FINALE:
echo    - Titre: "Caisse" (centré avec 3 boutons)
echo    - Boutons: Stats + Stock + Déconnexion
echo    - Gestion de stock TABLEAU SIMPLE
echo    - Filtrage par jour: J-1, Aujourd'hui, J+1
echo    - Filtrage par date: CALENDRIER (sélection visuelle)
echo    - Saisie stock: VIRGULE FONCTIONNE (tapez 12,5 et ça marche!)
echo    - Affichage: Garde votre saisie (virgule ou point)
echo    - Précision: 3 chiffres après la virgule
echo    - Stock Final calculé automatiquement
echo    - CONTINUITÉ CORRIGÉE: Stock final J-1 = Stock initial J (automatique)
echo    - Texte: "Consommation" au lieu de "Sorties"
echo    - BOUTON VIDER: Remet tout le stock à zéro pour recommencer
echo    - CALCULS CORRIGÉS: Logs de debug pour vérifier les calculs
echo    - Statistiques: Commandes d'aujourd'hui par défaut
echo    - EXPORT PDF: EN HAUT stats GRANDES + Liste PETITE + Récap en bas
echo    - PDF: Nombres ROUGES en GRAS + "DT" + Liste compacte pour voir tout
echo    - Synchronisation: Données en temps réel
echo.
echo ==========================================

cd /d "C:\Users\<USER>\Documents\AppCaisse\Apk test\PizzaAPK"

if not exist "package.json" (
    echo ❌ ERREUR: Fichier package.json non trouvé
    echo Vérifiez que vous êtes dans le bon répertoire
    pause
    exit /b 1
)

echo 📂 Répertoire: %CD%
echo.

echo 🧹 Nettoyage du cache...
if exist ".expo" rmdir /s /q ".expo" >nul 2>&1
if exist "node_modules\.cache" rmdir /s /q "node_modules\.cache" >nul 2>&1
echo ✅ Cache nettoyé
echo.

echo 📦 Vérification des dépendances...
if not exist "node_modules" (
    echo 📥 Installation des dépendances...
    npm install
)
echo.

echo 🔧 Configuration EAS Build...
echo.

echo 🏗️ Démarrage de la build APK...
echo ⏳ Cela peut prendre 10-15 minutes...
echo.

echo 📱 Build de l'APK Android...
npx eas build --platform android --profile preview

echo.
echo ==========================================
echo 🎉 BUILD TERMINÉE !
echo.
echo 📱 Votre APK est prêt !
echo 🔗 Le lien de téléchargement sera affiché ci-dessus
echo.
echo 📋 Instructions:
echo 1. Copiez le lien de téléchargement
echo 2. Téléchargez l'APK sur votre téléphone
echo 3. Installez l'APK (autorisez les sources inconnues)
echo 4. Profitez de votre application !
echo.
echo ==========================================
pause
