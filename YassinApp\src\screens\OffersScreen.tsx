import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  Alert,
  ScrollView,
  Dimensions,
} from 'react-native';
import { OFFERS } from '../data/offers';
import { Offer, OfferItem, OrderItem } from '../types';
import { Colors, FontSizes, Spacing, BorderRadius, Shadows } from '../styles/theme';

interface OffersScreenProps {
  navigation: any;
}

const { width } = Dimensions.get('window');
const cardWidth = width / 2 - 24; // 2 cards per row with spacing

export const OffersScreen: React.FC<OffersScreenProps> = ({ navigation }) => {
  const [cart, setCart] = useState<OrderItem[]>([]);
  const [cartTotal, setCartTotal] = useState(0);
  const [selectedOffer, setSelectedOffer] = useState<Offer | null>(null);
  const [currentItemIndex, setCurrentItemIndex] = useState(0);
  const [selectedChoices, setSelectedChoices] = useState<string[]>([]);
  const [currentSubChoice, setCurrentSubChoice] = useState(0); // Pour gérer les choix multiples (pizza 1, pizza 2, etc.)

  useEffect(() => {
    const total = cart.reduce((sum, item) => sum + item.totalPrice, 0);
    setCartTotal(total);
  }, [cart]);

  const handleSelectOffer = (offer: Offer) => {
    console.log('🍕 Sélection de l\'offre:', offer.name);
    setSelectedOffer(offer);

    // Pré-sélectionner automatiquement TOUS les choix uniques (nuggets, frites, etc.)
    const preSelectedChoices: string[] = [];
    let choiceIndex = 0;

    offer.items.forEach((item) => {
      if (item.quantity > 1) {
        // Pour les items avec plusieurs choix (comme 2 pizzas)
        for (let i = 0; i < item.quantity; i++) {
          if (item.choices.length === 1) {
            // Sélectionner automatiquement les éléments uniques
            preSelectedChoices[choiceIndex] = item.choices[0];
            console.log(`✅ Auto-sélectionné: ${item.name} ${i + 1} = ${item.choices[0]}`);
          }
          // Laisser vide pour les choix multiples (pizzas)
          choiceIndex++;
        }
      } else {
        // Pour les items avec un seul choix (nuggets, frites)
        if (item.choices.length === 1) {
          // Sélectionner automatiquement
          preSelectedChoices[choiceIndex] = item.choices[0];
          console.log(`✅ Auto-sélectionné: ${item.name} = ${item.choices[0]}`);
        }
        // Laisser vide pour les choix multiples (boissons avec options)
        choiceIndex++;
      }
    });

    setSelectedChoices(preSelectedChoices);

    // Trouver le premier item qui nécessite un choix de l'utilisateur
    findFirstUserChoiceItem(offer);
  };

  const findFirstUserChoiceItem = (offer: Offer) => {
    let itemIndex = 0;
    let subChoice = 0;

    // Parcourir tous les items pour trouver le premier qui nécessite un choix
    for (let i = 0; i < offer.items.length; i++) {
      const item = offer.items[i];

      if (item.quantity > 1) {
        // Pour les items avec plusieurs choix (comme 2 pizzas)
        for (let j = 0; j < item.quantity; j++) {
          if (item.choices.length > 1) {
            // Premier item nécessitant un choix trouvé
            setCurrentItemIndex(i);
            setCurrentSubChoice(j);
            return;
          }
        }
      } else {
        // Pour les items avec un seul choix
        if (item.choices.length > 1) {
          // Premier item nécessitant un choix trouvé
          setCurrentItemIndex(i);
          setCurrentSubChoice(0);
          return;
        }
      }
    }

    // Si aucun item ne nécessite de choix, commencer par le premier
    setCurrentItemIndex(0);
    setCurrentSubChoice(0);
  };

  const handleSelectChoice = (choice: string) => {
    const currentItem = selectedOffer?.items[currentItemIndex];
    if (!currentItem) return;

    const newSelectedChoices = [...selectedChoices];

    // Si c'est un item avec plusieurs choix (comme 2 pizzas)
    if (currentItem.quantity > 1) {
      const baseIndex = currentItemIndex * currentItem.quantity + currentSubChoice;
      newSelectedChoices[baseIndex] = choice;
    } else {
      newSelectedChoices[currentItemIndex] = choice;
    }

    setSelectedChoices(newSelectedChoices);
  };

  const handleNextItem = () => {
    if (!selectedOffer) return;

    const currentItem = selectedOffer.items[currentItemIndex];

    // Calculer l'index du choix actuel
    let choiceIndex = 0;
    for (let i = 0; i < currentItemIndex; i++) {
      choiceIndex += selectedOffer.items[i].quantity;
    }
    choiceIndex += currentSubChoice;

    // Vérifier si l'utilisateur a fait un choix (seulement pour les items avec plusieurs choix)
    if (currentItem.choices.length > 1 && !selectedChoices[choiceIndex]) {
      Alert.alert('Sélection requise', `Veuillez sélectionner ${currentItem.name}`);
      return;
    }

    // Trouver le prochain item qui nécessite un choix de l'utilisateur
    findNextUserChoiceItem();
  };

  const findNextUserChoiceItem = () => {
    if (!selectedOffer) return;

    const currentItem = selectedOffer.items[currentItemIndex];
    let nextItemIndex = currentItemIndex;
    let nextSubChoice = currentSubChoice;

    // Si c'est un item avec plusieurs sous-choix (comme 2 pizzas)
    if (currentItem.quantity > 1 && nextSubChoice < currentItem.quantity - 1) {
      nextSubChoice++;

      // Vérifier si le prochain sous-choix nécessite une sélection
      if (currentItem.choices.length > 1) {
        setCurrentSubChoice(nextSubChoice);
        return;
      }
    }

    // Passer à l'item suivant
    nextItemIndex++;
    nextSubChoice = 0;

    // Chercher le prochain item qui nécessite un choix
    while (nextItemIndex < selectedOffer.items.length) {
      const nextItem = selectedOffer.items[nextItemIndex];

      if (nextItem.quantity > 1) {
        // Item avec plusieurs sous-choix
        for (let j = 0; j < nextItem.quantity; j++) {
          if (nextItem.choices.length > 1) {
            // Trouvé un item nécessitant un choix
            setCurrentItemIndex(nextItemIndex);
            setCurrentSubChoice(j);
            return;
          }
        }
      } else {
        // Item simple
        if (nextItem.choices.length > 1) {
          // Trouvé un item nécessitant un choix
          setCurrentItemIndex(nextItemIndex);
          setCurrentSubChoice(0);
          return;
        }
      }

      nextItemIndex++;
    }

    // Tous les choix nécessaires ont été faits, ajouter au panier
    addOfferToCart();
  };

  const addOfferToCart = () => {
    if (!selectedOffer) {
      console.log('❌ Aucune offre sélectionnée');
      return;
    }

    console.log('🛒 Ajout de l\'offre au panier:', selectedOffer.name);
    console.log('📋 Choix sélectionnés:', selectedChoices);

    const offerItems: { name: string; choice: string }[] = [];
    let choiceIndex = 0;

    selectedOffer.items.forEach((item, itemIndex) => {
      if (item.quantity > 1) {
        // Pour les items avec plusieurs choix (comme 2 pizzas)
        for (let i = 0; i < item.quantity; i++) {
          const choice = selectedChoices[choiceIndex] || item.choices[0] || 'Non spécifié';
          offerItems.push({
            name: `${item.name} ${i + 1}`,
            choice: choice
          });
          console.log(`📦 Ajouté: ${item.name} ${i + 1} = ${choice}`);
          choiceIndex++;
        }
      } else {
        // Pour les items avec un seul choix
        const choice = selectedChoices[choiceIndex] || item.choices[0] || 'Non spécifié';
        offerItems.push({
          name: item.name,
          choice: choice
        });
        console.log(`📦 Ajouté: ${item.name} = ${choice}`);
        choiceIndex++;
      }
    });

    const newItem: OrderItem = {
      id: `offer-${selectedOffer.id}-${Date.now()}`,
      name: selectedOffer.name,
      quantity: 1,
      unitPrice: selectedOffer.price,
      totalPrice: selectedOffer.price,
      isOffer: true,
      offerItems
    };

    console.log('✅ Item créé pour le panier:', newItem);

    try {
      setCart(prevCart => {
        const newCart = [...prevCart, newItem];
        console.log('🛒 Panier mis à jour, nombre d\'items:', newCart.length);
        return newCart;
      });

      Alert.alert(
        '✅ Offre ajoutée',
        `${selectedOffer.name} a été ajoutée au panier avec tous les éléments inclus !`,
        [
          {
            text: 'Continuer les achats',
            onPress: () => {
              setSelectedOffer(null);
              setCurrentItemIndex(0);
              setCurrentSubChoice(0);
              setSelectedChoices([]);
            }
          },
          {
            text: 'Voir le panier',
            onPress: () => navigation.navigate('OrderForm')
          }
        ]
      );
    } catch (error) {
      console.error('❌ Erreur lors de l\'ajout au panier:', error);
      Alert.alert('Erreur', 'Impossible d\'ajouter l\'offre au panier');
    }
  };

  const handleViewCart = () => {
    if (cart.length === 0) {
      Alert.alert('Panier vide', 'Ajoutez des offres à votre panier avant de continuer.');
      return;
    }
    
    navigation.navigate('OrderForm', { cartItems: cart });
  };

  const handleBack = () => {
    if (selectedOffer) {
      const currentItem = selectedOffer.items[currentItemIndex];

      // Si on est dans un choix multiple et pas au premier choix
      if (currentItem.quantity > 1 && currentSubChoice > 0) {
        setCurrentSubChoice(currentSubChoice - 1);
      } else if (currentItemIndex > 0) {
        // Revenir à l'item précédent
        const previousItem = selectedOffer.items[currentItemIndex - 1];
        setCurrentItemIndex(currentItemIndex - 1);
        setCurrentSubChoice(previousItem.quantity > 1 ? previousItem.quantity - 1 : 0);
      } else {
        // Revenir à la liste des offres
        setSelectedOffer(null);
        setCurrentItemIndex(0);
        setCurrentSubChoice(0);
      }
    } else {
      navigation.goBack();
    }
  };

  const clearCart = () => {
    Alert.alert(
      'Vider le panier',
      'Êtes-vous sûr de vouloir vider le panier ?',
      [
        { text: 'Annuler', style: 'cancel' },
        { 
          text: 'Vider', 
          style: 'destructive',
          onPress: () => setCart([])
        }
      ]
    );
  };

  const renderOfferCard = ({ item }: { item: Offer }) => (
    <TouchableOpacity 
      style={styles.offerCard}
      onPress={() => handleSelectOffer(item)}
    >
      <Text style={styles.offerEmoji}>{item.image}</Text>
      <Text style={styles.offerName}>{item.name}</Text>
      <Text style={styles.offerDescription}>{item.description}</Text>
      <Text style={styles.offerPrice}>{item.price.toFixed(2)} DT</Text>
    </TouchableOpacity>
  );

  const renderChoiceSelection = () => {
    if (!selectedOffer) return null;

    const currentItem = selectedOffer.items[currentItemIndex];

    // Calculer l'index correct pour les choix
    let choiceIndex = 0;
    for (let i = 0; i < currentItemIndex; i++) {
      choiceIndex += selectedOffer.items[i].quantity;
    }
    choiceIndex += currentSubChoice;

    // Vérifier si l'item actuel nécessite un choix
    const needsUserChoice = currentItem.choices.length > 1;

    // Déterminer le titre en fonction du nombre de choix
    let choiceTitle = '';
    if (currentItem.quantity > 1) {
      choiceTitle = `Choisissez ${currentItem.name} ${currentSubChoice + 1}/${currentItem.quantity}`;
    } else {
      choiceTitle = `Choisissez ${currentItem.name}`;
    }

    // Calculer le nombre de sélections restantes
    const itemsLeft = selectedOffer.items.length - currentItemIndex - 1;
    let remainingSelections = itemsLeft;
    if (currentItem.quantity > 1) {
      remainingSelections += (currentItem.quantity - currentSubChoice - 1);
    }

    return (
      <View style={styles.choiceContainer}>
        {/* Résumé de l'offre */}
        <View style={styles.offerSummaryContainer}>
          <Text style={styles.offerSummaryTitle}>📋 {selectedOffer.name} - {selectedOffer.price.toFixed(2)} DT</Text>
          <Text style={styles.offerSummarySubtitle}>Inclus automatiquement :</Text>
          <View style={styles.includedItemsList}>
            {selectedOffer.items.map((item, index) => {
              if (item.choices.length === 1) {
                // Éléments automatiques
                if (item.quantity > 1) {
                  return (
                    <Text key={index} style={styles.includedItem}>
                      ✅ {item.quantity}x {item.name}: {item.choices[0]}
                    </Text>
                  );
                } else {
                  return (
                    <Text key={index} style={styles.includedItem}>
                      ✅ {item.name}: {item.choices[0]}
                    </Text>
                  );
                }
              }
              return null;
            })}
          </View>
        </View>

        <View style={styles.choiceHeader}>
          <Text style={styles.choiceTitle}>{choiceTitle}</Text>
          <Text style={styles.choiceSubtitle}>
            Choisissez votre option
          </Text>
        </View>

        <ScrollView style={styles.choiceList}>
          {currentItem.choices.map((choice, index) => (
            <TouchableOpacity
              key={index}
              style={[
                styles.choiceItem,
                selectedChoices[choiceIndex] === choice && styles.choiceItemSelected
              ]}
              onPress={() => handleSelectChoice(choice)}
            >
              <Text
                style={[
                  styles.choiceItemText,
                  selectedChoices[choiceIndex] === choice && styles.choiceItemTextSelected
                ]}
              >
                {choice}
              </Text>
            </TouchableOpacity>
          ))}
        </ScrollView>

        {/* Afficher les éléments automatiquement sélectionnés */}
        {renderAutoSelectedItems()}

        <TouchableOpacity
          style={[
            styles.nextButton,
            (currentItem.choices.length > 1 && !selectedChoices[choiceIndex]) && styles.nextButtonDisabled
          ]}
          onPress={handleNextItem}
          disabled={currentItem.choices.length > 1 && !selectedChoices[choiceIndex]}
        >
          <Text style={styles.nextButtonText}>
            {currentItemIndex < selectedOffer.items.length - 1 ? 'Suivant' : 'Ajouter au panier'}
          </Text>
        </TouchableOpacity>
      </View>
    );
  };

  const renderAutoSelectedItems = () => {
    if (!selectedOffer) return null;

    const autoSelectedItems: { name: string; choice: string }[] = [];
    let choiceIndex = 0;

    selectedOffer.items.forEach((item, itemIndex) => {
      if (item.quantity > 1) {
        // Pour les items avec plusieurs choix (comme 2 pizzas)
        for (let i = 0; i < item.quantity; i++) {
          if (item.choices.length === 1 && selectedChoices[choiceIndex]) {
            autoSelectedItems.push({
              name: `${item.name} ${i + 1}`,
              choice: selectedChoices[choiceIndex]
            });
          }
          choiceIndex++;
        }
      } else {
        // Pour les items avec un seul choix
        if (item.choices.length === 1 && selectedChoices[choiceIndex]) {
          autoSelectedItems.push({
            name: item.name,
            choice: selectedChoices[choiceIndex]
          });
        }
        choiceIndex++;
      }
    });

    if (autoSelectedItems.length === 0) return null;

    return (
      <View style={styles.autoSelectedContainer}>
        <Text style={styles.autoSelectedTitle}>✅ Éléments inclus :</Text>
        {autoSelectedItems.map((item, index) => (
          <View key={index} style={styles.autoSelectedItem}>
            <Text style={styles.autoSelectedText}>
              • {item.name}: {item.choice}
            </Text>
          </View>
        ))}
      </View>
    );
  };

  return (
    <View style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity onPress={handleBack} style={styles.backButton}>
          <Text style={styles.backButtonText}>← Retour</Text>
        </TouchableOpacity>
        <Text style={styles.title}>
          {selectedOffer ? selectedOffer.name : 'Nos Offres'}
        </Text>
      </View>

      {selectedOffer ? (
        renderChoiceSelection()
      ) : (
        <FlatList
          data={OFFERS}
          renderItem={renderOfferCard}
          keyExtractor={(item) => item.id}
          numColumns={2}
          showsVerticalScrollIndicator={false}
          contentContainerStyle={styles.listContainer}
        />
      )}

      {/* Panier flottant */}
      {cart.length > 0 && !selectedOffer && (
        <View style={styles.cartContainer}>
          <View style={styles.cartInfo}>
            <Text style={styles.cartText}>
              {cart.length} article{cart.length > 1 ? 's' : ''} - {cartTotal.toFixed(2)} DT
            </Text>
            <TouchableOpacity onPress={clearCart}>
              <Text style={styles.clearCartText}>🗑️</Text>
            </TouchableOpacity>
          </View>
          <TouchableOpacity style={styles.cartButton} onPress={handleViewCart}>
            <Text style={styles.cartButtonText}>Voir le panier</Text>
          </TouchableOpacity>
        </View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
  },
  header: {
    backgroundColor: Colors.secondary,
    paddingTop: 50,
    paddingBottom: 20,
    paddingHorizontal: 16,
    flexDirection: 'row',
    alignItems: 'center',
  },
  backButton: {
    marginRight: 10,
  },
  backButtonText: {
    color: Colors.textOnDark,
    fontSize: FontSizes.md,
    fontWeight: '600',
  },
  title: {
    fontSize: FontSizes.xl,
    fontWeight: 'bold',
    color: Colors.textOnDark,
    flex: 1,
  },
  listContainer: {
    paddingBottom: 100,
    paddingHorizontal: Spacing.md,
    paddingTop: Spacing.md,
  },
  offerCard: {
    width: cardWidth,
    backgroundColor: Colors.surface,
    margin: Spacing.sm,
    padding: Spacing.md,
    borderRadius: BorderRadius.large,
    alignItems: 'center',
    ...Shadows.medium,
  },
  offerEmoji: {
    fontSize: 40,
    marginBottom: Spacing.sm,
  },
  offerName: {
    fontSize: FontSizes.lg,
    fontWeight: 'bold',
    color: Colors.textPrimary,
    marginBottom: Spacing.xs,
    textAlign: 'center',
  },
  offerDescription: {
    fontSize: FontSizes.sm,
    color: Colors.textSecondary,
    textAlign: 'center',
    marginBottom: Spacing.sm,
  },
  offerPrice: {
    fontSize: FontSizes.lg,
    fontWeight: 'bold',
    color: Colors.primary,
    marginBottom: Spacing.md,
  },
  choiceContainer: {
    flex: 1,
    padding: Spacing.md,
  },
  choiceHeader: {
    marginBottom: Spacing.md,
  },
  choiceTitle: {
    fontSize: FontSizes.lg,
    fontWeight: 'bold',
    color: Colors.textPrimary,
    marginBottom: Spacing.xs,
  },
  choiceSubtitle: {
    fontSize: FontSizes.sm,
    color: Colors.textSecondary,
  },
  choiceList: {
    flex: 1,
  },
  choiceItem: {
    backgroundColor: Colors.surface,
    padding: Spacing.md,
    borderRadius: BorderRadius.medium,
    marginBottom: Spacing.sm,
    borderWidth: 1,
    borderColor: Colors.border,
    ...Shadows.small,
  },
  choiceItemSelected: {
    backgroundColor: Colors.primary,
    borderColor: Colors.primary,
  },
  choiceItemText: {
    fontSize: FontSizes.md,
    color: Colors.textPrimary,
  },
  choiceItemTextSelected: {
    color: Colors.textOnPrimary,
    fontWeight: 'bold',
  },
  nextButton: {
    backgroundColor: Colors.primary,
    padding: Spacing.md,
    borderRadius: BorderRadius.medium,
    alignItems: 'center',
    marginTop: Spacing.md,
    ...Shadows.medium,
  },
  nextButtonDisabled: {
    backgroundColor: Colors.gray,
  },
  nextButtonText: {
    fontSize: FontSizes.lg,
    fontWeight: 'bold',
    color: Colors.textOnPrimary,
  },
  cartContainer: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    backgroundColor: Colors.surface,
    padding: Spacing.md,
    borderTopWidth: 1,
    borderTopColor: Colors.border,
    ...Shadows.large,
  },
  cartInfo: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: Spacing.sm,
  },
  cartText: {
    fontSize: FontSizes.md,
    fontWeight: '600',
    color: Colors.textPrimary,
  },
  clearCartText: {
    fontSize: FontSizes.lg,
    padding: Spacing.xs,
  },
  cartButton: {
    backgroundColor: Colors.primary,
    borderRadius: BorderRadius.medium,
    paddingVertical: Spacing.md,
    alignItems: 'center',
  },
  cartButtonText: {
    fontSize: FontSizes.md,
    fontWeight: 'bold',
    color: Colors.textOnPrimary,
  },

  // Styles pour les éléments automatiquement sélectionnés
  autoSelectedContainer: {
    backgroundColor: Colors.light,
    padding: Spacing.md,
    borderRadius: BorderRadius.medium,
    marginVertical: Spacing.md,
    borderWidth: 1,
    borderColor: Colors.success,
  },
  autoSelectedTitle: {
    fontSize: FontSizes.md,
    fontWeight: 'bold',
    color: Colors.success,
    marginBottom: Spacing.sm,
  },
  autoSelectedItem: {
    marginBottom: Spacing.xs,
  },
  autoSelectedText: {
    fontSize: FontSizes.sm,
    color: Colors.textSecondary,
    fontStyle: 'italic',
  },

  // Styles pour le résumé de l'offre
  offerSummaryContainer: {
    backgroundColor: Colors.light,
    padding: Spacing.md,
    borderRadius: BorderRadius.medium,
    marginBottom: Spacing.lg,
    borderWidth: 1,
    borderColor: Colors.success,
  },
  offerSummaryTitle: {
    fontSize: FontSizes.lg,
    fontWeight: 'bold',
    color: Colors.primary,
    marginBottom: Spacing.sm,
    textAlign: 'center',
  },
  offerSummarySubtitle: {
    fontSize: FontSizes.md,
    fontWeight: '600',
    color: Colors.success,
    marginBottom: Spacing.sm,
  },
  includedItemsList: {
    marginLeft: Spacing.sm,
  },
  includedItem: {
    fontSize: FontSizes.sm,
    color: Colors.textSecondary,
    marginBottom: Spacing.xs,
    lineHeight: 20,
  },
});
