# 📦 EXEMPLE D'UTILISATION DU STOCK - YASSINAPP

## 🎯 Tableau Simple avec Synchronisation Automatique

Voici comment utiliser le nouveau tableau de stock avec synchronisation automatique.

## 📱 Interface du Tableau

```
┌─────────────────────────────────────┐
│ [← Retour]  Stock du Jour  [💾 Sauver] │
├─────────────────────────────────────┤
│ [← Hier]    Lundi 15 juillet   [<PERSON><PERSON><PERSON> →] │
├─────────────────────────────────────┤
│   Produit   │Initial│Entrées│ Final │
├─────────────┼───────┼───────┼───────┤
│ Pâte à pizza│ [___] │ [___] │  0,0  │
│    (kg)     │       │       │       │
├─────────────┼───────┼───────┼───────┤
│ Mozzarella  │ [___] │ [___] │  0,0  │
│    (kg)     │       │       │       │
├─────────────┼───────┼───────┼───────┤
│ Sauce tomate│ [___] │ [___] │  0,0  │
│    (L)      │       │       │       │
├─────────────┼───────┼───────┼───────┤
│ Pepperoni   │ [___] │ [___] │  0,0  │
│    (kg)     │       │       │       │
├─────────────┼───────┼───────┼───────┤
│ Olives      │ [___] │ [___] │  0,0  │
│    (kg)     │       │       │       │
└─────────────┴───────┴───────┴───────┘
```

## 📝 JOUR 1 - <PERSON> Jour (Lundi)

### 🔢 Saisie Initiale

**Vous remplissez :**
```
┌─────────────┬───────┬───────┬───────┐
│   Produit   │Initial│Entrées│ Final │
├─────────────┼───────┼───────┼───────┤
│ Pâte à pizza│ [25,0]│ [10,0]│ 35,0  │ ← Calculé automatiquement
│ Mozzarella  │ [15,0]│ [ 5,0]│ 20,0  │ ← Calculé automatiquement
│ Sauce tomate│ [10,0]│ [ 3,0]│ 13,0  │ ← Calculé automatiquement
│ Pepperoni   │ [ 8,0]│ [ 2,0]│ 10,0  │ ← Calculé automatiquement
│ Olives      │ [ 5,0]│ [ 1,0]│  6,0  │ ← Calculé automatiquement
└─────────────┴───────┴───────┴───────┘
```

**Actions :**
1. Tapez Stock Initial : `25,0` pour Pâte
2. Tapez Entrées : `10,0` pour Pâte
3. Stock Final se calcule : `35,0` ✅
4. Répétez pour tous les produits
5. Cliquez **"💾 Sauver"**

## 📝 JOUR 2 - Synchronisation Automatique (Mardi)

### 🔄 Stock Initial Automatique

**Quand vous ouvrez le stock du mardi :**
```
┌─────────────┬───────┬───────┬───────┐
│   Produit   │Initial│Entrées│ Final │
├─────────────┼───────┼───────┼───────┤
│ Pâte à pizza│ 35,0  │ [___] │ 35,0  │ ← Stock Initial = Stock Final Lundi
│ Mozzarella  │ 20,0  │ [___] │ 20,0  │ ← Stock Initial = Stock Final Lundi
│ Sauce tomate│ 13,0  │ [___] │ 13,0  │ ← Stock Initial = Stock Final Lundi
│ Pepperoni   │ 10,0  │ [___] │ 10,0  │ ← Stock Initial = Stock Final Lundi
│ Olives      │  6,0  │ [___] │  6,0  │ ← Stock Initial = Stock Final Lundi
└─────────────┴───────┴───────┴───────┘
```

**Vous ajoutez seulement les Entrées :**
```
┌─────────────┬───────┬───────┬───────┐
│   Produit   │Initial│Entrées│ Final │
├─────────────┼───────┼───────┼───────┤
│ Pâte à pizza│ 35,0  │ [ 5,0]│ 40,0  │ ← Nouveau calcul automatique
│ Mozzarella  │ 20,0  │ [ 0,0]│ 20,0  │ ← Pas d'entrée aujourd'hui
│ Sauce tomate│ 13,0  │ [ 2,0]│ 15,0  │ ← Nouveau calcul automatique
│ Pepperoni   │ 10,0  │ [ 1,0]│ 11,0  │ ← Nouveau calcul automatique
│ Olives      │  6,0  │ [ 0,0]│  6,0  │ ← Pas d'entrée aujourd'hui
└─────────────┴───────┴───────┴───────┘
```

## 📝 JOUR 3 - Continuité (Mercredi)

### 🔄 Synchronisation Continue

**Stock automatique du mercredi :**
```
┌─────────────┬───────┬───────┬───────┐
│   Produit   │Initial│Entrées│ Final │
├─────────────┼───────┼───────┼───────┤
│ Pâte à pizza│ 40,0  │ [___] │ 40,0  │ ← Stock Final Mardi
│ Mozzarella  │ 20,0  │ [___] │ 20,0  │ ← Stock Final Mardi
│ Sauce tomate│ 15,0  │ [___] │ 15,0  │ ← Stock Final Mardi
│ Pepperoni   │ 11,0  │ [___] │ 11,0  │ ← Stock Final Mardi
│ Olives      │  6,0  │ [___] │  6,0  │ ← Stock Final Mardi
└─────────────┴───────┴───────┴───────┘
```

## 🎯 Principe de Synchronisation

### 📊 Formule Simple

**Stock Final Jour J = Stock Initial Jour J+1**

### 🔄 Automatisme

1. **Vous saisissez** Stock Initial + Entrées
2. **Système calcule** Stock Final automatiquement
3. **Système synchronise** avec le jour suivant
4. **Lendemain** : Stock Initial déjà rempli

## 🚀 Test Pratique

### 📱 Étapes de Test

1. **Lancez l'app** : `npx expo start`
2. **Connexion** : admin / 123
3. **Cliquez "Stock"**

### 📝 Test Jour 1

4. **Remplissez Stock Initial** :
   - Pâte : `25,0`
   - Mozzarella : `15,0`
   - Sauce : `10,0`

5. **Remplissez Entrées** :
   - Pâte : `10,0`
   - Mozzarella : `5,0`
   - Sauce : `3,0`

6. **Vérifiez Stock Final** :
   - Pâte : `35,0` ✅
   - Mozzarella : `20,0` ✅
   - Sauce : `13,0` ✅

7. **Sauvegardez** : "💾 Sauver"

### 📝 Test Jour 2

8. **Passez à demain** : "Demain →"

9. **Vérifiez Stock Initial** :
   - Pâte : `35,0` ✅ (automatique)
   - Mozzarella : `20,0` ✅ (automatique)
   - Sauce : `13,0` ✅ (automatique)

10. **Ajoutez Entrées** :
    - Pâte : `5,0`
    - Sauce : `2,0`

11. **Vérifiez Nouveau Stock Final** :
    - Pâte : `40,0` ✅
    - Sauce : `15,0` ✅

## 🎉 Avantages du Système

### ✅ Simplicité
- **Tableau direct** : Tout visible
- **Saisie simple** : Juste remplir les cases
- **Calcul automatique** : Stock Final

### ✅ Synchronisation
- **Automatique** : Stock Final J → Stock Initial J+1
- **Pas de ressaisie** : Stock Initial déjà rempli
- **Continuité parfaite** : Entre tous les jours

### ✅ Facilité
- **Un bouton** : "💾 Sauver"
- **Navigation simple** : "← Hier" / "Demain →"
- **Interface claire** : Tableau lisible

## 📋 Résumé d'Utilisation

1. **Premier jour** : Saisissez Stock Initial + Entrées
2. **Sauvegardez** : Synchronisation automatique
3. **Jours suivants** : Stock Initial déjà rempli
4. **Ajoutez seulement** : Les Entrées du jour
5. **Sauvegardez** : Synchronisation continue

**📦 Votre stock est maintenant simple et automatique !**
