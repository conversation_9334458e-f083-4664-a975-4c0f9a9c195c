import { Pizza } from '../types';

export const PIZZAS: Pizza[] = [
  {
    id: '1',
    name: '<PERSON><PERSON><PERSON><PERSON>',
    description: 'Sauce tomate, mozzarella, basilic frais',
    basePrice: 12.50,
    image: '🍕',
    category: 'Classique',
    ingredients: ['Sauce tomate', 'Mozzarella', 'Basilic'],
    sizes: [
      { name: 'Petite', multiplier: 0.8 },
      { name: '<PERSON>ye<PERSON>', multiplier: 1.0 },
      { name: 'Grande', multiplier: 1.3 },
      { name: 'Familiale', multiplier: 1.6 }
    ]
  },
  {
    id: '2',
    name: '<PERSON><PERSON>',
    description: 'Sauce tomate, mozzarella, pepperoni',
    basePrice: 15.00,
    image: '🍕',
    category: 'Classique',
    ingredients: ['Sauce tomate', 'Mozzarella', 'Pepperoni'],
    sizes: [
      { name: 'Petite', multiplier: 0.8 },
      { name: '<PERSON>yenne', multiplier: 1.0 },
      { name: '<PERSON>', multiplier: 1.3 },
      { name: 'Familiale', multiplier: 1.6 }
    ]
  },
  {
    id: '3',
    name: 'Quatre Fromages',
    description: 'Mozzarella, gorgonzola, parmesan, chèvre',
    basePrice: 17.50,
    image: '🧀',
    category: 'Spécialité',
    ingredients: ['Mozzarella', 'Gorgonzola', 'Parmesan', 'Chèvre'],
    sizes: [
      { name: 'Petite', multiplier: 0.8 },
      { name: 'Moyenne', multiplier: 1.0 },
      { name: 'Grande', multiplier: 1.3 },
      { name: 'Familiale', multiplier: 1.6 }
    ]
  },
  {
    id: '4',
    name: 'Végétarienne',
    description: 'Sauce tomate, mozzarella, légumes grillés',
    basePrice: 16.00,
    image: '🥬',
    category: 'Végétarienne',
    ingredients: ['Sauce tomate', 'Mozzarella', 'Poivrons', 'Courgettes', 'Aubergines'],
    sizes: [
      { name: 'Petite', multiplier: 0.8 },
      { name: 'Moyenne', multiplier: 1.0 },
      { name: 'Grande', multiplier: 1.3 },
      { name: 'Familiale', multiplier: 1.6 }
    ]
  },
  {
    id: '5',
    name: 'Calzone',
    description: 'Pizza fermée avec jambon, mozzarella, champignons',
    basePrice: 18.00,
    image: '🥟',
    category: 'Spécialité',
    ingredients: ['Jambon', 'Mozzarella', 'Champignons', 'Sauce tomate'],
    sizes: [
      { name: 'Petite', multiplier: 0.8 },
      { name: 'Moyenne', multiplier: 1.0 },
      { name: 'Grande', multiplier: 1.3 },
      { name: 'Familiale', multiplier: 1.6 }
    ]
  },
  {
    id: '6',
    name: 'Fruits de Mer',
    description: 'Sauce tomate, mozzarella, fruits de mer',
    basePrice: 22.00,
    image: '🦐',
    category: 'Premium',
    ingredients: ['Sauce tomate', 'Mozzarella', 'Crevettes', 'Moules', 'Calamars'],
    sizes: [
      { name: 'Petite', multiplier: 0.8 },
      { name: 'Moyenne', multiplier: 1.0 },
      { name: 'Grande', multiplier: 1.3 },
      { name: 'Familiale', multiplier: 1.6 }
    ]
  }
];
