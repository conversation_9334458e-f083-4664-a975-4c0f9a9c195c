# 📦 TEST DU STOCK SIMPLE - YASSINAPP

## 🎯 Test de la Synchronisation Automatique

Voici comment tester le système de stock avec synchronisation automatique.

## 📱 Interface de Test

```
┌─────────────────────────────────────┐
│ [← Retour]  Stock du Jour  [💾 Sauver] │
├─────────────────────────────────────┤
│ [← Hier]    Lundi 15 juillet   [<PERSON><PERSON><PERSON> →] │
├─────────────────────────────────────┤
│   Produit   │Initial│Entrées│ Final │
├─────────────┼───────┼───────┼───────┤
│ Pâte à pizza│ [___] │ [___] │ [___] │
│    (kg)     │       │       │       │
├─────────────┼───────┼───────┼───────┤
│ Mozzarella  │ [___] │ [___] │ [___] │
│    (kg)     │       │       │       │
├─────────────┼───────┼───────┼───────┤
│ Sauce tomate│ [___] │ [___] │ [___] │
│    (L)      │       │       │       │
├─────────────┼───────┼───────┼───────┤
│ Pepperoni   │ [___] │ [___] │ [___] │
│    (kg)     │       │       │       │
├─────────────┼───────┼───────┼───────┤
│ Olives      │ [___] │ [___] │ [___] │
│    (kg)     │       │       │       │
└─────────────┴───────┴───────┴───────┘
```

## 🚀 Étapes de Test

### 📱 1. Lancement de l'Application

```cmd
cd "C:\Users\<USER>\Documents\AppCaisse\YassinApp"
npx expo start
```

### 🔑 2. Connexion

- **Nom d'utilisateur** : `admin`
- **Mot de passe** : `123`

### 📦 3. Accès au Stock

1. **Cliquez sur "Stock"** dans l'écran principal
2. **Le tableau s'affiche** directement

## 📝 TEST JOUR 1 - Premier Jour

### 🔢 Saisie Initiale

**Remplissez le tableau :**

```
┌─────────────┬───────┬───────┬───────┐
│   Produit   │Initial│Entrées│ Final │
├─────────────┼───────┼───────┼───────┤
│ Pâte à pizza│ [25,0]│ [10,0]│ [30,0]│
│ Mozzarella  │ [15,0]│ [ 5,0]│ [18,0]│
│ Sauce tomate│ [10,0]│ [ 3,0]│ [12,0]│
│ Pepperoni   │ [ 8,0]│ [ 2,0]│ [ 9,0]│
│ Olives      │ [ 5,0]│ [ 1,0]│ [ 5,5]│
└─────────────┴───────┴───────┴───────┘
```

**Actions :**
1. **Tapez** dans Stock Initial Pâte : `25,0`
2. **Tapez** dans Entrées Pâte : `10,0`
3. **Tapez** dans Stock Final Pâte : `30,0`
4. **Répétez** pour tous les produits
5. **Cliquez "💾 Sauver"**

### ✅ Vérification Après Sauvegarde

**Le tableau doit garder vos valeurs :**
```
┌─────────────┬───────┬───────┬───────┐
│   Produit   │Initial│Entrées│ Final │
├─────────────┼───────┼───────┼───────┤
│ Pâte à pizza│ 25,0  │ 10,0  │ 30,0  │ ← Valeurs gardées
│ Mozzarella  │ 15,0  │  5,0  │ 18,0  │ ← Valeurs gardées
│ Sauce tomate│ 10,0  │  3,0  │ 12,0  │ ← Valeurs gardées
│ Pepperoni   │  8,0  │  2,0  │  9,0  │ ← Valeurs gardées
│ Olives      │  5,0  │  1,0  │  5,5  │ ← Valeurs gardées
└─────────────┴───────┴───────┴───────┘
```

## 📝 TEST JOUR 2 - Synchronisation

### 🔄 Passage au Jour Suivant

1. **Cliquez "Demain →"**
2. **Vérifiez la synchronisation**

**Le tableau du jour 2 doit afficher :**
```
┌─────────────┬───────┬───────┬───────┐
│   Produit   │Initial│Entrées│ Final │
├─────────────┼───────┼───────┼───────┤
│ Pâte à pizza│ 30,0  │       │       │ ← Stock Final Jour 1
│ Mozzarella  │ 18,0  │       │       │ ← Stock Final Jour 1
│ Sauce tomate│ 12,0  │       │       │ ← Stock Final Jour 1
│ Pepperoni   │  9,0  │       │       │ ← Stock Final Jour 1
│ Olives      │  5,5  │       │       │ ← Stock Final Jour 1
└─────────────┴───────┴───────┴───────┘
```

### 🔢 Saisie Jour 2

**Ajoutez Entrées et Stock Final :**
```
┌─────────────┬───────┬───────┬───────┐
│   Produit   │Initial│Entrées│ Final │
├─────────────┼───────┼───────┼───────┤
│ Pâte à pizza│ 30,0  │ [ 5,0]│ [32,0]│
│ Mozzarella  │ 18,0  │ [ 0,0]│ [17,0]│
│ Sauce tomate│ 12,0  │ [ 2,0]│ [13,5]│
│ Pepperoni   │  9,0  │ [ 1,0]│ [ 9,5]│
│ Olives      │  5,5  │ [ 0,0]│ [ 5,0]│
└─────────────┴───────┴───────┴───────┘
```

**Actions :**
1. **Tapez** Entrées pour chaque produit
2. **Tapez** Stock Final pour chaque produit
3. **Cliquez "💾 Sauver"**

## 📝 TEST JOUR 3 - Continuité

### 🔄 Vérification Continue

1. **Cliquez "Demain →"** encore
2. **Vérifiez** que le Stock Initial = Stock Final Jour 2

**Le tableau du jour 3 doit afficher :**
```
┌─────────────┬───────┬───────┬───────┐
│   Produit   │Initial│Entrées│ Final │
├─────────────┼───────┼───────┼───────┤
│ Pâte à pizza│ 32,0  │       │       │ ← Stock Final Jour 2
│ Mozzarella  │ 17,0  │       │       │ ← Stock Final Jour 2
│ Sauce tomate│ 13,5  │       │       │ ← Stock Final Jour 2
│ Pepperoni   │  9,5  │       │       │ ← Stock Final Jour 2
│ Olives      │  5,0  │       │       │ ← Stock Final Jour 2
└─────────────┴───────┴───────┴───────┘
```

## ✅ Points de Vérification

### 🔍 Après Chaque Sauvegarde

1. **Valeurs gardées** : Le tableau garde vos saisies
2. **Pas de vidage** : Les champs restent remplis
3. **Message de succès** : "Stock sauvegardé et synchronisé"

### 🔍 Après Changement de Jour

1. **Stock Initial** : = Stock Final du jour précédent
2. **Entrées vides** : Prêtes pour nouvelle saisie
3. **Stock Final vide** : Prêt pour nouvelle saisie

### 🔍 Navigation

1. **"← Hier"** : Retour au jour précédent avec valeurs
2. **"Demain →"** : Passage au jour suivant avec sync
3. **Date affichée** : Correcte et en français

## 🚨 Problèmes Possibles

### ❌ Si le Tableau se Vide

**Symptôme** : Après sauvegarde, tous les champs sont vides
**Solution** : Le système recharge maintenant les données

### ❌ Si Pas de Synchronisation

**Symptôme** : Stock Initial du lendemain = 0
**Solution** : Vérifiez que vous avez saisi le Stock Final

### ❌ Si Erreur de Sauvegarde

**Symptôme** : Message d'erreur
**Solution** : Vérifiez que tous les champs sont remplis

## 🎯 Résultat Attendu

### ✅ Fonctionnement Correct

1. **Saisie** : Vous remplissez tout le tableau
2. **Sauvegarde** : Valeurs gardées dans le tableau
3. **Synchronisation** : Stock Final J → Stock Initial J+1
4. **Continuité** : Chaque jour commence avec le bon stock

### ✅ Workflow Quotidien

1. **Ouvrir Stock** : Stock Initial déjà rempli (sauf jour 1)
2. **Saisir** : Entrées et Stock Final
3. **Sauvegarder** : Valeurs gardées + sync automatique
4. **Lendemain** : Stock Initial automatique

## 📋 Checklist de Test

- [ ] Application lancée et connectée
- [ ] Stock accessible depuis l'écran principal
- [ ] Tableau affiché avec 5 produits
- [ ] Saisie possible dans tous les champs
- [ ] Sauvegarde garde les valeurs
- [ ] Synchronisation fonctionne (J → J+1)
- [ ] Navigation entre les jours
- [ ] Continuité sur plusieurs jours

**📦 Si tous ces points fonctionnent, votre stock est parfait !**
