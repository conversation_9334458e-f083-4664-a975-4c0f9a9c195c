import React from 'react';
import { NavigationContainer } from '@react-navigation/native';
import { createStackNavigator } from '@react-navigation/stack';
import { StatusBar } from 'react-native';

import { PizzaListScreen } from './src/screens/PizzaListScreen';
import { OrderFormScreen } from './src/screens/OrderFormScreen';
import { StatisticsScreen } from './src/screens/StatisticsScreen';

const Stack = createStackNavigator();

function App(): React.JSX.Element {
  return (
    <NavigationContainer>
      <StatusBar barStyle="light-content" backgroundColor="#2c3e50" />
      <Stack.Navigator
        initialRouteName="PizzaList"
        screenOptions={{
          headerShown: false,
        }}
      >
        <Stack.Screen
          name="PizzaList"
          component={PizzaListScreen}
          options={{ title: 'Pizza Caisse Manager' }}
        />
        <Stack.Screen
          name="OrderForm"
          component={OrderFormScreen}
          options={{ title: 'Finaliser la commande' }}
        />
        <Stack.Screen
          name="Statistics"
          component={StatisticsScreen}
          options={{ title: 'Statistiques' }}
        />
      </Stack.Navigator>
    </NavigationContainer>
  );
}

export default App;
