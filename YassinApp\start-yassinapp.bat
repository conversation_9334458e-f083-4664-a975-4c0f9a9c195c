@echo off
chcp 65001 >nul
cls

echo ==========================================
echo    🍕 YASSINAPP - VERSION CALENDRIER 2.4
echo ==========================================
echo.
echo 📱 Application: YassinApp
echo 🎯 Version: 2.4.0 - Calendrier et Annulations (21 juillet 2025)
echo.
echo 🎨 NOUVELLES FONCTIONNALITÉS V2.4:
echo    ✓ Calendrier intégré pour sélection de date
echo    ✓ Commandes annulées exclues automatiquement
echo    ✓ Recalcul des totaux en temps réel
echo    ✓ Interface calendrier moderne
echo    ✓ Gestion complète des annulations
echo    ✓ Frais de livraison variables
echo.
echo 📱 FONCTIONNALITÉS COMPLÈTES:
echo    ✓ Connexion admin (admin / 123)
echo    ✓ SYSTÈME D'OFFRES/PROMOS
echo    ✓ Offre Dimanche: Pizza L au choix à 22 DT
echo    ✓ Promo Family: 2 Pizzas L + Nuggets + Coca 33cl
echo    ✓ Sélection interactive des choix
echo    ✓ Formulaire de commande complet
echo    ✓ Statistiques détaillées avec graphiques
echo    ✓ Gestion du stock avec alertes
echo    ✓ Système de promotions et codes promo
echo    ✓ Interface tactile optimisée pour mobile
echo.
echo ==========================================

cd /d "C:\Users\<USER>\Documents\AppCaisse\YassinApp"

if not exist "package.json" (
    echo ❌ ERREUR: Fichier package.json non trouvé
    echo Vérifiez que vous êtes dans le bon répertoire
    pause
    exit /b 1
)

echo 📂 Répertoire: %CD%
echo.

echo 🧹 Nettoyage du cache...
if exist ".expo" rmdir /s /q ".expo" >nul 2>&1
if exist "node_modules\.cache" rmdir /s /q "node_modules\.cache" >nul 2>&1
echo ✅ Cache nettoyé
echo.

echo 📦 Vérification des dépendances...
if not exist "node_modules" (
    echo 📥 Installation des dépendances...
    npm install
) else (
    echo ✅ Dépendances déjà installées
)
echo.

echo 🚀 Démarrage de YassinApp...
echo 📱 L'application va se lancer avec toutes les fonctionnalités
echo.
echo 🔑 Identifiants de connexion:
echo    Nom d'utilisateur: admin
echo    Mot de passe: 123
echo.
echo 📋 NOUVELLES FONCTIONNALITÉS À TESTER:
echo    1. Connexion admin (admin / 123)
echo    2. Allez dans Statistiques → Commandes par jour
echo    3. Testez le calendrier: Cliquez sur "🗓️ Choisir date"
echo    4. Sélectionnez différentes dates dans le calendrier
echo    5. Observez les commandes et totaux pour chaque date
echo    6. Cliquez sur une commande "Livrée" → Détails
echo    7. Testez "❌ Annuler Commande" et observez:
echo       - Confirmation de sécurité
echo       - Disparition de la commande de la liste
echo       - Recalcul automatique des totaux
echo    8. Testez l'impression de tickets
echo    9. Ajoutez des frais de livraison dans les commandes
echo.
echo ==========================================
echo.

npx expo start --clear

echo.
echo ==========================================
echo 📱 YassinApp lancée !
echo.
echo 🔍 Si le QR code n'apparaît pas:
echo 1. Fermez cette fenêtre
echo 2. Ouvrez cmd en tant qu'administrateur
echo 3. Tapez: cd "C:\Users\<USER>\Documents\AppCaisse\YassinApp"
echo 4. Tapez: npx expo start
echo.
echo 📱 Scannez le QR code avec Expo Go pour tester
echo 🎯 Ou appuyez sur 'w' pour ouvrir dans le navigateur
echo.
echo ==========================================
pause
