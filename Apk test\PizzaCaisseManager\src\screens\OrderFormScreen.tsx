import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TextInput,
  TouchableOpacity,
  ScrollView,
  Alert,
  FlatList,
} from 'react-native';
import { OrderItem, Order, Customer } from '../types';
import { StorageService } from '../services/StorageService';
import { StatisticsService } from '../services/StatisticsService';

interface OrderFormScreenProps {
  navigation: any;
  route: {
    params: {
      cartItems: OrderItem[];
    };
  };
}

export const OrderFormScreen: React.FC<OrderFormScreenProps> = ({ navigation, route }) => {
  const { cartItems } = route.params;
  const [customerName, setCustomerName] = useState('');
  const [customerAddress, setCustomerAddress] = useState('');
  const [customerPhone, setCustomerPhone] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);

  const totalAmount = cartItems.reduce((sum, item) => sum + item.totalPrice, 0);

  const validateForm = (): boolean => {
    if (!customerName.trim()) {
      Alert.alert('Erreur', 'Veuillez saisir le nom du client');
      return false;
    }
    if (!customerAddress.trim()) {
      Alert.alert('Erreur', 'Veuillez saisir l\'adresse du client');
      return false;
    }
    if (!customerPhone.trim()) {
      Alert.alert('Erreur', 'Veuillez saisir le numéro de téléphone du client');
      return false;
    }
    
    // Validation basique du numéro de téléphone
    const phoneRegex = /^[0-9+\-\s()]{8,}$/;
    if (!phoneRegex.test(customerPhone.trim())) {
      Alert.alert('Erreur', 'Veuillez saisir un numéro de téléphone valide');
      return false;
    }

    return true;
  };

  const handleConfirmOrder = async () => {
    if (!validateForm()) return;

    setIsSubmitting(true);

    try {
      const customer: Customer = {
        name: customerName.trim(),
        address: customerAddress.trim(),
        phoneNumber: customerPhone.trim(),
      };

      const order: Order = {
        id: Date.now().toString(),
        customer,
        items: cartItems,
        totalAmount,
        status: 'confirmed',
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      await StorageService.saveOrder(order);
      await StatisticsService.calculateStatistics();

      Alert.alert(
        'Commande confirmée !',
        `Commande #${order.id} confirmée pour ${customer.name}\nMontant total: ${totalAmount.toFixed(2)}€`,
        [
          {
            text: 'OK',
            onPress: () => {
              navigation.reset({
                index: 0,
                routes: [{ name: 'PizzaList' }],
              });
            },
          },
        ]
      );
    } catch (error) {
      console.error('Erreur lors de la confirmation de la commande:', error);
      Alert.alert('Erreur', 'Une erreur est survenue lors de la confirmation de la commande');
    } finally {
      setIsSubmitting(false);
    }
  };

  const renderCartItem = ({ item }: { item: OrderItem }) => (
    <View style={styles.cartItem}>
      <View style={styles.cartItemInfo}>
        <Text style={styles.cartItemName}>{item.pizzaName}</Text>
        <Text style={styles.cartItemDetails}>
          Taille {item.size} × {item.quantity}
        </Text>
      </View>
      <Text style={styles.cartItemPrice}>{item.totalPrice.toFixed(2)}€</Text>
    </View>
  );

  return (
    <ScrollView style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity onPress={() => navigation.goBack()}>
          <Text style={styles.backButton}>← Retour</Text>
        </TouchableOpacity>
        <Text style={styles.title}>Finaliser la commande</Text>
      </View>

      {/* Récapitulatif du panier */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Récapitulatif de la commande</Text>
        <FlatList
          data={cartItems}
          renderItem={renderCartItem}
          keyExtractor={(item, index) => `${item.pizzaId}-${item.size}-${index}`}
          scrollEnabled={false}
        />
        <View style={styles.totalContainer}>
          <Text style={styles.totalText}>Total: {totalAmount.toFixed(2)}€</Text>
        </View>
      </View>

      {/* Formulaire client */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Informations client</Text>
        
        <View style={styles.inputContainer}>
          <Text style={styles.inputLabel}>Nom du client *</Text>
          <TextInput
            style={styles.input}
            value={customerName}
            onChangeText={setCustomerName}
            placeholder="Saisissez le nom du client"
            autoCapitalize="words"
          />
        </View>

        <View style={styles.inputContainer}>
          <Text style={styles.inputLabel}>Adresse *</Text>
          <TextInput
            style={[styles.input, styles.multilineInput]}
            value={customerAddress}
            onChangeText={setCustomerAddress}
            placeholder="Saisissez l'adresse de livraison"
            multiline
            numberOfLines={3}
            autoCapitalize="sentences"
          />
        </View>

        <View style={styles.inputContainer}>
          <Text style={styles.inputLabel}>Numéro de téléphone *</Text>
          <TextInput
            style={styles.input}
            value={customerPhone}
            onChangeText={setCustomerPhone}
            placeholder="Saisissez le numéro de téléphone"
            keyboardType="phone-pad"
          />
        </View>
      </View>

      {/* Bouton de confirmation */}
      <View style={styles.buttonContainer}>
        <TouchableOpacity
          style={[styles.confirmButton, isSubmitting && styles.disabledButton]}
          onPress={handleConfirmOrder}
          disabled={isSubmitting}
        >
          <Text style={styles.confirmButtonText}>
            {isSubmitting ? 'Confirmation...' : `Confirmer la commande - ${totalAmount.toFixed(2)}€`}
          </Text>
        </TouchableOpacity>
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  header: {
    backgroundColor: '#2c3e50',
    paddingTop: 50,
    paddingBottom: 20,
    paddingHorizontal: 16,
    flexDirection: 'row',
    alignItems: 'center',
  },
  backButton: {
    color: '#3498db',
    fontSize: 16,
    fontWeight: '600',
    marginRight: 16,
  },
  title: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#fff',
  },
  section: {
    backgroundColor: '#fff',
    margin: 16,
    borderRadius: 12,
    padding: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 16,
  },
  cartItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 8,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  cartItemInfo: {
    flex: 1,
  },
  cartItemName: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
  },
  cartItemDetails: {
    fontSize: 14,
    color: '#666',
    marginTop: 2,
  },
  cartItemPrice: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#e74c3c',
  },
  totalContainer: {
    marginTop: 16,
    paddingTop: 16,
    borderTopWidth: 2,
    borderTopColor: '#e74c3c',
    alignItems: 'flex-end',
  },
  totalText: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#e74c3c',
  },
  inputContainer: {
    marginBottom: 16,
  },
  inputLabel: {
    fontSize: 14,
    fontWeight: '600',
    color: '#333',
    marginBottom: 8,
  },
  input: {
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 10,
    fontSize: 16,
    backgroundColor: '#fff',
  },
  multilineInput: {
    height: 80,
    textAlignVertical: 'top',
  },
  buttonContainer: {
    padding: 16,
  },
  confirmButton: {
    backgroundColor: '#27ae60',
    borderRadius: 8,
    paddingVertical: 16,
    alignItems: 'center',
  },
  disabledButton: {
    backgroundColor: '#95a5a6',
  },
  confirmButtonText: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#fff',
  },
});
