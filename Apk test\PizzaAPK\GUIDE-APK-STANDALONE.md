# 📱 Guide pour Créer l'APK Standalone - Caisse 2.0

## 🎯 Application Finale
- **Nom**: Pizza Caisse Manager
- **Version**: 2.0.0 (Build 37)
- **Type**: APK Standalone (sans Expo Go)

## ✅ Fonctionnalités Incluses
- ✓ Gestion des commandes complète
- ✓ Gestion du stock avec continuité automatique
- ✓ Statistiques avec export PDF professionnel
- ✓ Interface tactile optimisée
- ✓ Saisie avec virgule (12,5 fonctionne)
- ✓ Calendrier pour sélection de dates
- ✓ PDF avec design rouge magnifique
- ✓ Bouton "Vider" pour reset du stock
- ✓ Calculs automatiques corrigés

## 🚀 Étapes pour Créer l'APK

### 1. Préparation
```cmd
cd "C:\Users\<USER>\Documents\AppCaisse\Apk test\PizzaAPK"
```

### 2. Connexion à Expo
```cmd
npx expo login
```
- Email: `<EMAIL>`
- Mot de passe: `Themer@10`

### 3. Création de l'APK
```cmd
npx eas build --platform android --profile production
```

### 4. Alternative si EAS ne marche pas
```cmd
npx expo build:android -t apk
```

## 📋 Instructions d'Installation de l'APK

### Sur votre téléphone Android :

1. **Téléchargez l'APK**
   - Copiez le lien fourni par Expo
   - Téléchargez le fichier APK

2. **Autorisez les sources inconnues**
   - Allez dans Paramètres > Sécurité
   - Activez "Sources inconnues" ou "Installer des apps inconnues"

3. **Installez l'APK**
   - Ouvrez le fichier APK téléchargé
   - Appuyez sur "Installer"
   - Attendez la fin de l'installation

4. **Lancez l'application**
   - Trouvez l'icône "Caisse" sur votre écran d'accueil
   - Appuyez pour lancer l'application

## 🎉 Application Prête !

Votre application **Caisse 2.0** est maintenant installée et prête à utiliser !

### Fonctionnalités principales :
- **Commandes** : Créez et gérez vos commandes de pizza
- **Stock** : Gérez votre stock avec continuité automatique
- **Stats** : Consultez vos statistiques et exportez en PDF

### Plus besoin d'Expo Go !
L'application fonctionne de manière autonome sur votre téléphone.

## 🔧 Dépannage

Si vous avez des problèmes :

1. **Erreur d'installation** : Vérifiez que les sources inconnues sont activées
2. **Application ne démarre pas** : Redémarrez votre téléphone
3. **Problème de build** : Vérifiez votre connexion internet

## 📞 Support

Si vous avez besoin d'aide, vérifiez :
- Que vous êtes connecté à internet
- Que votre compte Expo fonctionne
- Que toutes les dépendances sont installées

---

**🎯 Version Finale : Caisse 2.0 - Application complète et professionnelle !**
