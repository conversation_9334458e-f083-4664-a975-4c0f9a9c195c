@echo off
chcp 65001 >nul
cls

echo ==========================================
echo    PIZZA CAISSE MANAGER - VERSION 2.0
echo ==========================================
echo.
echo Version du 6 juillet 2025 - 15h45
echo Titre: "Pizza Caisse Manager" (original)
echo Boutons: SEULEMENT Stats (comme 6 juillet)
echo Manipulation tactile: Amelioree
echo Interface: EXACTEMENT comme 6 juillet 2025
echo.

cd /d "C:\Users\<USER>\Documents\AppCaisse\Apk test\PizzaAPK"

if not exist "package.json" (
    echo ERREUR: Fichier package.json non trouve
    echo Verifiez que vous etes dans le bon repertoire
    pause
    exit /b 1
)

echo Repertoire: %CD%
echo.

echo Nettoyage du cache...
if exist ".expo" rmdir /s /q ".expo" >nul 2>&1
if exist "node_modules\.cache" rmdir /s /q "node_modules\.cache" >nul 2>&1
echo Cache nettoye
echo.

echo Verification des dependances...
if not exist "node_modules" (
    echo Installation des dependances...
    npm install
)
echo.

echo Demarrage d'Expo...
echo Le QR code va apparaitre ci-dessous:
echo ==========================================
echo.

npx expo start --clear

echo.
echo ==========================================
echo Si le QR code n'apparait pas:
echo 1. Fermez cette fenetre
echo 2. Ouvrez cmd en tant qu'administrateur
echo 3. Tapez: cd "C:\Users\<USER>\Documents\AppCaisse\Apk test\PizzaAPK"
echo 4. Tapez: npx expo start
echo ==========================================
pause
