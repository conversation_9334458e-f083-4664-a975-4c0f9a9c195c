import React, { useState, useEffect, useCallback } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  RefreshControl,
  FlatList,
  Alert,
  TextInput,
} from 'react-native';
import { Statistics, Order } from '../types';
import { StatisticsService } from '../services/StatisticsService';
import { StorageService } from '../services/StorageService';
import { PDFService } from '../services/PDFService';
import { useCustomModal } from '../hooks/useCustomModal';

interface StatisticsScreenProps {
  navigation: any;
}

export const StatisticsScreen: React.FC<StatisticsScreenProps> = ({ navigation }) => {
  const [recentOrders, setRecentOrders] = useState<Order[]>([]);
  const [allOrders, setAllOrders] = useState<Order[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);

  const { showDestructiveConfirm, showAlert, ModalComponent } = useCustomModal();
  // Initialiser avec la date du jour au format YYYY-MM-DD
  const today = new Date().toISOString().split('T')[0];
  const currentMonth = new Date().toISOString().slice(0, 7); // YYYY-MM
  const [selectedDate, setSelectedDate] = useState<string>(today);
  const [selectedMonth, setSelectedMonth] = useState<string>(currentMonth);
  const [monthlyOrders, setMonthlyOrders] = useState<Order[]>([]);

  const loadData = useCallback(async () => {
    try {
      console.log('🔄 Chargement des données...');
      const orders = await StorageService.getOrders();
      console.log('📦 Commandes chargées:', orders.length);
      setAllOrders(orders);

      // Filtrer les commandes d'aujourd'hui par défaut
      const todayOrders = orders.filter(order => {
        const orderDate = new Date(order.createdAt);
        const todayDate = new Date();
        return orderDate.toDateString() === todayDate.toDateString();
      });
      setRecentOrders([...todayOrders].reverse());
      console.log(`📊 Commandes d'aujourd'hui:`, todayOrders.length);

      // Initialiser les données mensuelles avec le mois actuel
      const currentMonthOrders = orders.filter(order => {
        const orderDate = new Date(order.createdAt);
        const orderMonth = orderDate.toISOString().slice(0, 7);
        return orderMonth === currentMonth;
      });
      setMonthlyOrders([...currentMonthOrders].reverse());
      console.log(`📊 Commandes du mois actuel (${currentMonth}):`, currentMonthOrders.length);

      // Calculer les statistiques en temps réel
      const stats = await StatisticsService.calculateStatistics();
      console.log('📈 Statistiques calculées:', stats);

    } catch (error) {
      console.error('❌ Erreur lors du chargement des données:', error);
      setAllOrders([]);
      setRecentOrders([]);
    } finally {
      setIsLoading(false);
      setRefreshing(false);
    }
  }, [currentMonth]);



  useEffect(() => {
    loadData();
  }, [loadData]);

  const onRefresh = useCallback(() => {
    setRefreshing(true);
    loadData();
  }, [loadData]);

  // Fonctions utilitaires pour les dates
  const getYesterday = () => {
    const yesterday = new Date();
    yesterday.setDate(yesterday.getDate() - 1);
    return yesterday.toISOString().split('T')[0];
  };

  const formatDisplayDate = (dateString: string) => {
    if (!dateString) return '';
    const date = new Date(dateString + 'T00:00:00');
    return date.toLocaleDateString('fr-FR', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  const getPreviousMonth = () => {
    const date = new Date();
    date.setMonth(date.getMonth() - 1);
    return date.toISOString().slice(0, 7);
  };

  const formatDisplayMonth = (monthString: string) => {
    if (!monthString) return '';
    const [year, month] = monthString.split('-');
    const date = new Date(parseInt(year), parseInt(month) - 1);
    return date.toLocaleDateString('fr-FR', {
      year: 'numeric',
      month: 'long'
    });
  };

  const handleDateChange = (date: string) => {
    setSelectedDate(date);

    if (!date) {
      // Si pas de date, afficher toutes les commandes
      setRecentOrders([...allOrders].reverse());
      return;
    }

    // Filtrer par la date sélectionnée
    try {
      const targetDate = new Date(date);
      const filteredOrders = allOrders.filter(order => {
        const orderDate = new Date(order.createdAt);
        return orderDate.toDateString() === targetDate.toDateString();
      });
      setRecentOrders([...filteredOrders].reverse());
    } catch (error) {
      console.error('Erreur de filtrage:', error);
      setRecentOrders([...allOrders].reverse());
    }
  };

  const handleMonthChange = (month: string) => {
    console.log('📅 Changement de mois:', month);
    setSelectedMonth(month);

    if (!month) {
      // Si pas de mois, afficher toutes les commandes
      setMonthlyOrders([...allOrders].reverse());
      return;
    }

    try {
      const filteredOrders = allOrders.filter(order => {
        const orderDate = new Date(order.createdAt);
        const orderMonth = orderDate.toISOString().slice(0, 7); // YYYY-MM
        return orderMonth === month;
      });
      setMonthlyOrders([...filteredOrders].reverse());
      console.log(`📊 Commandes du mois ${month}:`, filteredOrders.length);
    } catch (error) {
      console.error('Erreur de filtrage mensuel:', error);
      setMonthlyOrders([...allOrders].reverse());
    }
  };



  // VERSION WEB NATIVE - Bouton Détails
  const showOrderDetails = (order: Order) => {
    console.log('🔍 showOrderDetails appelée pour:', order.id);

    const message = `DÉTAILS DE LA COMMANDE #${order.id.slice(-6)}

👤 Client: ${order.customer?.name || 'N/A'}
📍 Adresse: ${order.customer?.address || 'N/A'}
📞 Téléphone: ${order.customer?.phoneNumber || 'N/A'}
📅 Date: ${new Date(order.createdAt).toLocaleDateString()}

🍕 Pizzas commandées:
${order.items.map(item => `• ${item.quantity}x ${item.pizzaName} (${item.size}) - ${(item.quantity * item.unitPrice).toFixed(2)} DT`).join('\n')}

💰 TOTAL: ${order.totalAmount.toFixed(2)} DT`;

    // Utiliser le modal personnalisé
    showAlert('📋 DÉTAILS DE LA COMMANDE', message);
  };

  // VERSION WEB NATIVE - Bouton Annuler
  const cancelOrder = async (orderId: string) => {
    console.log('🔴 cancelOrder appelée pour:', orderId);

    // Utiliser le modal personnalisé pour la confirmation
    showDestructiveConfirm(
      '⚠️ ANNULER LA COMMANDE',
      `Le client souhaite-t-il vraiment annuler la commande #${orderId.slice(-6)} ?

Cette action est définitive !`,
      async () => {
        console.log('✅ Annulation confirmée, suppression en cours...');
        try {
          await StorageService.deleteOrder(orderId);
          console.log('✅ Commande supprimée du storage');

          // Recharger les données
          await loadData();
          console.log('✅ Données rechargées');

          // Message de succès
          showAlert('✅ SUCCÈS !', `La commande #${orderId.slice(-6)} a été annulée et supprimée.`);

        } catch (error) {
          console.error('❌ Erreur lors de l\'annulation:', error);
          showAlert('❌ ERREUR !', `Impossible d'annuler la commande.\nErreur: ${error.message}`);
        }
      },
      () => {
        console.log('ℹ️ Annulation annulée par l\'utilisateur');
        showAlert('ℹ️ Information', 'Annulation annulée - La commande est conservée.');
      },
      'Annuler Commande',
      'Conserver'
    );
  };

  const handleConfirmOrder = async (orderId: string) => {
    showDestructiveConfirm(
      'Confirmer la commande',
      'Marquer cette commande comme confirmée ?',
      async () => {
        try {
          await StorageService.updateOrderStatus(orderId, 'confirmed');
          loadData(); // Recharger les données
          showAlert('Succès', 'Commande confirmée avec succès');
        } catch (error) {
          console.error('Erreur lors de la confirmation:', error);
          showAlert('Erreur', 'Impossible de confirmer la commande');
        }
      },
      () => {
        // Annulation - ne rien faire
      },
      'Confirmer',
      'Annuler'
    );
  };

  const formatDate = (date: Date) => {
    return new Intl.DateTimeFormat('fr-FR', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    }).format(date);
  };

  const exportToPDF = async () => {
    try {
      showAlert('📄 Export PDF', 'Génération du PDF en cours...');

      await PDFService.generateDailyStatsPDF(allOrders, selectedDate);

      // Le PDF est automatiquement partagé par le service
      // Pas besoin de message de succès car l'utilisateur voit le partage
    } catch (error) {
      console.error('❌ Erreur export PDF:', error);
      showAlert('❌ Erreur', 'Impossible de générer le PDF. Veuillez réessayer.');
    }
  };



  const getStatusColor = (status: Order['status']) => {
    switch (status) {
      case 'confirmed': return '#3498db';
      case 'preparing': return '#f39c12';
      case 'ready': return '#27ae60';
      case 'delivered': return '#2ecc71';
      default: return '#95a5a6';
    }
  };

  const getStatusText = (status: Order['status']) => {
    switch (status) {
      case 'confirmed': return 'Confirmée';
      case 'preparing': return 'En préparation';
      case 'ready': return 'Prête';
      case 'delivered': return 'Livrée';
      default: return 'En attente';
    }
  };

  // VERSION FINALE FONCTIONNELLE - renderOrderItem
  const renderOrderItem = ({ item }: { item: Order }) => {
    console.log('🔄 Rendu de la commande:', item.id);

    return (
      <View style={styles.orderCard}>
        <View style={styles.orderRow}>
          <View style={styles.orderInfo}>
            <Text style={styles.orderNumber}>Commande #{item.id.slice(-6)}</Text>
            <Text style={styles.orderTotal}>{item.totalAmount.toFixed(2)} DT</Text>
          </View>

          <View style={styles.actionButtonsRow}>
            <TouchableOpacity
              style={[styles.detailsButton, { backgroundColor: '#007bff', padding: 12, minWidth: 80 }]}
              onPress={() => {
                console.log('🔵 CLIC DÉTAILS pour commande:', item.id);
                showOrderDetails(item);
              }}
              activeOpacity={0.7}
            >
              <Text style={[styles.detailsButtonText, { color: 'white', fontSize: 14, fontWeight: 'bold' }]}>
                Détails
              </Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={[styles.cancelButton, { backgroundColor: '#dc3545', padding: 12, minWidth: 80 }]}
              onPress={() => {
                console.log('🔴 CLIC ANNULER pour commande:', item.id);
                cancelOrder(item.id);
              }}
              activeOpacity={0.7}
            >
              <Text style={[styles.cancelButtonText, { color: 'white', fontSize: 14, fontWeight: 'bold' }]}>
                Annuler
              </Text>
            </TouchableOpacity>
          </View>
        </View>
      </View>
    );
  };

  if (isLoading) {
    return (
      <View style={[styles.container, styles.centered]}>
        <Text style={styles.loadingText}>Chargement des statistiques...</Text>
      </View>
    );
  }

  return (
    <View style={styles.container}>
    <ScrollView
      style={styles.scrollView}
      refreshControl={
        <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
      }
    >
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity onPress={() => navigation.goBack()}>
          <Text style={styles.backButton}>← Retour</Text>
        </TouchableOpacity>
        <Text style={styles.title}>Statistiques</Text>
        <TouchableOpacity style={styles.pdfButton} onPress={exportToPDF}>
          <Text style={styles.pdfButtonText}>📄 PDF</Text>
        </TouchableOpacity>
      </View>



      {/* Statistiques du jour actuel */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>📅 Statistiques du Jour</Text>
        <Text style={styles.dateTitle}>
          TOTALE DE JOUR
        </Text>
        <View style={styles.bigStatsContainer}>
          <View style={styles.bigStatCard}>
            <Text style={styles.bigStatNumber}>{recentOrders.length}</Text>
            <Text style={styles.bigStatLabel}>COMMANDES AUJOURD'HUI</Text>
          </View>
          <View style={styles.bigStatCard}>
            <Text style={styles.bigStatNumber}>
              {recentOrders.reduce((total, order) => total + order.totalAmount, 0).toFixed(2)}
            </Text>
            <Text style={styles.bigStatLabel}>DINARS AUJOURD'HUI</Text>
          </View>
        </View>
      </View>

      {/* Statistiques mensuelles */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>📊 Statistiques du Mois</Text>

        {/* Sélecteur de mois */}
        <View style={styles.monthSelector}>
          <Text style={styles.monthLabel}>Sélectionner le mois :</Text>
          <View style={styles.monthButtonsContainer}>
            <TouchableOpacity
              style={[styles.monthButton, selectedMonth === currentMonth && styles.monthButtonActive]}
              onPress={() => handleMonthChange(currentMonth)}
            >
              <Text style={[styles.monthButtonText, selectedMonth === currentMonth && styles.monthButtonTextActive]}>
                Mois Actuel
              </Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={[styles.monthButton, selectedMonth === getPreviousMonth() && styles.monthButtonActive]}
              onPress={() => handleMonthChange(getPreviousMonth())}
            >
              <Text style={[styles.monthButtonText, selectedMonth === getPreviousMonth() && styles.monthButtonTextActive]}>
                Mois Précédent
              </Text>
            </TouchableOpacity>
          </View>

          {/* Sélection manuelle de mois */}
          <TextInput
            style={styles.monthInput}
            value={selectedMonth}
            onChangeText={handleMonthChange}
            placeholder="YYYY-MM (ex: 2024-07)"
            placeholderTextColor="#999"
          />
        </View>

        {/* Affichage du mois sélectionné */}
        <Text style={styles.selectedMonthDisplay}>
          📅 {formatDisplayMonth(selectedMonth)}
        </Text>

        <View style={styles.smallStatsContainer}>
          <View style={styles.smallStatCard}>
            <Text style={styles.smallStatNumber}>{monthlyOrders.length}</Text>
            <Text style={styles.smallStatLabel}>Commandes du Mois</Text>
          </View>
          <View style={styles.smallStatCard}>
            <Text style={styles.smallStatNumber}>
              {monthlyOrders.reduce((total, order) => total + order.totalAmount, 0).toFixed(2)} DT
            </Text>
            <Text style={styles.smallStatLabel}>Dinars du Mois</Text>
          </View>
        </View>
      </View>

      {/* Filtre par date avec boutons rapides */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>📅 Filtrer par date</Text>

        {/* Boutons rapides */}
        <View style={styles.quickDateButtons}>
          <TouchableOpacity
            style={[styles.quickDateButton, selectedDate === today && styles.quickDateButtonActive]}
            onPress={() => handleDateChange(today)}
          >
            <Text style={[styles.quickDateButtonText, selectedDate === today && styles.quickDateButtonTextActive]}>
              Aujourd'hui
            </Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={[styles.quickDateButton, selectedDate === getYesterday() && styles.quickDateButtonActive]}
            onPress={() => handleDateChange(getYesterday())}
          >
            <Text style={[styles.quickDateButtonText, selectedDate === getYesterday() && styles.quickDateButtonTextActive]}>
              Hier
            </Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={[styles.quickDateButton, selectedDate === '' && styles.quickDateButtonActive]}
            onPress={() => handleDateChange('')}
          >
            <Text style={[styles.quickDateButtonText, selectedDate === '' && styles.quickDateButtonTextActive]}>
              Toutes
            </Text>
          </TouchableOpacity>
        </View>

        {/* Sélection manuelle de date */}
        <View style={styles.dateContainer}>
          <Text style={styles.dateLabel}>Ou sélectionner une date précise :</Text>
          <TextInput
            style={styles.dateInput}
            value={selectedDate}
            onChangeText={handleDateChange}
            placeholder="YYYY-MM-DD (ex: 2024-01-15)"
            placeholderTextColor="#999"
          />
        </View>

        {/* Affichage de la date sélectionnée */}
        {selectedDate && (
          <View style={styles.selectedDateDisplay}>
            <Text style={styles.selectedDateText}>
              📅 Affichage des commandes du : {formatDisplayDate(selectedDate)}
            </Text>
          </View>
        )}
      </View>

      {/* Liste des commandes */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>📋 Liste des Commandes ({recentOrders.length})</Text>
        {recentOrders.length > 0 ? (
          <FlatList
            data={recentOrders}
            renderItem={renderOrderItem}
            keyExtractor={(item) => item.id}
            scrollEnabled={false}
          />
        ) : (
          <Text style={styles.noOrdersText}>Aucune commande trouvée pour cette période</Text>
        )}
      </View>
    </ScrollView>
    {ModalComponent}
  </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  scrollView: {
    flex: 1,
  },
  centered: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    fontSize: 16,
    color: '#666',
  },
  errorText: {
    fontSize: 16,
    color: '#e74c3c',
    textAlign: 'center',
    marginBottom: 20,
  },
  retryButton: {
    backgroundColor: '#e74c3c',
    paddingHorizontal: 20,
    paddingVertical: 10,
    borderRadius: 8,
  },
  retryButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
  },
  header: {
    backgroundColor: '#2c3e50',
    paddingTop: 50,
    paddingBottom: 20,
    paddingHorizontal: 16,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  backButton: {
    color: '#3498db',
    fontSize: 16,
    fontWeight: '600',
  },
  title: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#fff',
    flex: 1,
    textAlign: 'center',
  },
  pdfButton: {
    backgroundColor: '#e74c3c',
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 6,
  },
  pdfButtonText: {
    color: '#fff',
    fontSize: 14,
    fontWeight: '600',
  },
  section: {
    backgroundColor: '#fff',
    margin: 16,
    borderRadius: 12,
    padding: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 16,
  },
  bigStatsContainer: {
    flexDirection: 'row',
    gap: 16,
    marginBottom: 16,
  },
  bigStatCard: {
    flex: 1,
    backgroundColor: '#e74c3c',
    borderRadius: 12,
    padding: 24,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.15,
    shadowRadius: 8,
    elevation: 5,
  },
  bigStatNumber: {
    fontSize: 48,
    fontWeight: 'bold',
    color: '#fff',
    marginBottom: 8,
  },
  bigStatLabel: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#fff',
    textAlign: 'center',
    letterSpacing: 1,
  },
  dateTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#2c3e50',
    textAlign: 'center',
    marginTop: 8,
    marginBottom: 12,
    backgroundColor: '#ecf0f1',
    padding: 8,
    borderRadius: 8,
  },
  smallStatsContainer: {
    flexDirection: 'row',
    gap: 8,
    marginTop: 12,
  },
  smallStatCard: {
    flex: 1,
    backgroundColor: '#34495e',
    borderRadius: 8,
    padding: 12,
    alignItems: 'center',
  },
  smallStatNumber: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#fff',
    marginBottom: 4,
  },
  smallStatLabel: {
    fontSize: 10,
    color: '#bdc3c7',
    textAlign: 'center',
    fontWeight: '500',
  },
  statsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 12,
  },
  statCard: {
    flex: 1,
    minWidth: '45%',
    backgroundColor: '#f8f9fa',
    borderRadius: 8,
    padding: 12,
    alignItems: 'center',
  },
  statValue: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#2c3e50',
    marginBottom: 4,
  },
  statLabel: {
    fontSize: 12,
    color: '#666',
    textAlign: 'center',
  },
  orderItem: {
    backgroundColor: '#f8f9fa',
    borderRadius: 8,
    padding: 12,
    marginBottom: 8,
  },
  orderCard: {
    backgroundColor: '#fff',
    marginBottom: 8,
    padding: 16,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#e1e8ed',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 2,
  },
  orderRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  orderInfo: {
    flex: 1,
  },
  orderNumber: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#2c3e50',
    marginBottom: 4,
  },
  actionButtonsRow: {
    flexDirection: 'row',
    gap: 8,
  },
  detailsButton: {
    backgroundColor: '#3498db',
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 6,
  },
  detailsButtonText: {
    color: '#fff',
    fontSize: 14,
    fontWeight: '600',
  },
  cancelButton: {
    backgroundColor: '#e74c3c',
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 6,
  },
  cancelButtonText: {
    color: '#fff',
    fontSize: 14,
    fontWeight: '600',
  },
  orderHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  orderId: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#333',
  },
  statusBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  statusText: {
    fontSize: 12,
    fontWeight: '600',
    color: '#fff',
  },
  customerDetails: {
    backgroundColor: '#f8f9fa',
    padding: 10,
    borderRadius: 6,
    marginBottom: 8,
  },
  customerName: {
    fontSize: 16,
    fontWeight: '600',
    color: '#2c3e50',
    marginBottom: 4,
  },
  customerAddress: {
    fontSize: 14,
    color: '#555',
    marginBottom: 2,
  },
  customerPhone: {
    fontSize: 14,
    color: '#555',
    marginBottom: 2,
  },
  orderDate: {
    fontSize: 12,
    color: '#666',
    marginBottom: 4,
  },
  orderDetails: {
    backgroundColor: '#fff3cd',
    padding: 8,
    borderRadius: 6,
    marginBottom: 8,
    borderLeftWidth: 3,
    borderLeftColor: '#ffc107',
  },
  orderDetailsTitle: {
    fontSize: 14,
    fontWeight: '600',
    color: '#856404',
    marginBottom: 4,
  },
  orderItemDetail: {
    fontSize: 12,
    color: '#856404',
    marginBottom: 2,
  },
  orderTotal: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#27ae60',
  },
  orderActions: {
    flexDirection: 'row',
    gap: 8,
    marginTop: 8,
  },
  confirmButton: {
    flex: 1,
    backgroundColor: '#27ae60',
    borderRadius: 6,
    paddingVertical: 8,
    alignItems: 'center',
  },
  deleteButton: {
    flex: 1,
    backgroundColor: '#e74c3c',
    borderRadius: 6,
    paddingVertical: 8,
    alignItems: 'center',
  },
  actionButtonText: {
    color: '#fff',
    fontSize: 12,
    fontWeight: '600',
  },
  filterContainer: {
    flexDirection: 'row',
    gap: 8,
    marginTop: 8,
  },
  filterButton: {
    flex: 1,
    backgroundColor: '#f8f9fa',
    borderRadius: 8,
    paddingVertical: 10,
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#ddd',
  },
  filterButtonActive: {
    backgroundColor: '#e74c3c',
    borderColor: '#e74c3c',
  },
  filterButtonText: {
    fontSize: 12,
    fontWeight: '600',
    color: '#666',
  },
  filterButtonTextActive: {
    color: '#fff',
  },
  dateContainer: {
    gap: 12,
  },
  dateLabel: {
    fontSize: 16,
    fontWeight: '600',
    color: '#2c3e50',
    marginBottom: 8,
  },
  dateInput: {
    backgroundColor: '#f8f9fa',
    borderRadius: 8,
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderWidth: 1,
    borderColor: '#ddd',
    fontSize: 16,
    color: '#333',
    marginBottom: 12,
  },
  quickDateButtons: {
    flexDirection: 'row',
    gap: 8,
    marginTop: 12,
    marginBottom: 16,
  },
  quickDateButton: {
    flex: 1,
    backgroundColor: '#ecf0f1',
    padding: 12,
    borderRadius: 8,
    alignItems: 'center',
    borderWidth: 2,
    borderColor: 'transparent',
  },
  quickDateButtonActive: {
    backgroundColor: '#3498db',
    borderColor: '#2980b9',
  },
  quickDateButtonText: {
    fontSize: 14,
    fontWeight: '600',
    color: '#2c3e50',
  },
  quickDateButtonTextActive: {
    color: '#fff',
  },
  selectedDateDisplay: {
    marginTop: 12,
    backgroundColor: '#e8f5e8',
    padding: 12,
    borderRadius: 8,
    borderLeftWidth: 4,
    borderLeftColor: '#27ae60',
  },
  selectedDateText: {
    fontSize: 14,
    fontWeight: '600',
    color: '#27ae60',
    textAlign: 'center',
  },
  monthSelector: {
    marginTop: 12,
    marginBottom: 16,
  },
  monthLabel: {
    fontSize: 14,
    fontWeight: '600',
    color: '#2c3e50',
    marginBottom: 8,
  },
  monthButtonsContainer: {
    flexDirection: 'row',
    gap: 8,
    marginBottom: 12,
  },
  monthButton: {
    flex: 1,
    backgroundColor: '#ecf0f1',
    padding: 12,
    borderRadius: 8,
    alignItems: 'center',
    borderWidth: 2,
    borderColor: 'transparent',
  },
  monthButtonActive: {
    backgroundColor: '#9b59b6',
    borderColor: '#8e44ad',
  },
  monthButtonText: {
    fontSize: 14,
    fontWeight: '600',
    color: '#2c3e50',
  },
  monthButtonTextActive: {
    color: '#fff',
  },
  monthInput: {
    backgroundColor: '#f8f9fa',
    borderRadius: 8,
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderWidth: 1,
    borderColor: '#dee2e6',
    fontSize: 16,
    color: '#495057',
    textAlign: 'center',
  },
  selectedMonthDisplay: {
    fontSize: 16,
    fontWeight: '600',
    color: '#9b59b6',
    textAlign: 'center',
    marginBottom: 12,
    backgroundColor: '#f3e5f5',
    padding: 8,
    borderRadius: 8,
  },
  clearDateButton: {
    backgroundColor: '#3498db',
    paddingVertical: 10,
    paddingHorizontal: 16,
    borderRadius: 8,
    alignItems: 'center',
  },
  clearDateText: {
    color: '#fff',
    fontSize: 14,
    fontWeight: '600',
  },
  noOrdersText: {
    textAlign: 'center',
    color: '#666',
    fontStyle: 'italic',
    marginTop: 20,
  },
});
