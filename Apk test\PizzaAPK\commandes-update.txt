========================================
COMMANDES POUR MISE A JOUR EXPO
========================================

📧 Utilisateur: <EMAIL>
🔑 Mot de passe: Themer@10
📱 Version: 1.3.0 - Interface Admin

========================================
ETAPES A SUIVRE:
========================================

1. Ouvrir PowerShell en tant qu'administrateur

2. Naviguer vers le dossier:
   cd "C:\Users\<USER>\Documents\AppCaisse\Apk test\PizzaAPK"

3. Déconnexion (au cas où):
   npx expo logout

4. Connexion à Expo:
   npx expo login
   (Entrer: <EMAIL>)
   (Entrer: Themer@10)

5. Vérifier la connexion:
   npx expo whoami

6. Publier la mise à jour EAS:
   npx eas update --branch main --message "Version 1.3.0 - Interface Admin avec boutons Stock et Promotion"

7. Créer un nouveau build APK:
   npx eas build --platform android --profile production --message "Version 1.3.0 - Interface Admin"

8. Lancer le serveur pour Expo Go:
   npx expo start --tunnel

========================================
RESULTATS ATTENDUS:
========================================

✅ Mise à jour visible sur: https://expo.dev/accounts/brona
✅ Nouveau build APK en cours (10-15 minutes)
✅ QR code pour tester sur Expo Go
✅ Interface "Admin" avec boutons Stock et Promotion

========================================
VERIFICATION:
========================================

📱 Sur Expo Go:
   - Fermer complètement l'app
   - Rouvrir Expo Go
   - Scanner le nouveau QR code
   - Voir l'interface "Admin"

🌐 Sur le site web:
   - Aller sur https://expo.dev/accounts/brona
   - Vérifier la nouvelle mise à jour
   - Télécharger l'APK quand prêt

========================================
