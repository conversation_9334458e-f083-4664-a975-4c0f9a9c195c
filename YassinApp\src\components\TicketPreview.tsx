import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  Modal,
  TouchableOpacity,
  Alert,
} from 'react-native';
import { Colors, FontSizes, Spacing, BorderRadius, Shadows } from '../styles/theme';
import { OrderDetail } from '../types';

interface TicketPreviewProps {
  visible: boolean;
  order: OrderDetail;
  onClose: () => void;
  onPrint: () => void;
}

export const TicketPreview: React.FC<TicketPreviewProps> = ({
  visible,
  order,
  onClose,
  onPrint,
}) => {
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('fr-FR', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
    });
  };

  const formatTime = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleTimeString('fr-FR', {
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  const handlePrint = () => {
    Alert.alert(
      'Imprimer le ticket',
      `Voulez-vous imprimer le ticket de la commande #${order.orderNumber} ?`,
      [
        { text: 'Annuler', style: 'cancel' },
        {
          text: 'Imprimer',
          onPress: () => {
            onPrint();
            Alert.alert('Impression', `Ticket de la commande #${order.orderNumber} envoyé à l'imprimante.`);
          }
        }
      ]
    );
  };

  return (
    <Modal
      visible={visible}
      animationType="slide"
      presentationStyle="pageSheet"
    >
      <View style={styles.container}>
        <View style={styles.header}>
          <Text style={styles.headerTitle}>Aperçu du Ticket</Text>
          <TouchableOpacity onPress={onClose} style={styles.closeButton}>
            <Text style={styles.closeButtonText}>✕</Text>
          </TouchableOpacity>
        </View>

        <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
          <View style={styles.ticket}>
            {/* En-tête du restaurant */}
            <View style={styles.restaurantHeader}>
              <Text style={styles.restaurantName}>MR JOE PIZZA</Text>
              <Text style={styles.restaurantPhone}>+216 46 923 824</Text>
              <Text style={styles.separator}>═══════════════════════════</Text>
            </View>

            {/* Informations commande */}
            <View style={styles.orderInfo}>
              <Text style={styles.orderNumber}>Commande N° {order.orderNumber}</Text>
              <Text style={styles.orderDate}>
                {formatDate(order.date)} à {formatTime(order.date)}
              </Text>
              <Text style={styles.separator}>───────────────────────────</Text>
            </View>

            {/* Articles commandés */}
            <View style={styles.itemsSection}>
              {order.items.map((item, index) => (
                <View key={index} style={styles.itemRow}>
                  <View style={styles.itemInfo}>
                    <Text style={styles.itemName}>{item.name}</Text>
                  </View>
                  <View style={styles.itemPricing}>
                    <Text style={styles.itemQuantity}>{item.quantity}x</Text>
                    <Text style={styles.itemTotal}>{item.totalPrice.toFixed(2)} DT</Text>
                  </View>
                </View>
              ))}
              <Text style={styles.separator}>───────────────────────────</Text>
            </View>

            {/* Totaux */}
            <View style={styles.totalsSection}>
              {order.deliveryFee > 0 && (
                <View style={styles.totalRow}>
                  <Text style={styles.totalLabel}>Frais de livraison:</Text>
                  <Text style={styles.totalValue}>{order.deliveryFee.toFixed(2)} DT</Text>
                </View>
              )}

              <Text style={styles.separator}>═══════════════════════════</Text>
              <View style={styles.finalTotalRow}>
                <Text style={styles.finalTotalLabel}>TOTAL:</Text>
                <Text style={styles.finalTotalValue}>{order.totalAmount.toFixed(2)} DT</Text>
              </View>
            </View>

            {/* Pied de page */}
            <View style={styles.footer}>
              <Text style={styles.separator}>═══════════════════════════</Text>
              <Text style={styles.footerText}>Merci pour votre fidélité !</Text>
              <Text style={styles.footerText}>À bientôt chez MR JOE PIZZA</Text>
            </View>
          </View>
        </ScrollView>

        {/* Boutons d'action */}
        <View style={styles.actions}>
          <TouchableOpacity style={styles.printButton} onPress={handlePrint}>
            <Text style={styles.printButtonText}>🖨️ Imprimer</Text>
          </TouchableOpacity>
          
          <TouchableOpacity style={styles.cancelButton} onPress={onClose}>
            <Text style={styles.cancelButtonText}>Fermer</Text>
          </TouchableOpacity>
        </View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.backgroundSolid,
  },
  header: {
    backgroundColor: Colors.primary,
    paddingTop: 50,
    paddingBottom: 15,
    paddingHorizontal: 16,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    ...Shadows.medium,
  },
  headerTitle: {
    fontSize: FontSizes.lg,
    fontWeight: 'bold',
    color: Colors.textOnPrimary,
  },
  closeButton: {
    width: 32,
    height: 32,
    borderRadius: BorderRadius.round,
    backgroundColor: Colors.textOnPrimary,
    justifyContent: 'center',
    alignItems: 'center',
  },
  closeButtonText: {
    fontSize: FontSizes.lg,
    color: Colors.primary,
    fontWeight: 'bold',
  },
  content: {
    flex: 1,
    padding: Spacing.lg,
  },
  ticket: {
    backgroundColor: Colors.surface,
    borderRadius: BorderRadius.medium,
    padding: Spacing.lg,
    ...Shadows.small,
  },
  restaurantHeader: {
    alignItems: 'center',
    marginBottom: Spacing.lg,
  },
  restaurantName: {
    fontSize: FontSizes.xl,
    fontWeight: 'bold',
    color: Colors.primary,
    marginBottom: Spacing.xs,
  },
  restaurantPhone: {
    fontSize: FontSizes.md,
    color: Colors.textSecondary,
    marginBottom: Spacing.xs,
  },
  separator: {
    fontSize: FontSizes.sm,
    color: Colors.textSecondary,
    textAlign: 'center',
    marginVertical: Spacing.sm,
    fontFamily: 'monospace',
  },
  orderInfo: {
    alignItems: 'center',
    marginBottom: Spacing.lg,
  },

  orderNumber: {
    fontSize: FontSizes.md,
    fontWeight: 'bold',
    color: Colors.primary,
    marginBottom: Spacing.xs,
  },
  orderDate: {
    fontSize: FontSizes.sm,
    color: Colors.textSecondary,
  },

  itemsSection: {
    marginBottom: Spacing.lg,
  },
  itemsTitle: {
    fontSize: FontSizes.sm,
    fontWeight: 'bold',
    color: Colors.textPrimary,
    marginBottom: Spacing.sm,
  },
  itemRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: Spacing.sm,
  },
  itemInfo: {
    flex: 1,
    marginRight: Spacing.md,
  },
  itemName: {
    fontSize: FontSizes.sm,
    fontWeight: '600',
    color: Colors.textPrimary,
    marginBottom: 2,
  },

  itemPricing: {
    alignItems: 'flex-end',
  },
  itemQuantity: {
    fontSize: FontSizes.xs,
    color: Colors.textSecondary,
  },
  itemTotal: {
    fontSize: FontSizes.sm,
    fontWeight: 'bold',
    color: Colors.textPrimary,
  },
  totalsSection: {
    marginBottom: Spacing.lg,
  },
  totalRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: Spacing.xs,
  },
  totalLabel: {
    fontSize: FontSizes.sm,
    color: Colors.textSecondary,
  },
  totalValue: {
    fontSize: FontSizes.sm,
    fontWeight: '600',
    color: Colors.textPrimary,
  },
  finalTotalRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: Spacing.sm,
  },
  finalTotalLabel: {
    fontSize: FontSizes.md,
    fontWeight: 'bold',
    color: Colors.textPrimary,
  },
  finalTotalValue: {
    fontSize: FontSizes.lg,
    fontWeight: 'bold',
    color: Colors.primary,
  },

  footer: {
    alignItems: 'center',
  },
  footerText: {
    fontSize: FontSizes.sm,
    color: Colors.textSecondary,
    textAlign: 'center',
    marginBottom: 2,
  },
  actions: {
    flexDirection: 'row',
    padding: Spacing.lg,
    gap: Spacing.md,
    backgroundColor: Colors.surface,
    borderTopWidth: 1,
    borderTopColor: Colors.borderLight,
  },
  printButton: {
    flex: 1,
    backgroundColor: Colors.primary,
    borderRadius: BorderRadius.large,
    padding: Spacing.md,
    alignItems: 'center',
    ...Shadows.medium,
  },
  printButtonText: {
    fontSize: FontSizes.md,
    fontWeight: 'bold',
    color: Colors.textOnPrimary,
  },
  cancelButton: {
    flex: 1,
    backgroundColor: Colors.light,
    borderRadius: BorderRadius.large,
    padding: Spacing.md,
    alignItems: 'center',
    ...Shadows.small,
  },
  cancelButtonText: {
    fontSize: FontSizes.md,
    fontWeight: '600',
    color: Colors.textPrimary,
  },
});
