import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  TextInput,
  Alert,
  Switch,
} from 'react-native';
import { Colors, FontSizes, Spacing, BorderRadius, Shadows } from '../styles/theme';

interface PromotionScreenProps {
  navigation: any;
}

interface Promotion {
  id: string;
  name: string;
  description: string;
  type: 'percentage' | 'fixed' | 'buy_get';
  value: number;
  minAmount?: number;
  validFrom: string;
  validTo: string;
  isActive: boolean;
  usageCount: number;
  maxUsage?: number;
  code?: string;
}

export const PromotionScreen: React.FC<PromotionScreenProps> = ({ navigation }) => {
  const [promotions, setPromotions] = useState<Promotion[]>([]);
  const [showCreateForm, setShowCreateForm] = useState(false);
  const [newPromotion, setNewPromotion] = useState<Partial<Promotion>>({
    name: '',
    description: '',
    type: 'percentage',
    value: 0,
    minAmount: 0,
    validFrom: new Date().toISOString().split('T')[0],
    validTo: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
    isActive: true,
    code: '',
  });

  useEffect(() => {
    loadPromotions();
  }, []);

  const loadPromotions = () => {
    // Mock promotions data
    const mockPromotions: Promotion[] = [
      {
        id: '1',
        name: 'Réduction 20%',
        description: '20% de réduction sur toute commande',
        type: 'percentage',
        value: 20,
        minAmount: 25,
        validFrom: '2025-07-01',
        validTo: '2025-07-31',
        isActive: true,
        usageCount: 45,
        maxUsage: 100,
        code: 'SUMMER20',
      },
      {
        id: '2',
        name: 'Pizza gratuite',
        description: 'Achetez 2 pizzas, obtenez la 3ème gratuite',
        type: 'buy_get',
        value: 1,
        validFrom: '2025-07-10',
        validTo: '2025-07-20',
        isActive: true,
        usageCount: 12,
        code: 'PIZZA3',
      },
      {
        id: '3',
        name: '5 DT de réduction',
        description: '5 DT de réduction sur commandes de plus de 30 DT',
        type: 'fixed',
        value: 5,
        minAmount: 30,
        validFrom: '2025-07-05',
        validTo: '2025-07-25',
        isActive: false,
        usageCount: 8,
        code: 'SAVE5',
      },
    ];

    setPromotions(mockPromotions);
  };

  const togglePromotionStatus = (id: string) => {
    setPromotions(promotions.map(promo => 
      promo.id === id ? { ...promo, isActive: !promo.isActive } : promo
    ));
  };

  const deletePromotion = (id: string) => {
    Alert.alert(
      'Supprimer la promotion',
      'Êtes-vous sûr de vouloir supprimer cette promotion ?',
      [
        { text: 'Annuler', style: 'cancel' },
        {
          text: 'Supprimer',
          style: 'destructive',
          onPress: () => {
            setPromotions(promotions.filter(promo => promo.id !== id));
          }
        }
      ]
    );
  };

  const createPromotion = () => {
    if (!newPromotion.name || !newPromotion.description || !newPromotion.value) {
      Alert.alert('Erreur', 'Veuillez remplir tous les champs obligatoires');
      return;
    }

    const promotion: Promotion = {
      id: Date.now().toString(),
      name: newPromotion.name!,
      description: newPromotion.description!,
      type: newPromotion.type!,
      value: newPromotion.value!,
      minAmount: newPromotion.minAmount,
      validFrom: newPromotion.validFrom!,
      validTo: newPromotion.validTo!,
      isActive: newPromotion.isActive!,
      usageCount: 0,
      code: newPromotion.code || `PROMO${Date.now().toString().slice(-4)}`,
    };

    setPromotions([...promotions, promotion]);
    setShowCreateForm(false);
    setNewPromotion({
      name: '',
      description: '',
      type: 'percentage',
      value: 0,
      minAmount: 0,
      validFrom: new Date().toISOString().split('T')[0],
      validTo: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
      isActive: true,
      code: '',
    });

    Alert.alert('Succès', 'Promotion créée avec succès');
  };

  const getPromotionTypeLabel = (type: string) => {
    switch (type) {
      case 'percentage': return 'Pourcentage';
      case 'fixed': return 'Montant fixe';
      case 'buy_get': return 'Achat groupé';
      default: return type;
    }
  };

  const getPromotionValueDisplay = (promo: Promotion) => {
    switch (promo.type) {
      case 'percentage': return `${promo.value}%`;
      case 'fixed': return `${promo.value} DT`;
      case 'buy_get': return `+${promo.value} gratuit`;
      default: return promo.value.toString();
    }
  };

  const PromotionCard = ({ promotion }: { promotion: Promotion }) => (
    <View style={[styles.promotionCard, !promotion.isActive && styles.inactiveCard]}>
      <View style={styles.promotionHeader}>
        <View style={styles.promotionInfo}>
          <Text style={styles.promotionName}>{promotion.name}</Text>
          <Text style={styles.promotionCode}>Code: {promotion.code}</Text>
        </View>
        <View style={styles.promotionActions}>
          <Switch
            value={promotion.isActive}
            onValueChange={() => togglePromotionStatus(promotion.id)}
            trackColor={{ false: Colors.lightGray, true: Colors.success }}
            thumbColor={promotion.isActive ? Colors.white : Colors.gray}
          />
          <TouchableOpacity onPress={() => deletePromotion(promotion.id)}>
            <Text style={styles.deleteButton}>🗑️</Text>
          </TouchableOpacity>
        </View>
      </View>
      
      <Text style={styles.promotionDescription}>{promotion.description}</Text>
      
      <View style={styles.promotionDetails}>
        <View style={styles.detailItem}>
          <Text style={styles.detailLabel}>Type:</Text>
          <Text style={styles.detailValue}>{getPromotionTypeLabel(promotion.type)}</Text>
        </View>
        
        <View style={styles.detailItem}>
          <Text style={styles.detailLabel}>Valeur:</Text>
          <Text style={styles.detailValue}>{getPromotionValueDisplay(promotion)}</Text>
        </View>
        
        {promotion.minAmount && (
          <View style={styles.detailItem}>
            <Text style={styles.detailLabel}>Minimum:</Text>
            <Text style={styles.detailValue}>{promotion.minAmount} DT</Text>
          </View>
        )}
      </View>
      
      <View style={styles.promotionFooter}>
        <Text style={styles.validityText}>
          Valide du {promotion.validFrom} au {promotion.validTo}
        </Text>
        <Text style={styles.usageText}>
          Utilisé: {promotion.usageCount}{promotion.maxUsage ? `/${promotion.maxUsage}` : ''}
        </Text>
      </View>
    </View>
  );

  if (showCreateForm) {
    return (
      <View style={styles.container}>
        <View style={styles.header}>
          <TouchableOpacity onPress={() => setShowCreateForm(false)} style={styles.backButton}>
            <Text style={styles.backButtonText}>← Annuler</Text>
          </TouchableOpacity>
          <Text style={styles.title}>Nouvelle Promotion</Text>
          <TouchableOpacity style={styles.createButton} onPress={createPromotion}>
            <Text style={styles.createButtonText}>Créer</Text>
          </TouchableOpacity>
        </View>

        <ScrollView style={styles.formContainer}>
          <View style={styles.inputGroup}>
            <Text style={styles.label}>Nom de la promotion*</Text>
            <TextInput
              style={styles.input}
              value={newPromotion.name}
              onChangeText={(text) => setNewPromotion({...newPromotion, name: text})}
              placeholder="Ex: Réduction été"
            />
          </View>

          <View style={styles.inputGroup}>
            <Text style={styles.label}>Description*</Text>
            <TextInput
              style={[styles.input, styles.textArea]}
              value={newPromotion.description}
              onChangeText={(text) => setNewPromotion({...newPromotion, description: text})}
              placeholder="Description de la promotion"
              multiline
              numberOfLines={3}
            />
          </View>

          <View style={styles.inputGroup}>
            <Text style={styles.label}>Type de promotion</Text>
            <View style={styles.typeButtons}>
              {[
                { key: 'percentage', label: 'Pourcentage' },
                { key: 'fixed', label: 'Montant fixe' },
                { key: 'buy_get', label: 'Achat groupé' }
              ].map(type => (
                <TouchableOpacity
                  key={type.key}
                  style={[
                    styles.typeButton,
                    newPromotion.type === type.key && styles.typeButtonActive
                  ]}
                  onPress={() => setNewPromotion({...newPromotion, type: type.key as any})}
                >
                  <Text
                    style={[
                      styles.typeButtonText,
                      newPromotion.type === type.key && styles.typeButtonTextActive
                    ]}
                  >
                    {type.label}
                  </Text>
                </TouchableOpacity>
              ))}
            </View>
          </View>

          <View style={styles.inputGroup}>
            <Text style={styles.label}>
              Valeur* {newPromotion.type === 'percentage' ? '(%)' : newPromotion.type === 'fixed' ? '(DT)' : '(quantité)'}
            </Text>
            <TextInput
              style={styles.input}
              value={newPromotion.value?.toString()}
              onChangeText={(text) => setNewPromotion({...newPromotion, value: parseFloat(text) || 0})}
              placeholder="0"
              keyboardType="numeric"
            />
          </View>

          <View style={styles.inputGroup}>
            <Text style={styles.label}>Montant minimum (DT)</Text>
            <TextInput
              style={styles.input}
              value={newPromotion.minAmount?.toString()}
              onChangeText={(text) => setNewPromotion({...newPromotion, minAmount: parseFloat(text) || 0})}
              placeholder="0"
              keyboardType="numeric"
            />
          </View>

          <View style={styles.inputGroup}>
            <Text style={styles.label}>Code promo</Text>
            <TextInput
              style={styles.input}
              value={newPromotion.code}
              onChangeText={(text) => setNewPromotion({...newPromotion, code: text.toUpperCase()})}
              placeholder="Laissez vide pour génération automatique"
              autoCapitalize="characters"
            />
          </View>

          <View style={styles.dateRow}>
            <View style={styles.dateGroup}>
              <Text style={styles.label}>Date de début</Text>
              <TextInput
                style={styles.input}
                value={newPromotion.validFrom}
                onChangeText={(text) => setNewPromotion({...newPromotion, validFrom: text})}
                placeholder="YYYY-MM-DD"
              />
            </View>
            
            <View style={styles.dateGroup}>
              <Text style={styles.label}>Date de fin</Text>
              <TextInput
                style={styles.input}
                value={newPromotion.validTo}
                onChangeText={(text) => setNewPromotion({...newPromotion, validTo: text})}
                placeholder="YYYY-MM-DD"
              />
            </View>
          </View>

          <View style={styles.switchGroup}>
            <Text style={styles.label}>Activer immédiatement</Text>
            <Switch
              value={newPromotion.isActive}
              onValueChange={(value) => setNewPromotion({...newPromotion, isActive: value})}
              trackColor={{ false: Colors.lightGray, true: Colors.success }}
              thumbColor={newPromotion.isActive ? Colors.white : Colors.gray}
            />
          </View>
        </ScrollView>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity onPress={() => navigation.goBack()} style={styles.backButton}>
          <Text style={styles.backButtonText}>← Retour</Text>
        </TouchableOpacity>
        <Text style={styles.title}>Promotions</Text>
        <TouchableOpacity style={styles.addButton} onPress={() => setShowCreateForm(true)}>
          <Text style={styles.addButtonText}>+ Ajouter</Text>
        </TouchableOpacity>
      </View>

      <View style={styles.statsContainer}>
        <View style={styles.statItem}>
          <Text style={styles.statValue}>{promotions.filter(p => p.isActive).length}</Text>
          <Text style={styles.statLabel}>Actives</Text>
        </View>
        <View style={styles.statItem}>
          <Text style={styles.statValue}>{promotions.reduce((sum, p) => sum + p.usageCount, 0)}</Text>
          <Text style={styles.statLabel}>Utilisations</Text>
        </View>
        <View style={styles.statItem}>
          <Text style={styles.statValue}>{promotions.length}</Text>
          <Text style={styles.statLabel}>Total</Text>
        </View>
      </View>

      <ScrollView style={styles.content}>
        {promotions.length === 0 ? (
          <View style={styles.emptyState}>
            <Text style={styles.emptyStateText}>🎯</Text>
            <Text style={styles.emptyStateTitle}>Aucune promotion</Text>
            <Text style={styles.emptyStateSubtitle}>Créez votre première promotion pour attirer plus de clients</Text>
          </View>
        ) : (
          promotions.map(promotion => (
            <PromotionCard key={promotion.id} promotion={promotion} />
          ))
        )}
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
  },
  header: {
    backgroundColor: Colors.secondary,
    paddingTop: 50,
    paddingBottom: 15,
    paddingHorizontal: 16,
    flexDirection: 'row',
    alignItems: 'center',
  },
  backButton: {
    marginRight: 10,
  },
  backButtonText: {
    color: Colors.textOnDark,
    fontSize: FontSizes.md,
    fontWeight: '600',
  },
  title: {
    fontSize: FontSizes.xl,
    fontWeight: 'bold',
    color: Colors.textOnDark,
    flex: 1,
  },
  addButton: {
    backgroundColor: Colors.success,
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: BorderRadius.medium,
  },
  addButtonText: {
    color: Colors.textOnPrimary,
    fontSize: FontSizes.sm,
    fontWeight: '600',
  },
  createButton: {
    backgroundColor: Colors.success,
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: BorderRadius.medium,
  },
  createButtonText: {
    color: Colors.textOnPrimary,
    fontSize: FontSizes.sm,
    fontWeight: '600',
  },
  statsContainer: {
    flexDirection: 'row',
    backgroundColor: Colors.surface,
    paddingVertical: Spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: Colors.border,
  },
  statItem: {
    flex: 1,
    alignItems: 'center',
  },
  statValue: {
    fontSize: FontSizes.xl,
    fontWeight: 'bold',
    color: Colors.primary,
  },
  statLabel: {
    fontSize: FontSizes.sm,
    color: Colors.textSecondary,
    marginTop: 2,
  },
  content: {
    flex: 1,
    padding: Spacing.md,
  },
  promotionCard: {
    backgroundColor: Colors.surface,
    borderRadius: BorderRadius.large,
    padding: Spacing.md,
    marginBottom: Spacing.md,
    ...Shadows.small,
  },
  inactiveCard: {
    opacity: 0.6,
  },
  promotionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: Spacing.sm,
  },
  promotionInfo: {
    flex: 1,
  },
  promotionName: {
    fontSize: FontSizes.lg,
    fontWeight: 'bold',
    color: Colors.textPrimary,
  },
  promotionCode: {
    fontSize: FontSizes.sm,
    color: Colors.textSecondary,
    fontFamily: 'monospace',
  },
  promotionActions: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  deleteButton: {
    fontSize: FontSizes.lg,
    marginLeft: Spacing.sm,
    padding: Spacing.xs,
  },
  promotionDescription: {
    fontSize: FontSizes.md,
    color: Colors.textSecondary,
    marginBottom: Spacing.md,
  },
  promotionDetails: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginBottom: Spacing.md,
  },
  detailItem: {
    flexDirection: 'row',
    marginRight: Spacing.lg,
    marginBottom: Spacing.xs,
  },
  detailLabel: {
    fontSize: FontSizes.sm,
    color: Colors.textSecondary,
    marginRight: Spacing.xs,
  },
  detailValue: {
    fontSize: FontSizes.sm,
    fontWeight: '600',
    color: Colors.textPrimary,
  },
  promotionFooter: {
    borderTopWidth: 1,
    borderTopColor: Colors.border,
    paddingTop: Spacing.sm,
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  validityText: {
    fontSize: FontSizes.xs,
    color: Colors.textSecondary,
  },
  usageText: {
    fontSize: FontSizes.xs,
    color: Colors.textSecondary,
  },
  emptyState: {
    alignItems: 'center',
    paddingVertical: Spacing.xxl,
  },
  emptyStateText: {
    fontSize: 60,
    marginBottom: Spacing.md,
  },
  emptyStateTitle: {
    fontSize: FontSizes.xl,
    fontWeight: 'bold',
    color: Colors.textPrimary,
    marginBottom: Spacing.xs,
  },
  emptyStateSubtitle: {
    fontSize: FontSizes.md,
    color: Colors.textSecondary,
    textAlign: 'center',
  },
  formContainer: {
    flex: 1,
    padding: Spacing.md,
  },
  inputGroup: {
    marginBottom: Spacing.lg,
  },
  label: {
    fontSize: FontSizes.sm,
    color: Colors.textSecondary,
    marginBottom: Spacing.xs,
    fontWeight: '600',
  },
  input: {
    backgroundColor: Colors.light,
    borderRadius: BorderRadius.medium,
    paddingHorizontal: Spacing.md,
    paddingVertical: Spacing.sm,
    fontSize: FontSizes.md,
    color: Colors.textPrimary,
    borderWidth: 1,
    borderColor: Colors.border,
  },
  textArea: {
    minHeight: 80,
    textAlignVertical: 'top',
  },
  typeButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  typeButton: {
    flex: 1,
    padding: Spacing.md,
    borderRadius: BorderRadius.medium,
    backgroundColor: Colors.light,
    marginHorizontal: 2,
    alignItems: 'center',
    borderWidth: 1,
    borderColor: Colors.border,
  },
  typeButtonActive: {
    backgroundColor: Colors.primary,
    borderColor: Colors.primary,
  },
  typeButtonText: {
    fontSize: FontSizes.sm,
    fontWeight: '600',
    color: Colors.textPrimary,
  },
  typeButtonTextActive: {
    color: Colors.textOnPrimary,
  },
  dateRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  dateGroup: {
    flex: 1,
    marginHorizontal: 4,
  },
  switchGroup: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: Spacing.md,
  },
});
