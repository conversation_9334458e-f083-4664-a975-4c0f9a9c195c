export interface Pizza {
  id: string;
  name: string;
  description: string;
  basePrice: number;
  image: string;
  category: string;
  ingredients: string[];
  sizes: PizzaSize[];
}

export interface PizzaSize {
  name: string;
  multiplier: number;
}

export interface OfferItem {
  type: 'pizza' | 'drink' | 'side' | 'dessert';
  name: string;
  quantity: number;
  choices: string[];
  selectedChoices?: string[];
}

export interface Offer {
  id: string;
  name: string;
  description: string;
  price: number;
  image: string;
  category: string;
  items: OfferItem[];
  isActive: boolean;
}

export interface OrderItem {
  id: string;
  name: string;
  quantity: number;
  unitPrice: number;
  totalPrice: number;
  specialInstructions?: string;
  isOffer?: boolean;
  offerItems?: {
    name: string;
    choice: string;
  }[];
}

export interface Customer {
  name: string;
  phone: string;
  address: string;
}

export interface Order {
  id: string;
  orderNumber: string;
  customer: Customer;
  items: OrderItem[];
  subtotalAmount: number;
  deliveryFee: number;
  totalAmount: number;
  status: 'En préparation' | 'Prête' | 'Livrée' | 'Annulée';
  date: string;
  paymentMethod?: string;
  deliveryTime?: string;
  specialInstructions?: string;
}

export interface Statistics {
  totalOrders: number;
  totalRevenue: number;
  averageOrderValue: number;
  todayOrders: number;
  monthlyRevenue: number;
  popularPizzas: { name: string; count: number }[];
}

export interface DailyStats {
  date: string;
  orders: Order[];
  totalOrders: number;
  totalRevenue: number;
  averageOrderValue: number;
}

export interface OrderDetail extends Order {
  formattedDate: string;
  formattedTime: string;
}
