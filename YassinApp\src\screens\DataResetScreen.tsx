import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Alert,
  ScrollView,
} from 'react-native';
import { Colors, FontSizes, Spacing, BorderRadius } from '../styles/theme';
import { DataResetService } from '../services/DataResetService';

interface DataResetScreenProps {
  navigation: any;
}

export const DataResetScreen: React.FC<DataResetScreenProps> = ({ navigation }) => {
  const [dataStatus, setDataStatus] = useState<{[key: string]: boolean}>({});
  const [loading, setLoading] = useState<boolean>(false);

  useEffect(() => {
    checkData();
  }, []);

  const checkData = async () => {
    try {
      const status = await DataResetService.checkExistingData();
      setDataStatus(status);
    } catch (error) {
      console.error('Erreur lors de la vérification des données:', error);
    }
  };

  const handleClearAllData = () => {
    Alert.alert(
      '🗑️ Vider Toutes les Données',
      'ATTENTION: Cette action va supprimer TOUTES les données de l\'application (commandes, stock, statistiques, etc.). Cette action est IRRÉVERSIBLE.\n\nÊtes-vous absolument sûr ?',
      [
        { text: 'Annuler', style: 'cancel' },
        {
          text: 'VIDER TOUT',
          style: 'destructive',
          onPress: async () => {
            try {
              setLoading(true);
              await DataResetService.clearAllData();
              await checkData();
              Alert.alert(
                'Succès',
                '✅ Toutes les données ont été supprimées. L\'application est maintenant vide et prête pour un nouveau test.',
                [{ text: 'OK' }]
              );
            } catch (error) {
              Alert.alert('Erreur', 'Impossible de vider les données');
            } finally {
              setLoading(false);
            }
          }
        }
      ]
    );
  };

  const handleClearOrders = () => {
    Alert.alert(
      '🗑️ Vider les Commandes',
      'Supprimer toutes les commandes et remettre le compteur à zéro ?',
      [
        { text: 'Annuler', style: 'cancel' },
        {
          text: 'Supprimer',
          style: 'destructive',
          onPress: async () => {
            try {
              setLoading(true);
              await DataResetService.clearOrders();
              await checkData();
              Alert.alert('Succès', 'Commandes supprimées');
            } catch (error) {
              Alert.alert('Erreur', 'Impossible de supprimer les commandes');
            } finally {
              setLoading(false);
            }
          }
        }
      ]
    );
  };

  const handleClearStock = () => {
    Alert.alert(
      '🗑️ Vider le Stock',
      'Supprimer toutes les données de stock ?',
      [
        { text: 'Annuler', style: 'cancel' },
        {
          text: 'Supprimer',
          style: 'destructive',
          onPress: async () => {
            try {
              setLoading(true);
              await DataResetService.clearStock();
              await checkData();
              Alert.alert('Succès', 'Stock supprimé');
            } catch (error) {
              Alert.alert('Erreur', 'Impossible de supprimer le stock');
            } finally {
              setLoading(false);
            }
          }
        }
      ]
    );
  };

  const handleClearStatistics = () => {
    Alert.alert(
      '🗑️ Vider les Statistiques',
      'Supprimer toutes les statistiques ?',
      [
        { text: 'Annuler', style: 'cancel' },
        {
          text: 'Supprimer',
          style: 'destructive',
          onPress: async () => {
            try {
              setLoading(true);
              await DataResetService.clearStatistics();
              await checkData();
              Alert.alert('Succès', 'Statistiques supprimées');
            } catch (error) {
              Alert.alert('Erreur', 'Impossible de supprimer les statistiques');
            } finally {
              setLoading(false);
            }
          }
        }
      ]
    );
  };

  const handleResetToDefault = () => {
    Alert.alert(
      '🔄 Réinitialisation Complète',
      'Remettre l\'application dans son état initial avec des données par défaut ?',
      [
        { text: 'Annuler', style: 'cancel' },
        {
          text: 'Réinitialiser',
          onPress: async () => {
            try {
              setLoading(true);
              await DataResetService.resetToDefault();
              await checkData();
              Alert.alert(
                'Succès',
                '✅ Application réinitialisée. Redémarrez l\'application pour voir les changements.',
                [{ text: 'OK' }]
              );
            } catch (error) {
              Alert.alert('Erreur', 'Impossible de réinitialiser');
            } finally {
              setLoading(false);
            }
          }
        }
      ]
    );
  };

  const generateReport = async () => {
    try {
      setLoading(true);
      const report = await DataResetService.generateCleanupReport();
      Alert.alert('Rapport de Nettoyage', report, [{ text: 'OK' }]);
    } catch (error) {
      Alert.alert('Erreur', 'Impossible de générer le rapport');
    } finally {
      setLoading(false);
    }
  };

  return (
    <View style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity onPress={() => navigation.goBack()} style={styles.backButton}>
          <Text style={styles.backButtonText}>← Retour</Text>
        </TouchableOpacity>
        <Text style={styles.title}>Nettoyage des Données</Text>
        <TouchableOpacity style={styles.refreshButton} onPress={checkData}>
          <Text style={styles.refreshButtonText}>🔄</Text>
        </TouchableOpacity>
      </View>

      <ScrollView style={styles.content}>
        {/* État des données */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>📊 État Actuel des Données</Text>
          {Object.entries(dataStatus).map(([key, exists]) => (
            <View key={key} style={styles.dataRow}>
              <Text style={styles.dataKey}>{key}</Text>
              <Text style={[styles.dataStatus, exists ? styles.dataExists : styles.dataEmpty]}>
                {exists ? '✅ Données présentes' : '❌ Vide'}
              </Text>
            </View>
          ))}
        </View>

        {/* Actions de nettoyage */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>🗑️ Actions de Nettoyage</Text>
          
          <TouchableOpacity 
            style={[styles.actionButton, styles.dangerButton]} 
            onPress={handleClearAllData}
            disabled={loading}
          >
            <Text style={styles.actionButtonText}>🗑️ VIDER TOUTES LES DONNÉES</Text>
            <Text style={styles.actionButtonSubtext}>Supprime tout (commandes, stock, stats)</Text>
          </TouchableOpacity>

          <TouchableOpacity 
            style={[styles.actionButton, styles.warningButton]} 
            onPress={handleClearOrders}
            disabled={loading}
          >
            <Text style={styles.actionButtonText}>📋 Vider les Commandes</Text>
            <Text style={styles.actionButtonSubtext}>Supprime toutes les commandes</Text>
          </TouchableOpacity>

          <TouchableOpacity 
            style={[styles.actionButton, styles.warningButton]} 
            onPress={handleClearStock}
            disabled={loading}
          >
            <Text style={styles.actionButtonText}>📦 Vider le Stock</Text>
            <Text style={styles.actionButtonSubtext}>Supprime toutes les données de stock</Text>
          </TouchableOpacity>

          <TouchableOpacity 
            style={[styles.actionButton, styles.warningButton]} 
            onPress={handleClearStatistics}
            disabled={loading}
          >
            <Text style={styles.actionButtonText}>📈 Vider les Statistiques</Text>
            <Text style={styles.actionButtonSubtext}>Supprime toutes les statistiques</Text>
          </TouchableOpacity>

          <TouchableOpacity 
            style={[styles.actionButton, styles.primaryButton]} 
            onPress={handleResetToDefault}
            disabled={loading}
          >
            <Text style={styles.actionButtonText}>🔄 Réinitialisation Complète</Text>
            <Text style={styles.actionButtonSubtext}>Remet l'app à l'état initial</Text>
          </TouchableOpacity>

          <TouchableOpacity 
            style={[styles.actionButton, styles.infoButton]} 
            onPress={generateReport}
            disabled={loading}
          >
            <Text style={styles.actionButtonText}>📋 Générer un Rapport</Text>
            <Text style={styles.actionButtonSubtext}>Rapport de nettoyage détaillé</Text>
          </TouchableOpacity>
        </View>

        {/* Avertissement */}
        <View style={styles.warningSection}>
          <Text style={styles.warningTitle}>⚠️ ATTENTION</Text>
          <Text style={styles.warningText}>
            • Les suppressions sont IRRÉVERSIBLES{'\n'}
            • Sauvegardez vos données importantes avant{'\n'}
            • Redémarrez l'application après nettoyage{'\n'}
            • Utilisez pour les tests uniquement
          </Text>
        </View>
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
  },
  header: {
    backgroundColor: Colors.danger,
    paddingTop: 50,
    paddingBottom: 15,
    paddingHorizontal: 16,
    flexDirection: 'row',
    alignItems: 'center',
  },
  backButton: {
    marginRight: 10,
  },
  backButtonText: {
    color: Colors.textOnDark,
    fontSize: FontSizes.md,
    fontWeight: '600',
  },
  title: {
    fontSize: FontSizes.xl,
    fontWeight: 'bold',
    color: Colors.textOnDark,
    flex: 1,
  },
  refreshButton: {
    backgroundColor: Colors.light,
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: BorderRadius.medium,
  },
  refreshButtonText: {
    fontSize: FontSizes.lg,
  },
  content: {
    flex: 1,
    padding: Spacing.md,
  },
  section: {
    backgroundColor: Colors.surface,
    borderRadius: BorderRadius.medium,
    padding: Spacing.md,
    marginBottom: Spacing.md,
  },
  sectionTitle: {
    fontSize: FontSizes.lg,
    fontWeight: 'bold',
    color: Colors.textPrimary,
    marginBottom: Spacing.md,
  },
  dataRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: Spacing.sm,
    borderBottomWidth: 1,
    borderBottomColor: Colors.border,
  },
  dataKey: {
    fontSize: FontSizes.sm,
    color: Colors.textPrimary,
    flex: 1,
  },
  dataStatus: {
    fontSize: FontSizes.sm,
    fontWeight: '600',
  },
  dataExists: {
    color: Colors.success,
  },
  dataEmpty: {
    color: Colors.textSecondary,
  },
  actionButton: {
    padding: Spacing.md,
    borderRadius: BorderRadius.medium,
    marginBottom: Spacing.sm,
    alignItems: 'center',
  },
  dangerButton: {
    backgroundColor: Colors.danger,
  },
  warningButton: {
    backgroundColor: Colors.warning,
  },
  primaryButton: {
    backgroundColor: Colors.primary,
  },
  infoButton: {
    backgroundColor: Colors.accent,
  },
  actionButtonText: {
    color: Colors.textOnPrimary,
    fontSize: FontSizes.md,
    fontWeight: 'bold',
    marginBottom: Spacing.xs,
  },
  actionButtonSubtext: {
    color: Colors.textOnPrimary,
    fontSize: FontSizes.sm,
    opacity: 0.9,
  },
  warningSection: {
    backgroundColor: Colors.warning,
    borderRadius: BorderRadius.medium,
    padding: Spacing.md,
    marginTop: Spacing.md,
  },
  warningTitle: {
    fontSize: FontSizes.lg,
    fontWeight: 'bold',
    color: Colors.textOnPrimary,
    marginBottom: Spacing.sm,
  },
  warningText: {
    fontSize: FontSizes.sm,
    color: Colors.textOnPrimary,
    lineHeight: 20,
  },
});
