# 🗑️ GUIDE DE NETTOYAGE ET TEST - YASSINAPP

## 🎯 Vider Toutes les Données pour Test Complet

Ce guide vous permet de vider complètement toutes les données de l'application pour faire un test depuis zéro et vérifier que tout fonctionne correctement.

## 📱 Accès au Nettoyage

### 🚀 Lancement de l'Application

```cmd
cd "C:\Users\<USER>\Documents\AppCaisse\YassinApp"
npx expo start
```

### 🔑 Navigation vers le Nettoyage

1. **Connexion** : admin / 123
2. **Écran principal** → **"🗑️ Nettoyage"**

## 🗑️ Interface de Nettoyage

```
┌─────────────────────────────────────┐
│ [← Retour] Nettoyage des Données [🔄]│
├─────────────────────────────────────┤
│ 📊 État Actuel des Données          │
│ orders              ✅ Données présentes│
│ stock_data          ✅ Données présentes│
│ statistics          ✅ Données présentes│
│ daily_stats         ❌ Vide           │
├─────────────────────────────────────┤
│ 🗑️ Actions de Nettoyage             │
│                                     │
│ [🗑️ VIDER TOUTES LES DONNÉES]       │
│ Supprime tout (commandes, stock, stats)│
│                                     │
│ [📋 Vider les Commandes]            │
│ Supprime toutes les commandes       │
│                                     │
│ [📦 Vider le Stock]                 │
│ Supprime toutes les données de stock│
│                                     │
│ [📈 Vider les Statistiques]         │
│ Supprime toutes les statistiques    │
│                                     │
│ [🔄 Réinitialisation Complète]      │
│ Remet l'app à l'état initial        │
└─────────────────────────────────────┘
```

## 🗑️ PROCÉDURE DE NETTOYAGE COMPLET

### 📊 Étape 1 : Vérifier l'État Actuel

1. **Ouvrez l'écran de nettoyage**
2. **Consultez "📊 État Actuel des Données"**
3. **Notez quelles données existent** :
   - ✅ Données présentes
   - ❌ Vide

### 🗑️ Étape 2 : Nettoyage Total

1. **Cliquez "🗑️ VIDER TOUTES LES DONNÉES"**
2. **Lisez l'avertissement** :
   ```
   ATTENTION: Cette action va supprimer TOUTES les données 
   de l'application (commandes, stock, statistiques, etc.). 
   Cette action est IRRÉVERSIBLE.
   
   Êtes-vous absolument sûr ?
   ```
3. **Cliquez "VIDER TOUT"**
4. **Attendez le message de succès** :
   ```
   ✅ Toutes les données ont été supprimées. 
   L'application est maintenant vide et prête 
   pour un nouveau test.
   ```

### ✅ Étape 3 : Vérification du Nettoyage

1. **Cliquez le bouton 🔄** pour actualiser
2. **Vérifiez que tout est vide** :
   ```
   📊 État Actuel des Données
   orders              ❌ Vide
   stock_data          ❌ Vide
   statistics          ❌ Vide
   daily_stats         ❌ Vide
   promotions          ❌ Vide
   offers              ❌ Vide
   ```

## 🔄 REDÉMARRAGE DE L'APPLICATION

### 📱 Redémarrage Complet

1. **Fermez l'application** complètement
2. **Arrêtez le serveur Expo** (Ctrl+C dans le terminal)
3. **Relancez** :
   ```cmd
   npx expo start
   ```
4. **Reconnectez-vous** : admin / 123

## 🧪 TEST COMPLET DEPUIS ZÉRO

### 📦 Test 1 : Stock Vide

1. **Allez dans "📦 Stock"**
2. **Vérifiez** : Tableau vide, pas d'indicateurs
3. **Testez la saisie** :
   ```
   Pâte à pizza: Initial [25,0] + Entrées [10,0] + Final [30,0]
   ```
4. **Sauvegardez** : "💾 Sauver"
5. **Vérifiez** : "📊 Données saisies" apparaît

### 🔄 Test 2 : Synchronisation

1. **Cliquez "Demain →"**
2. **Vérifiez** : Stock Initial = 30,0 ✅
3. **Vérifiez** : "🔄 Synchronisé" apparaît
4. **Testez navigation** : "← Hier" / "Demain →"

### 📋 Test 3 : Commandes Vides

1. **Allez dans "Nouvelle Commande"**
2. **Vérifiez** : Numéro de commande = 1
3. **Créez une commande test**
4. **Vérifiez** : Commande sauvegardée

### 📈 Test 4 : Statistiques Vides

1. **Allez dans "📈 Statistiques"**
2. **Vérifiez** : Pas de données
3. **Après avoir créé des commandes** : Statistiques apparaissent

### 📊 Test 5 : Consommation Vide

1. **Allez dans "📈 Consommation"**
2. **Vérifiez** : "Aucune donnée disponible"
3. **Après stock sur plusieurs jours** : Consommation calculée

## 🗑️ NETTOYAGES SÉLECTIFS

### 📋 Vider Seulement les Commandes

1. **Cliquez "📋 Vider les Commandes"**
2. **Confirmez** la suppression
3. **Résultat** : Commandes supprimées, stock conservé

### 📦 Vider Seulement le Stock

1. **Cliquez "📦 Vider le Stock"**
2. **Confirmez** la suppression
3. **Résultat** : Stock supprimé, commandes conservées

### 📈 Vider Seulement les Statistiques

1. **Cliquez "📈 Vider les Statistiques"**
2. **Confirmez** la suppression
3. **Résultat** : Stats supprimées, autres données conservées

## 📋 RAPPORT DE NETTOYAGE

### 📊 Générer un Rapport

1. **Cliquez "📋 Générer un Rapport"**
2. **Consultez le rapport détaillé** :
   ```
   📋 RAPPORT DE NETTOYAGE
   ======================
   
   AVANT NETTOYAGE:
   ✅ orders: 1250 caractères
   ✅ stock_data: 890 caractères
   ✅ statistics: 456 caractères
   ❌ daily_stats: 0 caractères
   
   APRÈS NETTOYAGE:
   ❌ orders: 0 caractères
   ❌ stock_data: 0 caractères
   ❌ statistics: 0 caractères
   ❌ daily_stats: 0 caractères
   
   RÉSULTAT: ✅ NETTOYAGE RÉUSSI
   ```

## ⚠️ POINTS D'ATTENTION

### 🚨 Avertissements Importants

- **IRRÉVERSIBLE** : Les suppressions ne peuvent pas être annulées
- **SAUVEGARDE** : Sauvegardez vos données importantes avant
- **REDÉMARRAGE** : Redémarrez l'application après nettoyage
- **TESTS UNIQUEMENT** : Utilisez pour les tests, pas en production

### 🔍 Vérifications Après Nettoyage

1. **État des données** : Tout doit être "❌ Vide"
2. **Fonctionnalités** : Toutes doivent fonctionner depuis zéro
3. **Synchronisation** : Stock doit se synchroniser correctement
4. **Numérotation** : Commandes doivent recommencer à 1

## 🎯 CHECKLIST DE TEST COMPLET

### ✅ Avant Nettoyage

- [ ] **Données présentes** : Vérifiez qu'il y a des données à supprimer
- [ ] **Fonctionnalités** : Testez que tout fonctionne avec les données existantes

### ✅ Nettoyage

- [ ] **Nettoyage total** : "🗑️ VIDER TOUTES LES DONNÉES"
- [ ] **Confirmation** : Message de succès affiché
- [ ] **Vérification** : Toutes les données "❌ Vide"
- [ ] **Redémarrage** : Application relancée

### ✅ Test Post-Nettoyage

- [ ] **Stock vide** : Tableau vide, saisie possible
- [ ] **Synchronisation** : Fonctionne depuis zéro
- [ ] **Commandes** : Numéro recommence à 1
- [ ] **Statistiques** : Vides puis se remplissent
- [ ] **Consommation** : Vide puis calculs corrects
- [ ] **Navigation** : Fluide entre tous les écrans

## 🎉 RÉSULTAT ATTENDU

### ✅ Application Propre

Après le nettoyage complet, vous devez avoir :

- **📦 Stock** : Tableau vide, prêt pour saisie
- **📋 Commandes** : Numéro 1, pas d'historique
- **📈 Statistiques** : Vides, prêtes à se remplir
- **📊 Consommation** : Vide, prête pour calculs
- **🔄 Synchronisation** : Fonctionne parfaitement
- **📱 Navigation** : Fluide entre tous les écrans

### ✅ Test Complet Réussi

Si tous les tests passent après nettoyage :

- ✅ **Nettoyage efficace** : Toutes les données supprimées
- ✅ **Fonctionnalités intactes** : Tout fonctionne depuis zéro
- ✅ **Synchronisation parfaite** : Stock se synchronise correctement
- ✅ **Interface propre** : Pas de données parasites
- ✅ **Prêt pour production** : Application testée et validée

**🗑️ Votre application est maintenant complètement nettoyée et prête pour un test complet depuis zéro !**
