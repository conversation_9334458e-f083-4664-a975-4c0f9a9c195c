// Base de données complète des produits - YassinApp
// Cette liste contient tous les produits du restaurant

import { BASE_PIZZAS } from './pizzaDatabase';

// Types de produits
export interface Product {
  id: string;
  name: string;
  description: string;
  category: 'pizza' | 'drink' | 'side';
  prices: {
    [key: string]: number;
  };
  image: string;
  isActive: boolean;
  sizes?: string[];
}

// Générer les pizzas avec leurs tailles et prix
const generatePizzaProducts = (): Product[] => {
  return BASE_PIZZAS.map((pizzaName, index) => ({
    id: `pizza-${index + 1}`,
    name: pizzaName,
    description: '', // Description supprimée
    category: 'pizza' as const,
    prices: {
      'Moyenne': 18.00,
      'Large': 24.00
    },
    image: '🍕',
    isActive: true,
    sizes: ['Moyenne', 'Large']
  }));
};

// Produits boissons
const DRINK_PRODUCTS: Product[] = [
  {
    id: 'boisson-1l',
    name: '<PERSON><PERSON> 1L',
    description: '<PERSON>son rafraîchissante 1 litre',
    category: 'drink',
    prices: {
      '1L': 6.00
    },
    image: '🥤',
    isActive: true,
    sizes: ['1L']
  },
  {
    id: 'canette',
    name: 'Canette',
    description: 'Canette rafraîchissante',
    category: 'drink',
    prices: {
      'Canette': 2.50
    },
    image: '🥤',
    isActive: true,
    sizes: ['Canette']
  }
];

// Produits accompagnements
const SIDE_PRODUCTS: Product[] = [
  {
    id: 'frites',
    name: 'Frites',
    description: 'Frites croustillantes et dorées',
    category: 'side',
    prices: {
      'Portion': 4.50
    },
    image: '🍟',
    isActive: true,
    sizes: ['Portion']
  },
  {
    id: 'nuggets',
    name: 'Nuggets',
    description: 'Nuggets de poulet croustillants',
    category: 'side',
    prices: {
      '4 pièces': 5.00,
      '6 pièces': 7.00
    },
    image: '🍗',
    isActive: true,
    sizes: ['4 pièces', '6 pièces']
  }
];



// Exporter tous les produits
export const ALL_PRODUCTS: Product[] = [
  ...generatePizzaProducts(),
  ...DRINK_PRODUCTS,
  ...SIDE_PRODUCTS
];

// Fonctions utilitaires
export const getProductsByCategory = (category: string): Product[] => {
  return ALL_PRODUCTS.filter(product => product.category === category && product.isActive);
};

export const getProductById = (id: string): Product | undefined => {
  return ALL_PRODUCTS.find(product => product.id === id);
};

export const getAllCategories = (): string[] => {
  const categories = [...new Set(ALL_PRODUCTS.map(product => product.category))];
  return categories;
};

// Correspondances pour les choix d'offres
export const OFFER_CHOICE_MAPPINGS: { [key: string]: { productId: string; size: string } } = {
  // Boissons
  'Boisson 1L': { productId: 'boisson-1l', size: '1L' },
  'Canette': { productId: 'canette', size: 'Canette' },

  // Accompagnements
  'Frites portion': { productId: 'frites', size: 'Portion' },
  'Nuggets (4 pièces)': { productId: 'nuggets', size: '4 pièces' },
  'Nuggets (6 pièces)': { productId: 'nuggets', size: '6 pièces' }
};
