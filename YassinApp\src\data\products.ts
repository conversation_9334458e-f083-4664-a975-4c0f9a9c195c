// Base de données complète des produits - YassinApp
// Cette liste contient tous les produits du restaurant

import { BASE_PIZZAS } from './pizzaDatabase';

// Types de produits
export interface Product {
  id: string;
  name: string;
  description: string;
  category: 'pizza' | 'drink' | 'side' | 'dessert';
  prices: {
    [key: string]: number;
  };
  image: string;
  isActive: boolean;
  sizes?: string[];
}

// Générer les pizzas avec leurs tailles et prix
const generatePizzaProducts = (): Product[] => {
  return BASE_PIZZAS.map((pizzaName, index) => ({
    id: `pizza-${index + 1}`,
    name: pizzaName,
    description: `Délicieuse pizza ${pizzaName.toLowerCase()}`,
    category: 'pizza' as const,
    prices: {
      'Petite': 12.00,
      'Moyenne': 18.00,
      'Large': 24.00
    },
    image: '🍕',
    isActive: true,
    sizes: ['Petite', 'Moyenne', 'Large']
  }));
};

// Produits boissons
const DRINK_PRODUCTS: Product[] = [
  {
    id: 'coca-33cl',
    name: 'Coca Cola',
    description: 'Coca Cola rafraîchissant',
    category: 'drink',
    prices: {
      '33cl': 2.50,
      '1L': 6.00
    },
    image: '🥤',
    isActive: true,
    sizes: ['33cl', '1L']
  },
  {
    id: 'fanta-33cl',
    name: 'Fanta',
    description: 'Fanta orange pétillant',
    category: 'drink',
    prices: {
      '33cl': 2.50,
      '1L': 6.00
    },
    image: '🍊',
    isActive: true,
    sizes: ['33cl', '1L']
  },
  {
    id: 'sprite-33cl',
    name: 'Sprite',
    description: 'Sprite citron-lime',
    category: 'drink',
    prices: {
      '33cl': 2.50,
      '1L': 6.00
    },
    image: '🍋',
    isActive: true,
    sizes: ['33cl', '1L']
  },
  {
    id: 'eau-50cl',
    name: 'Eau Minérale',
    description: 'Eau minérale naturelle',
    category: 'drink',
    prices: {
      '50cl': 1.50,
      '1.5L': 3.00
    },
    image: '💧',
    isActive: true,
    sizes: ['50cl', '1.5L']
  }
];

// Produits accompagnements
const SIDE_PRODUCTS: Product[] = [
  {
    id: 'frites',
    name: 'Frites',
    description: 'Frites croustillantes et dorées',
    category: 'side',
    prices: {
      'Portion': 4.50,
      'Grande Portion': 6.50
    },
    image: '🍟',
    isActive: true,
    sizes: ['Portion', 'Grande Portion']
  },
  {
    id: 'nuggets-4',
    name: 'Nuggets',
    description: 'Nuggets de poulet croustillants',
    category: 'side',
    prices: {
      '4 pièces': 5.00,
      '6 pièces': 7.00,
      '10 pièces': 11.00
    },
    image: '🍗',
    isActive: true,
    sizes: ['4 pièces', '6 pièces', '10 pièces']
  },
  {
    id: 'salade',
    name: 'Salade Verte',
    description: 'Salade fraîche et croquante',
    category: 'side',
    prices: {
      'Portion': 3.50
    },
    image: '🥗',
    isActive: true,
    sizes: ['Portion']
  },
  {
    id: 'pain-ail',
    name: 'Pain à l\'Ail',
    description: 'Pain grillé à l\'ail et aux herbes',
    category: 'side',
    prices: {
      'Portion': 3.00
    },
    image: '🥖',
    isActive: true,
    sizes: ['Portion']
  }
];

// Produits desserts
const DESSERT_PRODUCTS: Product[] = [
  {
    id: 'tiramisu',
    name: 'Tiramisu',
    description: 'Tiramisu italien traditionnel',
    category: 'dessert',
    prices: {
      'Portion': 6.00
    },
    image: '🍰',
    isActive: true,
    sizes: ['Portion']
  },
  {
    id: 'panna-cotta',
    name: 'Panna Cotta',
    description: 'Panna cotta aux fruits rouges',
    category: 'dessert',
    prices: {
      'Portion': 5.50
    },
    image: '🍮',
    isActive: true,
    sizes: ['Portion']
  },
  {
    id: 'glace-vanille',
    name: 'Glace Vanille',
    description: 'Glace artisanale à la vanille',
    category: 'dessert',
    prices: {
      '2 boules': 4.00,
      '3 boules': 5.50
    },
    image: '🍦',
    isActive: true,
    sizes: ['2 boules', '3 boules']
  },
  {
    id: 'glace-chocolat',
    name: 'Glace Chocolat',
    description: 'Glace artisanale au chocolat',
    category: 'dessert',
    prices: {
      '2 boules': 4.00,
      '3 boules': 5.50
    },
    image: '🍫',
    isActive: true,
    sizes: ['2 boules', '3 boules']
  }
];

// Exporter tous les produits
export const ALL_PRODUCTS: Product[] = [
  ...generatePizzaProducts(),
  ...DRINK_PRODUCTS,
  ...SIDE_PRODUCTS,
  ...DESSERT_PRODUCTS
];

// Fonctions utilitaires
export const getProductsByCategory = (category: string): Product[] => {
  return ALL_PRODUCTS.filter(product => product.category === category && product.isActive);
};

export const getProductById = (id: string): Product | undefined => {
  return ALL_PRODUCTS.find(product => product.id === id);
};

export const getAllCategories = (): string[] => {
  const categories = [...new Set(ALL_PRODUCTS.map(product => product.category))];
  return categories;
};

// Correspondances pour les choix d'offres
export const OFFER_CHOICE_MAPPINGS: { [key: string]: { productId: string; size: string } } = {
  // Boissons
  'Coca Cola 33cl': { productId: 'coca-33cl', size: '33cl' },
  'Coca Cola 1L': { productId: 'coca-33cl', size: '1L' },
  'Fanta 33cl': { productId: 'fanta-33cl', size: '33cl' },
  'Fanta 1L': { productId: 'fanta-33cl', size: '1L' },
  'Sprite 33cl': { productId: 'sprite-33cl', size: '33cl' },
  'Sprite 1L': { productId: 'sprite-33cl', size: '1L' },
  'Eau 50cl': { productId: 'eau-50cl', size: '50cl' },
  
  // Accompagnements
  'Frites portion': { productId: 'frites', size: 'Portion' },
  'Nuggets (4 pièces)': { productId: 'nuggets-4', size: '4 pièces' },
  'Nuggets (6 pièces)': { productId: 'nuggets-4', size: '6 pièces' },
  'Nuggets (10 pièces)': { productId: 'nuggets-4', size: '10 pièces' },
  
  // Desserts
  'Tiramisu': { productId: 'tiramisu', size: 'Portion' },
  'Panna Cotta': { productId: 'panna-cotta', size: 'Portion' },
  'Glace Vanille': { productId: 'glace-vanille', size: '2 boules' },
  'Glace Chocolat': { productId: 'glace-chocolat', size: '2 boules' }
};
