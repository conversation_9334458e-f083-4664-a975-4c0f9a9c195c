// Palette de couleurs pour l'application Pizza Caisse Manager

export const Colors = {
  // Couleurs principales
  primary: '#e74c3c',      // Rouge pizza
  primaryDark: '#c0392b',  // Rouge foncé
  secondary: '#3498db',    // Bleu
  secondaryDark: '#2980b9', // Bleu foncé
  
  // Couleurs d'accent
  success: '#27ae60',      // Vert
  successDark: '#229954',  // Vert foncé
  warning: '#f39c12',      // Orange
  warningDark: '#e67e22',  // Orange foncé
  danger: '#e74c3c',       // Rouge
  info: '#3498db',         // Bleu info
  
  // Couleurs neutres
  dark: '#2c3e50',         // Bleu foncé
  darkSecondary: '#34495e', // Bleu gris
  light: '#ecf0f1',        // Gris très clair
  lightSecondary: '#bdc3c7', // Gris clair
  
  // Couleurs de fond
  background: '#f5f5f5',   // Gris de fond
  surface: '#ffffff',      // Blanc
  card: '#ffffff',         // Blanc pour les cartes
  
  // Couleurs de texte
  text: '#2c3e50',         // Texte principal
  textSecondary: '#7f8c8d', // Texte secondaire
  textLight: '#95a5a6',    // Texte clair
  textOnPrimary: '#ffffff', // Texte sur couleur primaire
  textOnDark: '#ffffff',   // Texte sur fond foncé
  
  // Couleurs de bordure
  border: '#ddd',          // Bordure standard
  borderLight: '#f0f0f0',  // Bordure claire
  
  // Couleurs d'état
  online: '#27ae60',       // En ligne
  offline: '#95a5a6',      // Hors ligne
  pending: '#f39c12',      // En attente
  
  // Couleurs spécifiques pizza
  pizzaRed: '#d63031',     // Rouge pizza
  pizzaGreen: '#00b894',   // Vert basilic
  pizzaYellow: '#fdcb6e',  // Jaune fromage
  pizzaBrown: '#6c5ce7',   // Brun pâte
};

export const Shadows = {
  small: {
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  medium: {
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  large: {
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.15,
    shadowRadius: 8,
    elevation: 5,
  },
};

export const BorderRadius = {
  small: 4,
  medium: 8,
  large: 12,
  xl: 16,
  round: 50,
};

export const Spacing = {
  xs: 4,
  sm: 8,
  md: 16,
  lg: 24,
  xl: 32,
  xxl: 48,
};

export const FontSizes = {
  xs: 12,
  sm: 14,
  md: 16,
  lg: 18,
  xl: 20,
  xxl: 24,
  xxxl: 32,
};
