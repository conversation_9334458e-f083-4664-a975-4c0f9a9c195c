@echo off
echo ========================================
echo      REPARATION ET DEMARRAGE EXPO
echo ========================================
echo.
echo 🔧 Script de reparation automatique
echo 📱 Pizza Caisse Manager v1.6.0
echo.

cd /d "C:\Users\<USER>\Documents\AppCaisse\Apk test\PizzaAPK"

echo 📁 Repertoire: %CD%
echo.

echo 🧹 Nettoyage du cache...
if exist "node_modules\.cache" rmdir /s /q "node_modules\.cache"
if exist ".expo" rmdir /s /q ".expo"
echo ✅ Cache nettoye
echo.

echo 📦 Reinstallation des dependances...
if exist "node_modules" rmdir /s /q "node_modules"
if exist "package-lock.json" del "package-lock.json"
call npm install
echo ✅ Dependances reinstallees
echo.

echo 🔧 Installation d'Expo CLI...
call npm install -g @expo/cli
echo ✅ Expo CLI installe
echo.

echo 🚀 Demarrage d'Expo (methode simple)...
echo.
echo 📱 Le QR code va apparaitre...
echo ⏳ Patientez 30 secondes...
echo.

call npx expo start

echo.
echo ========================================
echo Si ca ne marche toujours pas:
echo 1. Redemarrez l'ordinateur
echo 2. Verifiez la connexion internet
echo 3. Contactez le support
echo ========================================
pause
