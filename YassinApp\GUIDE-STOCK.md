# 📦 GUIDE DE GESTION DU STOCK - YASSINAPP

## 🎯 Système de Stock Simplifié

Le nouveau système de stock de YassinApp est conçu pour être **simple, pratique et automatique** avec la continuité entre les jours.

## 📱 Interface du Stock

### 🗓️ Navigation par Date

```
┌─────────────────────────────────────┐
│ [← J-1]    Lundi 15 juillet    [J+1 →] │
│              ✅ Finalisé              │
└─────────────────────────────────────┘
```

- **← J-1** : Voir le stock du jour précédent
- **J+1 →** : Voir le stock du jour suivant
- **✅ Finalisé** : Indique que le stock est verrouillé

### 📊 Tableau de Stock

```
┌─────────────┬──────────────┬─────────┬─────────────┐
│   Produit   │ Stock Initial│ Entrées │ Stock Final │
├─────────────┼──────────────┼─────────┼─────────────┤
│ Pâte à pizza│   [25,000]   │[10,000] │   35,000    │
│    (kg)     │              │         │             │
├─────────────┼──────────────┼─────────┼─────────────┤
│ Mozzarella  │   [8,500]    │[2,000]  │   10,500    │
│    (kg)     │              │         │             │
└─────────────┴──────────────┴─────────┴─────────────┘
```

## 🔢 Comment Saisir le Stock

### 📝 Étapes de Saisie :

1. **Stock Initial** : Saisis<PERSON>z le stock du début de journée
2. **Entrées** : Saisis<PERSON>z les livraisons/achats du jour
3. **Stock Final** : Calculé automatiquement (Initial + Entrées)

### ⌨️ Format de Saisie :

- **Virgule acceptée** : Tapez `12,5` ou `12.5`
- **3 décimales** : Précision jusqu'à `12,500`
- **Affichage** : Garde votre format de saisie

### 📋 Exemple Pratique :

**Jour 1 (Lundi) :**
```
Pâte à pizza:
- Stock Initial: 0,000 kg (premier jour)
- Entrées: 50,000 kg (livraison)
- Stock Final: 50,000 kg ✅ (calculé automatiquement)
```

**Jour 2 (Mardi) :**
```
Pâte à pizza:
- Stock Initial: 50,000 kg ✅ (= Stock Final de lundi)
- Entrées: 20,000 kg (nouvelle livraison)
- Stock Final: 70,000 kg ✅ (50 + 20)
```

## 🔄 Continuité Automatique

### 📅 Principe J → J+1 :

**Stock Final du jour J = Stock Initial du jour J+1**

Cette continuité se fait **automatiquement** quand vous finalisez le stock.

### 🎯 Avantages :

- ✅ **Pas de ressaisie** du stock initial
- ✅ **Continuité garantie** entre les jours
- ✅ **Traçabilité complète** des mouvements
- ✅ **Évite les erreurs** de report

## 💾 Sauvegarde et Finalisation

### 💾 Bouton Sauvegarder :

- **Fonction** : Sauvegarde vos modifications
- **Quand** : À tout moment pendant la saisie
- **Effet** : Garde vos données, permet modifications

### ✅ Bouton Finaliser :

- **Fonction** : Verrouille le stock du jour
- **Quand** : En fin de journée, quand tout est saisi
- **Effet** : 
  - Verrouille les modifications
  - Transfère automatiquement vers J+1
  - Marque le jour comme "Finalisé"

## ⚠️ Alertes de Stock Bas

### 🚨 Indicateurs Visuels :

- **⚠️** : Icône d'alerte sur les produits en stock bas
- **Texte rouge** : Stock final en dessous du seuil
- **Notification** : Alerte visible dans l'interface

### 📊 Seuils d'Alerte par Défaut :

- **Pâte à pizza** : 10 kg
- **Mozzarella** : 5 kg
- **Sauce tomate** : 3 L
- **Pepperoni** : 2 kg
- **Autres ingrédients** : 1-2 kg

## 🎯 Workflow Quotidien Recommandé

### 🌅 Début de Journée :

1. **Ouvrez le stock** du jour
2. **Vérifiez le stock initial** (automatique si J-1 finalisé)
3. **Ajustez si nécessaire** (inventaire physique)

### 📦 Pendant la Journée :

1. **Saisissez les entrées** au fur et à mesure
2. **Sauvegardez régulièrement** (💾)
3. **Vérifiez les alertes** de stock bas

### 🌙 Fin de Journée :

1. **Vérifiez tous les chiffres**
2. **Sauvegardez** une dernière fois (💾)
3. **Finalisez le stock** (✅)
4. **Le stock de demain** est automatiquement préparé

## 📱 Interface Mobile Optimisée

### 🎯 Caractéristiques :

- **Tableau responsive** : S'adapte à votre écran
- **Saisie tactile** : Clavier numérique automatique
- **Navigation fluide** : Changement de date facile
- **Feedback visuel** : Couleurs et icônes claires

### 📋 Boutons d'Action :

- **💾** : Sauvegarder (vert)
- **✅** : Finaliser (bleu)
- **← J-1** : Jour précédent
- **J+1 →** : Jour suivant

## 🔧 Fonctionnalités Avancées

### 📊 Historique :

- **Consultation** : Voir tous les jours précédents
- **Modification** : Possible si pas finalisé
- **Traçabilité** : Suivi complet des mouvements

### 🔄 Continuité :

- **Automatique** : Stock final → Stock initial
- **Vérification** : Contrôle de cohérence
- **Correction** : Possible avant finalisation

## 🚀 Comment Tester

### 📱 Lancement :
```cmd
cd "C:\Users\<USER>\Documents\AppCaisse\YassinApp"
npx expo start
```

### 🔑 Connexion :
- Nom d'utilisateur : `admin`
- Mot de passe : `123`

### 📦 Test du Stock :

1. **Cliquez sur "Stock"**
2. **Saisissez du stock initial** : `25,5` pour la pâte
3. **Ajoutez des entrées** : `10,0` pour une livraison
4. **Vérifiez le calcul** : Stock final = 35,5
5. **Sauvegardez** avec 💾
6. **Finalisez** avec ✅
7. **Passez au jour suivant** avec J+1 →
8. **Vérifiez la continuité** : Stock initial = 35,5

## 🎉 Avantages du Nouveau Système

### ✅ Simplicité :
- **3 colonnes seulement** : Initial, Entrées, Final
- **Calcul automatique** du stock final
- **Interface intuitive** et claire

### ✅ Continuité :
- **Transfert automatique** J → J+1
- **Pas de ressaisie** manuelle
- **Cohérence garantie** entre les jours

### ✅ Sécurité :
- **Finalisation** verrouille les modifications
- **Sauvegarde** protège contre les pertes
- **Historique** complet des mouvements

### ✅ Mobilité :
- **Optimisé tactile** pour smartphone
- **Saisie rapide** avec virgule
- **Navigation fluide** entre les jours

**📦 Votre stock est maintenant géré de manière professionnelle et automatique !**
