// Données des pizzas
const pizzas = [
    {
        id: 1,
        name: "<PERSON><PERSON><PERSON><PERSON>",
        description: "Sauce tomate, mozzarella, basilic frais",
        price: 12.50,
        image: "🍕"
    },
    {
        id: 2,
        name: "Pepperoni",
        description: "Sauce tomate, mozzarella, pepperoni",
        price: 15.00,
        image: "🍕"
    },
    {
        id: 3,
        name: "Quatre Fromages",
        description: "Mozzarella, gorgonzola, parmesan, chèvre",
        price: 17.50,
        image: "🧀"
    },
    {
        id: 4,
        name: "Végétarienne",
        description: "Sauce tomate, mozzarella, légumes grillés",
        price: 16.00,
        image: "🥬"
    },
    {
        id: 5,
        name: "Calzone",
        description: "Pizza fermée avec jambon, mozzarella, champignons",
        price: 18.00,
        image: "🥟"
    },
    {
        id: 6,
        name: "Fruits de Mer",
        description: "Sauce tomate, mozzarella, fruits de mer",
        price: 22.00,
        image: "🦐"
    }
];

// Variables globales
let cart = [];
let orders = JSON.parse(localStorage.getItem('pizza_orders') || '[]');

// Initialisation de l'application
document.addEventListener('DOMContentLoaded', function() {
    displayPizzas();
    updateStatistics();
});

// Affichage des pizzas
function displayPizzas() {
    const pizzaGrid = document.getElementById('pizzaGrid');
    pizzaGrid.innerHTML = '';

    pizzas.forEach(pizza => {
        const pizzaCard = document.createElement('div');
        pizzaCard.className = 'pizza-card';
        pizzaCard.innerHTML = `
            <div style="font-size: 3rem; margin-bottom: 10px;">${pizza.image}</div>
            <h3>${pizza.name}</h3>
            <p>${pizza.description}</p>
            <div class="price">${pizza.price.toFixed(2)} DT</div>
            <button class="add-btn" onclick="addToCart(${pizza.id})">Ajouter au panier</button>
        `;
        pizzaGrid.appendChild(pizzaCard);
    });
}

// Ajouter au panier
function addToCart(pizzaId) {
    const pizza = pizzas.find(p => p.id === pizzaId);
    const existingItem = cart.find(item => item.id === pizzaId);

    if (existingItem) {
        existingItem.quantity += 1;
    } else {
        cart.push({
            ...pizza,
            quantity: 1
        });
    }

    updateCartDisplay();
    showNotification(`${pizza.name} ajoutée au panier!`);
}

// Mettre à jour l'affichage du panier
function updateCartDisplay() {
    const cartElement = document.getElementById('cart');
    const cartItems = document.getElementById('cartItems');
    const cartTotal = document.getElementById('cartTotal');

    if (cart.length === 0) {
        cartElement.style.display = 'none';
        return;
    }

    cartElement.style.display = 'block';
    cartItems.innerHTML = '';

    let total = 0;
    cart.forEach(item => {
        const itemTotal = item.price * item.quantity;
        total += itemTotal;

        const cartItem = document.createElement('div');
        cartItem.className = 'cart-item';
        cartItem.innerHTML = `
            <span>${item.name} x${item.quantity}</span>
            <span>${itemTotal.toFixed(2)} DT</span>
            <button onclick="removeFromCart(${item.id})" style="background: #e74c3c; color: white; border: none; padding: 5px 10px; border-radius: 4px; cursor: pointer;">×</button>
        `;
        cartItems.appendChild(cartItem);
    });

    cartTotal.textContent = `Total: ${total.toFixed(2)} DT`;
    
    // Mettre à jour le résumé de commande
    updateOrderSummary();
}

// Supprimer du panier
function removeFromCart(pizzaId) {
    cart = cart.filter(item => item.id !== pizzaId);
    updateCartDisplay();
}

// Mettre à jour le résumé de commande
function updateOrderSummary() {
    const orderSummary = document.getElementById('orderSummary');
    const orderTotal = document.getElementById('orderTotal');

    orderSummary.innerHTML = '';
    let total = 0;

    cart.forEach(item => {
        const itemTotal = item.price * item.quantity;
        total += itemTotal;

        const orderItem = document.createElement('div');
        orderItem.className = 'cart-item';
        orderItem.innerHTML = `
            <span>${item.name} x${item.quantity}</span>
            <span>${itemTotal.toFixed(2)} DT</span>
        `;
        orderSummary.appendChild(orderItem);
    });

    orderTotal.textContent = `Total: ${total.toFixed(2)} DT`;
}

// Changer d'écran
function showScreen(screenName) {
    // Masquer tous les écrans
    document.querySelectorAll('.screen').forEach(screen => {
        screen.classList.remove('active');
    });

    // Désactiver tous les boutons de navigation
    document.querySelectorAll('.nav-btn').forEach(btn => {
        btn.classList.remove('active');
    });

    // Afficher l'écran sélectionné
    document.getElementById(screenName).classList.add('active');

    // Activer le bouton correspondant
    event.target.classList.add('active');

    // Mettre à jour les données si nécessaire
    if (screenName === 'order-form') {
        updateOrderSummary();
    } else if (screenName === 'statistics') {
        updateStatistics();
    }
}

// Soumettre la commande
function submitOrder() {
    const customerName = document.getElementById('customerName').value;
    const customerPhone = document.getElementById('customerPhone').value;
    const customerAddress = document.getElementById('customerAddress').value;

    if (!customerName || !customerPhone || cart.length === 0) {
        showNotification('Veuillez remplir tous les champs et ajouter des articles au panier!', 'error');
        return;
    }

    const total = cart.reduce((sum, item) => sum + (item.price * item.quantity), 0);

    const order = {
        id: Date.now(),
        customer: {
            name: customerName,
            phone: customerPhone,
            address: customerAddress
        },
        items: [...cart],
        totalAmount: total,
        status: 'En préparation',
        date: new Date().toISOString(),
        orderNumber: `CMD-${Date.now().toString().slice(-6)}`
    };

    orders.push(order);
    localStorage.setItem('pizza_orders', JSON.stringify(orders));

    // Réinitialiser le formulaire et le panier
    cart = [];
    document.getElementById('customerName').value = '';
    document.getElementById('customerPhone').value = '';
    document.getElementById('customerAddress').value = '';
    updateCartDisplay();

    showNotification(`Commande ${order.orderNumber} confirmée! Total: ${total.toFixed(2)} DT`, 'success');
    
    // Retourner à l'écran principal
    setTimeout(() => {
        showScreen('pizza-list');
        updateStatistics();
    }, 2000);
}

// Mettre à jour les statistiques
function updateStatistics() {
    const totalOrdersElement = document.getElementById('totalOrders');
    const totalRevenueElement = document.getElementById('totalRevenue');
    const avgOrderValueElement = document.getElementById('avgOrderValue');
    const todayOrdersElement = document.getElementById('todayOrders');

    const totalOrders = orders.length;
    const totalRevenue = orders.reduce((sum, order) => sum + order.totalAmount, 0);
    const avgOrderValue = totalOrders > 0 ? totalRevenue / totalOrders : 0;

    // Commandes d'aujourd'hui
    const today = new Date().toDateString();
    const todayOrders = orders.filter(order => 
        new Date(order.date).toDateString() === today
    ).length;

    totalOrdersElement.textContent = totalOrders;
    totalRevenueElement.textContent = `${totalRevenue.toFixed(2)} DT`;
    avgOrderValueElement.textContent = `${avgOrderValue.toFixed(2)} DT`;
    todayOrdersElement.textContent = todayOrders;
}

// Afficher une notification
function showNotification(message, type = 'success') {
    const notification = document.createElement('div');
    notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: ${type === 'error' ? '#e74c3c' : '#27ae60'};
        color: white;
        padding: 15px 20px;
        border-radius: 8px;
        z-index: 1000;
        font-weight: bold;
        box-shadow: 0 4px 12px rgba(0,0,0,0.3);
        animation: slideIn 0.3s ease;
    `;
    notification.textContent = message;

    // Ajouter l'animation CSS
    const style = document.createElement('style');
    style.textContent = `
        @keyframes slideIn {
            from { transform: translateX(100%); opacity: 0; }
            to { transform: translateX(0); opacity: 1; }
        }
    `;
    document.head.appendChild(style);

    document.body.appendChild(notification);

    setTimeout(() => {
        notification.remove();
        style.remove();
    }, 3000);
}

// Fonctions utilitaires pour les tests (accessibles depuis la console)
window.showAllOrders = function() {
    console.log('📊 Commandes actuelles:', orders.length);
    orders.forEach((order, index) => {
        console.log(`${index + 1}. ${order.customer?.name || 'Client inconnu'} - ${order.totalAmount} DT - ${order.status}`);
    });
    return orders;
};

window.clearAllOrders = function() {
    orders = [];
    localStorage.removeItem('pizza_orders');
    updateStatistics();
    console.log('🧹 Toutes les commandes supprimées');
};

window.addTestOrder = function() {
    const testOrder = {
        id: Date.now(),
        customer: {
            name: 'Client Test',
            phone: '12345678',
            address: 'Adresse Test'
        },
        items: [pizzas[0]],
        totalAmount: pizzas[0].price,
        status: 'Livrée',
        date: new Date().toISOString(),
        orderNumber: `CMD-${Date.now().toString().slice(-6)}`
    };
    
    orders.push(testOrder);
    localStorage.setItem('pizza_orders', JSON.stringify(orders));
    updateStatistics();
    console.log('✅ Commande test ajoutée');
};
