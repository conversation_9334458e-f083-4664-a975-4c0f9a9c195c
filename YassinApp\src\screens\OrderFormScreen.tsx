import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TextInput,
  TouchableOpacity,
  Alert,
  KeyboardAvoidingView,
  Platform,
} from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { Colors, FontSizes, Spacing, BorderRadius, Shadows } from '../styles/theme';
import { OrderItem, Customer } from '../types';
import { CartService } from '../services/CartService';

interface OrderFormScreenProps {
  navigation: any;
  route?: {
    params?: {
      cartItems: OrderItem[];
    };
  };
}

export const OrderFormScreen: React.FC<OrderFormScreenProps> = ({ navigation, route }) => {
  const [cartItems, setCartItems] = useState<OrderItem[]>([]);
  const [subtotalAmount, setSubtotalAmount] = useState(0);
  const [deliveryFee, setDeliveryFee] = useState('');
  const cartService = CartService;
  const [totalAmount, setTotalAmount] = useState(0);
  const [customer, setCustomer] = useState<Customer>({
    name: '',
    phone: '',
    address: '',
  });
  const [paymentMethod, setPaymentMethod] = useState('Espèces');
  const [specialInstructions, setSpecialInstructions] = useState('');

  // Charger le panier depuis CartService
  useEffect(() => {
    const loadCart = () => {
      try {
        const cartData = cartService.getCart();
        const cartItems = cartData.items || [];
        console.log('📋 Chargement du panier dans OrderForm:', cartItems.length, 'articles');

        // Convertir CartItem en OrderItem
        const orderItems = cartItems.map(cartItem => ({
          id: cartItem.id,
          name: cartItem.name,
          quantity: cartItem.quantity,
          unitPrice: cartItem.price || 0,
          totalPrice: (cartItem.price || 0) * (cartItem.quantity || 1),
          category: cartItem.category,
          size: cartItem.size,
          specialInstructions: cartItem.specialInstructions,
          isOffer: cartItem.isOffer || false,
          offerItems: cartItem.offerItems || []
        }));

        console.log('📦 Articles convertis:', orderItems);
        setCartItems(orderItems);
      } catch (error) {
        console.error('❌ Erreur lors du chargement du panier:', error);
        setCartItems([]);
      }
    };

    loadCart();
  }, []);

  // Fonction pour recharger le panier manuellement
  const reloadCart = () => {
    try {
      const cartData = CartService.getCart();
      const cartItems = cartData.items || [];
      console.log('🔄 Rechargement manuel du panier:', cartItems.length, 'articles');

      // Convertir CartItem en OrderItem
      const orderItems = cartItems.map(cartItem => ({
        id: cartItem.id,
        name: cartItem.name,
        quantity: cartItem.quantity,
        unitPrice: cartItem.price || 0,
        totalPrice: (cartItem.price || 0) * (cartItem.quantity || 1),
        category: cartItem.category,
        size: cartItem.size,
        specialInstructions: cartItem.specialInstructions,
        isOffer: cartItem.isOffer || false,
        offerItems: cartItem.offerItems || []
      }));

      setCartItems(orderItems);
      Alert.alert('Panier rechargé', `${orderItems.length} article${orderItems.length > 1 ? 's' : ''} trouvé${orderItems.length > 1 ? 's' : ''}`);
    } catch (error) {
      console.error('❌ Erreur lors du rechargement du panier:', error);
      Alert.alert('Erreur', 'Impossible de recharger le panier');
    }
  };

  useEffect(() => {
    // Calculer le sous-total de tous les items
    const subtotal = cartItems.reduce((sum, item) => sum + item.totalPrice, 0);
    setSubtotalAmount(subtotal);
  }, [cartItems]);

  useEffect(() => {
    // Calculer le total avec les frais de livraison
    const deliveryAmount = parseFloat(deliveryFee) || 0;
    setTotalAmount(subtotalAmount + deliveryAmount);
  }, [subtotalAmount, deliveryFee]);

  const handleQuantityChange = (index: number, newQuantity: number) => {
    if (newQuantity < 1) return;

    const updatedItems = [...cartItems];
    updatedItems[index].quantity = newQuantity;
    updatedItems[index].totalPrice = updatedItems[index].unitPrice * newQuantity;
    setCartItems(updatedItems);
  };

  const handleRemoveItem = (index: number) => {
    const updatedItems = [...cartItems];
    updatedItems.splice(index, 1);
    setCartItems(updatedItems);

    // Si plus d'items, retourner à l'écran précédent
    if (updatedItems.length === 0) {
      navigation.goBack();
    }
  };

  const handleSubmitOrder = async () => {
    if (!customer.name || !customer.phone) {
      Alert.alert('Information manquante', 'Veuillez remplir au moins le nom et le téléphone du client.');
      return;
    }

    try {
      // Générer un numéro de commande unique
      let orderCounter = await AsyncStorage.getItem('orderCounter');
      let nextOrderNumber = orderCounter ? parseInt(orderCounter) + 1 : 1001;
      await AsyncStorage.setItem('orderCounter', nextOrderNumber.toString());

      const orderId = Date.now().toString();
      const deliveryAmount = parseFloat(deliveryFee) || 0;

      const order = {
        id: orderId,
        orderNumber: nextOrderNumber.toString(),
        customer,
        items: cartItems,
        subtotalAmount,
        deliveryFee: deliveryAmount,
        totalAmount,
        status: 'Active',
        date: new Date().toISOString(),
        paymentMethod,
        specialInstructions,
      };

      // Sauvegarder la commande
      const existingOrders = await AsyncStorage.getItem('orders');
      const orders = existingOrders ? JSON.parse(existingOrders) : [];
      orders.push(order);
      await AsyncStorage.setItem('orders', JSON.stringify(orders));

      const deliveryText = deliveryAmount > 0 ? `\nFrais de livraison: ${deliveryAmount.toFixed(2)} DT` : '';
      Alert.alert(
        'Commande créée et sauvegardée',
        `Commande #${nextOrderNumber} créée avec succès!\nSous-total: ${subtotalAmount.toFixed(2)} DT${deliveryText}\nTotal: ${totalAmount.toFixed(2)} DT\n\n✅ La commande apparaîtra dans les statistiques.`,
        [
          {
            text: 'OK',
            onPress: () => navigation.navigate('Admin')
          }
        ]
      );
    } catch (error) {
      console.error('Erreur lors de la sauvegarde:', error);
      Alert.alert('Erreur', 'Impossible de sauvegarder la commande.');
    }
  };

  return (
    <KeyboardAvoidingView
      style={styles.container}
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      keyboardVerticalOffset={100}
    >
      <View style={styles.header}>
        <TouchableOpacity onPress={() => navigation.goBack()} style={styles.backButton}>
          <Text style={styles.backButtonText}>← Retour</Text>
        </TouchableOpacity>
        <Text style={styles.title}>Finaliser la commande</Text>
        <TouchableOpacity onPress={reloadCart} style={styles.reloadButton}>
          <Text style={styles.reloadButtonText}>🔄</Text>
        </TouchableOpacity>
      </View>

      <ScrollView style={styles.content}>
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Détails de la commande</Text>
          {cartItems.map((item, index) => (
            <View key={`${item.id}-${index}`} style={styles.cartItem}>
              <View style={styles.itemInfo}>
                <Text style={styles.itemName}>{item.name}</Text>
                {item.isOffer && item.offerItems && (
                  <View style={styles.offerDetails}>
                    <Text style={styles.offerDetailsTitle}>📋 Votre commande :</Text>
                    {item.offerItems.map((offerItem, idx) => (
                      <Text key={idx} style={styles.offerItemText}>
                        • {offerItem.choice}
                      </Text>
                    ))}
                  </View>
                )}
                <Text style={styles.itemPrice}>{item.unitPrice.toFixed(2)} DT</Text>
              </View>

              <View style={styles.quantityContainer}>
                <TouchableOpacity
                  onPress={() => handleQuantityChange(index, item.quantity - 1)}
                  style={styles.quantityButton}
                >
                  <Text style={styles.quantityButtonText}>-</Text>
                </TouchableOpacity>

                <Text style={styles.quantityText}>{item.quantity}</Text>

                <TouchableOpacity
                  onPress={() => handleQuantityChange(index, item.quantity + 1)}
                  style={styles.quantityButton}
                >
                  <Text style={styles.quantityButtonText}>+</Text>
                </TouchableOpacity>
              </View>

              <View style={styles.itemActions}>
                <Text style={styles.itemTotal}>{item.totalPrice.toFixed(2)} DT</Text>
                <TouchableOpacity onPress={() => handleRemoveItem(index)}>
                  <Text style={styles.removeButton}>🗑️</Text>
                </TouchableOpacity>
              </View>
            </View>
          ))}
          
          <View style={styles.subtotalContainer}>
            <Text style={styles.subtotalLabel}>Sous-total</Text>
            <Text style={styles.subtotalAmount}>{subtotalAmount.toFixed(2)} DT</Text>
          </View>
        </View>

        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Frais de livraison</Text>

          <View style={styles.deliveryContainer}>
            <View style={styles.inputContainer}>
              <Text style={styles.label}>Montant (selon la distance)</Text>
              <TextInput
                style={styles.input}
                value={deliveryFee}
                onChangeText={setDeliveryFee}
                placeholder="0.00"
                keyboardType="decimal-pad"
              />
              <Text style={styles.deliveryHint}>
                💡 Laissez vide si pas de livraison ou saisissez le montant selon la distance
              </Text>
            </View>
          </View>

          <View style={styles.totalContainer}>
            <Text style={styles.totalLabel}>TOTAL FINAL</Text>
            <Text style={styles.totalAmount}>{totalAmount.toFixed(2)} DT</Text>
          </View>
        </View>

        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Informations client</Text>
          
          <View style={styles.inputContainer}>
            <Text style={styles.label}>Nom*</Text>
            <TextInput
              style={styles.input}
              value={customer.name}
              onChangeText={(text) => setCustomer({...customer, name: text})}
              placeholder="Nom du client"
            />
          </View>
          
          <View style={styles.inputContainer}>
            <Text style={styles.label}>Téléphone*</Text>
            <TextInput
              style={styles.input}
              value={customer.phone}
              onChangeText={(text) => setCustomer({...customer, phone: text})}
              placeholder="Numéro de téléphone"
              keyboardType="phone-pad"
            />
          </View>
          
          <View style={styles.inputContainer}>
            <Text style={styles.label}>Adresse</Text>
            <TextInput
              style={styles.input}
              value={customer.address}
              onChangeText={(text) => setCustomer({...customer, address: text})}
              placeholder="Adresse de livraison (optionnel)"
              multiline
            />
          </View>
        </View>

        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Méthode de paiement</Text>
          
          <View style={styles.paymentOptions}>
            {['Espèces', 'Carte', 'Mobile'].map((method) => (
              <TouchableOpacity
                key={method}
                style={[
                  styles.paymentOption,
                  paymentMethod === method && styles.paymentOptionSelected
                ]}
                onPress={() => setPaymentMethod(method)}
              >
                <Text 
                  style={[
                    styles.paymentOptionText,
                    paymentMethod === method && styles.paymentOptionTextSelected
                  ]}
                >
                  {method}
                </Text>
              </TouchableOpacity>
            ))}
          </View>
        </View>

        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Instructions spéciales</Text>
          <TextInput
            style={[styles.input, styles.textArea]}
            value={specialInstructions}
            onChangeText={setSpecialInstructions}
            placeholder="Instructions spéciales pour la commande (optionnel)"
            multiline
            numberOfLines={3}
          />
        </View>

        <View style={styles.buttonContainer}>
          <TouchableOpacity style={styles.submitButton} onPress={handleSubmitOrder}>
            <Text style={styles.submitButtonText}>Confirmer la commande</Text>
          </TouchableOpacity>
        </View>
      </ScrollView>
    </KeyboardAvoidingView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
  },
  header: {
    backgroundColor: Colors.secondary,
    paddingTop: 50,
    paddingBottom: 15,
    paddingHorizontal: 16,
    flexDirection: 'row',
    alignItems: 'center',
  },
  backButton: {
    marginRight: 10,
  },
  backButtonText: {
    color: Colors.textOnDark,
    fontSize: FontSizes.md,
    fontWeight: '600',
  },
  title: {
    fontSize: FontSizes.xl,
    fontWeight: 'bold',
    color: Colors.textOnDark,
    flex: 1,
  },
  content: {
    flex: 1,
    padding: Spacing.md,
  },
  section: {
    backgroundColor: Colors.surface,
    borderRadius: BorderRadius.large,
    padding: Spacing.md,
    marginBottom: Spacing.lg,
    ...Shadows.small,
  },
  sectionTitle: {
    fontSize: FontSizes.lg,
    fontWeight: 'bold',
    color: Colors.textPrimary,
    marginBottom: Spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: Colors.border,
    paddingBottom: Spacing.xs,
  },
  cartItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: Spacing.sm,
    borderBottomWidth: 1,
    borderBottomColor: Colors.border,
  },
  itemInfo: {
    flex: 2,
  },
  itemName: {
    fontSize: FontSizes.md,
    fontWeight: '600',
    color: Colors.textPrimary,
  },

  itemSize: {
    fontSize: FontSizes.sm,
    color: Colors.textSecondary,
  },
  itemPrice: {
    fontSize: FontSizes.sm,
    color: Colors.textSecondary,
  },

  // Styles pour les détails des offres dans la finalisation de commande
  offerDetails: {
    marginTop: Spacing.sm,
    marginBottom: Spacing.xs,
    backgroundColor: Colors.light,
    padding: Spacing.sm,
    borderRadius: BorderRadius.small,
    borderLeftWidth: 3,
    borderLeftColor: Colors.success,
  },
  offerDetailsTitle: {
    fontSize: FontSizes.sm,
    fontWeight: 'bold',
    color: Colors.success,
    marginBottom: Spacing.xs,
  },
  offerItemText: {
    fontSize: FontSizes.sm,
    color: Colors.textSecondary,
    marginBottom: 2,
    paddingLeft: Spacing.sm,
  },
  quantityContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
    justifyContent: 'center',
  },
  quantityButton: {
    width: 30,
    height: 30,
    borderRadius: BorderRadius.round,
    backgroundColor: Colors.light,
    justifyContent: 'center',
    alignItems: 'center',
  },
  quantityButtonText: {
    fontSize: FontSizes.lg,
    fontWeight: 'bold',
    color: Colors.textPrimary,
  },
  quantityText: {
    fontSize: FontSizes.md,
    fontWeight: '600',
    marginHorizontal: Spacing.sm,
  },
  itemActions: {
    flex: 1,
    alignItems: 'flex-end',
  },
  itemTotal: {
    fontSize: FontSizes.md,
    fontWeight: 'bold',
    color: Colors.primary,
  },
  removeButton: {
    fontSize: FontSizes.lg,
    color: Colors.danger,
    padding: Spacing.xs,
  },
  subtotalContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginTop: Spacing.md,
    paddingTop: Spacing.md,
    borderTopWidth: 1,
    borderTopColor: Colors.border,
  },
  subtotalLabel: {
    fontSize: FontSizes.md,
    fontWeight: '600',
    color: Colors.textSecondary,
  },
  subtotalAmount: {
    fontSize: FontSizes.lg,
    fontWeight: 'bold',
    color: Colors.textPrimary,
  },
  deliveryContainer: {
    marginBottom: Spacing.md,
  },
  deliveryHint: {
    fontSize: FontSizes.xs,
    color: Colors.textSecondary,
    marginTop: Spacing.xs,
    fontStyle: 'italic',
    lineHeight: 16,
  },
  totalContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginTop: Spacing.lg,
    paddingTop: Spacing.md,
    borderTopWidth: 2,
    borderTopColor: Colors.primary,
    backgroundColor: Colors.light,
    padding: Spacing.md,
    borderRadius: BorderRadius.medium,
  },
  totalLabel: {
    fontSize: FontSizes.lg,
    fontWeight: 'bold',
    color: Colors.textPrimary,
  },
  totalAmount: {
    fontSize: FontSizes.xl,
    fontWeight: 'bold',
    color: Colors.primary,
  },
  inputContainer: {
    marginBottom: Spacing.md,
  },
  label: {
    fontSize: FontSizes.sm,
    color: Colors.textSecondary,
    marginBottom: Spacing.xs,
  },
  input: {
    backgroundColor: Colors.light,
    borderRadius: BorderRadius.medium,
    paddingHorizontal: Spacing.md,
    paddingVertical: Spacing.sm,
    fontSize: FontSizes.md,
    color: Colors.textPrimary,
    borderWidth: 1,
    borderColor: Colors.border,
  },
  textArea: {
    minHeight: 80,
    textAlignVertical: 'top',
  },
  paymentOptions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  paymentOption: {
    flex: 1,
    padding: Spacing.md,
    borderRadius: BorderRadius.medium,
    backgroundColor: Colors.light,
    marginHorizontal: Spacing.xs,
    alignItems: 'center',
    borderWidth: 1,
    borderColor: Colors.border,
  },
  paymentOptionSelected: {
    backgroundColor: Colors.primary,
    borderColor: Colors.primary,
  },
  paymentOptionText: {
    fontSize: FontSizes.md,
    fontWeight: '600',
    color: Colors.textPrimary,
  },
  paymentOptionTextSelected: {
    color: Colors.textOnPrimary,
  },
  buttonContainer: {
    marginVertical: Spacing.lg,
  },
  submitButton: {
    backgroundColor: Colors.success,
    borderRadius: BorderRadius.medium,
    paddingVertical: Spacing.md,
    alignItems: 'center',
    ...Shadows.medium,
  },
  submitButtonText: {
    fontSize: FontSizes.lg,
    fontWeight: 'bold',
    color: Colors.textOnPrimary,
  },

  // Styles pour le bouton de rechargement
  reloadButton: {
    padding: Spacing.sm,
  },
  reloadButtonText: {
    fontSize: FontSizes.lg,
    color: Colors.primary,
  },
});
