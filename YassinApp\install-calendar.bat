@echo off
chcp 65001 >nul
cls

echo ==========================================
echo    INSTALLATION CALENDRIER - YASSINAPP
echo ==========================================
echo.
echo Installation du package react-native-calendars
echo pour la nouvelle fonctionnalité de calendrier
echo.

cd /d "C:\Users\<USER>\Documents\AppCaisse\YassinApp"

if not exist "package.json" (
    echo ❌ ERREUR: Fichier package.json non trouvé
    echo Vérifiez que vous êtes dans le bon répertoire
    pause
    exit /b 1
)

echo 📁 Répertoire: %CD%
echo.

echo 📦 Installation du package react-native-calendars...
npm install react-native-calendars

if %ERRORLEVEL% EQU 0 (
    echo.
    echo ✅ Installation réussie !
    echo.
    echo 🎉 NOUVELLES FONCTIONNALITÉS DISPONIBLES:
    echo    ✓ Calendrier intégré pour sélection de date
    echo    ✓ Commandes annulées exclues automatiquement
    echo    ✓ Recalcul des totaux en temps réel
    echo    ✓ Interface calendrier moderne
    echo.
    echo 🚀 Vous pouvez maintenant lancer YassinApp avec:
    echo    start-yassinapp.bat
    echo.
) else (
    echo.
    echo ❌ Erreur lors de l'installation
    echo Vérifiez votre connexion internet et réessayez
    echo.
)

pause
