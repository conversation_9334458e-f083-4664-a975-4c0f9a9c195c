@echo off
chcp 65001 >nul
cls

echo ==========================================
echo    📱 CRÉATION APK SIMPLE - CAISSE 2.0
echo ==========================================
echo.
echo 🎯 Méthode simplifiée pour obtenir votre APK
echo 📱 Application: Pizza Caisse Manager 2.0
echo.
echo ==========================================

cd /d "C:\Users\<USER>\Documents\AppCaisse\Apk test\PizzaAPK"

echo 📂 Répertoire: %CD%
echo.

echo 🔧 Étape 1: Mise à jour de la configuration...
echo ✅ Configuration corrigée
echo.

echo 🔐 Étape 2: Connexion à Expo...
npx expo whoami

if %errorlevel% neq 0 (
    echo 🔑 Connexion requise...
    npx expo login
)

echo.
echo 🏗️ Étape 3: Création de l'APK...
echo ⏳ Cela va prendre 10-15 minutes...
echo.

echo 🚀 Démarrage de la build...
npx eas build --platform android --profile production

echo.
echo ==========================================
echo 🎉 BUILD LANCÉE !
echo ==========================================
echo.
echo ⏳ La build est en cours sur les serveurs Expo...
echo 📱 Vous recevrez le lien de téléchargement à la fin
echo.
echo 🔗 Le lien sera affiché ici quand c'est prêt ⬇️
echo.

timeout /t 5 /nobreak >nul

echo 📋 En attendant, vous pouvez aussi:
echo 1. Aller sur https://expo.dev/accounts/brona/projects
echo 2. Vous <NAME_EMAIL>
echo 3. Voir vos builds en cours
echo 4. Télécharger l'APK quand c'est prêt
echo.

echo ⏳ Attente de la fin de la build...
echo 📱 Le lien APK apparaîtra ci-dessous...
echo.

pause
