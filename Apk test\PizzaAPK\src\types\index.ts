// Types pour l'application de gestion de caisse pizza

export type PizzaSize = 'M' | 'L' | 'XL';

export interface Pizza {
  id: string;
  name: string;
  description: string;
  prices: {
    M: number;
    L: number;
    XL: number;
  };
  image?: string;
}

export interface OrderItem {
  pizzaId: string;
  pizzaName: string;
  size: PizzaSize;
  quantity: number;
  unitPrice: number;
  totalPrice: number;
}

export interface Customer {
  name: string;
  address: string;
  phoneNumber: string;
}

export interface Order {
  id: string;
  customer: Customer;
  items: OrderItem[];
  totalAmount: number;
  status: 'pending' | 'confirmed' | 'preparing' | 'ready' | 'delivered';
  createdAt: Date;
  updatedAt: Date;
}

export interface DailyStats {
  date: string;
  totalOrders: number;
  totalRevenue: number;
  popularPizza: string;
  averageOrderValue: number;
}

export interface Statistics {
  today: DailyStats;
  thisWeek: DailyStats[];
  thisMonth: DailyStats[];
  allTimeStats: {
    totalOrders: number;
    totalRevenue: number;
    mostPopularPizza: string;
    averageOrderValue: number;
  };
}

export interface StockItem {
  id: string;
  name: string;
  unit: string; // kg, L, unités, etc.
  stockInitial: number;
  entrees: number;
  sorties: number;
  stockFinal: number;
  seuilAlerte: number;
}

export interface DailyStock {
  date: string; // YYYY-MM-DD
  items: StockItem[];
}

export interface StockMovement {
  id: string;
  itemId: string;
  type: 'entree' | 'sortie';
  quantity: number;
  reason: string;
  date: Date;
}
