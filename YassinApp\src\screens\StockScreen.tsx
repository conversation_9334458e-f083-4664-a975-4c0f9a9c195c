import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  TextInput,
  Alert,
  KeyboardAvoidingView,
  Platform,
} from 'react-native';
import { Colors, FontSizes, Spacing, BorderRadius, Shadows } from '../styles/theme';
import { StockService, DailyStock } from '../services/StockService';

interface StockScreenProps {
  navigation: any;
}

interface StockItem {
  id: string;
  name: string;
  unit: string;
  stockInitial: number;
  entrees: number;
  stockFinal: number;
}

export const StockScreen: React.FC<StockScreenProps> = ({ navigation }) => {
  const [stockData, setStockData] = useState<DailyStock | null>(null);
  const [selectedDate, setSelectedDate] = useState<string>(new Date().toISOString().split('T')[0]);
  const [loading, setLoading] = useState<boolean>(true);
  const [currentView, setCurrentView] = useState<'stock' | 'consumption'>('stock');
  const [selectedMonth, setSelectedMonth] = useState<string>(new Date().toISOString().slice(0, 7));
  const [consumptionData, setConsumptionData] = useState<any[]>([]);
  const [filterType, setFilterType] = useState<'month' | 'year' | 'week' | 'custom'>('month');
  const [selectedYear, setSelectedYear] = useState<string>(new Date().getFullYear().toString());
  const [selectedWeek, setSelectedWeek] = useState<string>('');
  const [customStartDate, setCustomStartDate] = useState<string>(new Date().toISOString().split('T')[0]);
  const [customEndDate, setCustomEndDate] = useState<string>(new Date().toISOString().split('T')[0]);

  useEffect(() => {
    // Initialiser la semaine actuelle si pas encore définie
    if (selectedWeek === '') {
      const now = new Date();
      const currentWeek = getWeekNumber(now);
      setSelectedWeek(`${now.getFullYear()}-W${currentWeek.toString().padStart(2, '0')}`);
    }
  }, []);

  useEffect(() => {
    if (currentView === 'stock') {
      loadStock(selectedDate);
    } else {
      loadConsumptionDataByFilter();
    }
  }, [selectedDate, selectedMonth, selectedYear, selectedWeek, customStartDate, customEndDate, filterType, currentView]);

  const loadStock = async (date: string) => {
    try {
      setLoading(true);
      const stock = await StockService.getStockForDate(date);
      setStockData(stock);

      // Log pour debug
      console.log(`📅 Stock chargé pour ${date}:`, {
        nombreItems: stock.items.length,
        finalized: stock.isFinalized,
        exempleItem: stock.items[0] ? {
          name: stock.items[0].name,
          initial: stock.items[0].stockInitial,
          entrees: stock.items[0].entrees,
          final: stock.items[0].stockFinal
        } : null
      });
    } catch (error) {
      console.error('Erreur lors du chargement du stock:', error);
      Alert.alert('Erreur', 'Impossible de charger le stock');
    } finally {
      setLoading(false);
    }
  };

  const loadConsumptionDataByFilter = async () => {
    try {
      setLoading(true);
      let consumption: any[] = [];

      switch (filterType) {
        case 'month':
          consumption = await calculateMonthlyConsumption(selectedMonth);
          break;
        case 'year':
          consumption = await calculateYearlyConsumption(selectedYear);
          break;
        case 'week':
          consumption = await calculateWeeklyConsumption(selectedWeek);
          break;
        case 'custom':
          consumption = await calculateCustomPeriodConsumption(customStartDate, customEndDate);
          break;
      }

      setConsumptionData(consumption);
    } catch (error) {
      console.error('❌ Erreur lors du chargement de la consommation:', error);
      Alert.alert('Erreur', 'Impossible de charger les données de consommation');
    } finally {
      setLoading(false);
    }
  };

  const calculateMonthlyConsumption = async (month: string): Promise<any[]> => {
    // Obtenir tous les jours du mois
    const [year, monthNum] = month.split('-');
    const daysInMonth = new Date(parseInt(year), parseInt(monthNum), 0).getDate();

    const consumptionByProduct: { [key: string]: { name: string, unit: string, totalConsumption: number, category: string } } = {};

    for (let day = 1; day <= daysInMonth; day++) {
      const dateStr = `${month}-${day.toString().padStart(2, '0')}`;
      try {
        const dayStock = await StockService.getStockForDate(dateStr);

        dayStock.items.forEach(item => {
          if (!consumptionByProduct[item.id]) {
            consumptionByProduct[item.id] = {
              name: item.name,
              unit: item.unit,
              totalConsumption: 0,
              category: item.category || 'Autres'
            };
          }

          // Consommation = Stock Initial + Entrées - Stock Final
          const consumption = item.stockInitial + item.entrees - item.stockFinal;
          if (consumption > 0) {
            consumptionByProduct[item.id].totalConsumption += consumption;
          }
        });
      } catch (error) {
        // Ignorer les erreurs pour les jours sans données
      }
    }

    return Object.values(consumptionByProduct).filter(item => item.totalConsumption > 0);
  };

  const calculateYearlyConsumption = async (year: string): Promise<any[]> => {
    const consumptionByProduct: { [key: string]: { name: string, unit: string, totalConsumption: number, category: string } } = {};

    // Calculer pour tous les mois de l'année
    for (let month = 1; month <= 12; month++) {
      const monthStr = `${year}-${month.toString().padStart(2, '0')}`;
      const monthConsumption = await calculateMonthlyConsumption(monthStr);

      monthConsumption.forEach(item => {
        if (!consumptionByProduct[item.name]) {
          consumptionByProduct[item.name] = {
            name: item.name,
            unit: item.unit,
            totalConsumption: 0,
            category: item.category
          };
        }
        consumptionByProduct[item.name].totalConsumption += item.totalConsumption;
      });
    }

    return Object.values(consumptionByProduct).filter(item => item.totalConsumption > 0);
  };

  const calculateWeeklyConsumption = async (weekString: string): Promise<any[]> => {
    // Format: "2025-W30" (année-semaine)
    const [year, weekNum] = weekString.split('-W');
    const weekNumber = parseInt(weekNum);

    // Calculer les dates de début et fin de semaine
    const startDate = getDateOfWeek(parseInt(year), weekNumber);
    const endDate = new Date(startDate);
    endDate.setDate(startDate.getDate() + 6);

    return await calculateCustomPeriodConsumption(
      startDate.toISOString().split('T')[0],
      endDate.toISOString().split('T')[0]
    );
  };

  const calculateCustomPeriodConsumption = async (startDate: string, endDate: string): Promise<any[]> => {
    const consumptionByProduct: { [key: string]: { name: string, unit: string, totalConsumption: number, category: string } } = {};

    const start = new Date(startDate);
    const end = new Date(endDate);

    // Parcourir chaque jour de la période
    for (let date = new Date(start); date <= end; date.setDate(date.getDate() + 1)) {
      const dateStr = date.toISOString().split('T')[0];

      try {
        const dayStock = await StockService.getStockForDate(dateStr);

        dayStock.items.forEach(item => {
          if (!consumptionByProduct[item.id]) {
            consumptionByProduct[item.id] = {
              name: item.name,
              unit: item.unit,
              totalConsumption: 0,
              category: item.category || 'Autres'
            };
          }

          // Consommation = Stock Initial + Entrées - Stock Final
          const consumption = item.stockInitial + item.entrees - item.stockFinal;
          if (consumption > 0) {
            consumptionByProduct[item.id].totalConsumption += consumption;
          }
        });
      } catch (error) {
        // Ignorer les erreurs pour les jours sans données
      }
    }

    return Object.values(consumptionByProduct).filter(item => item.totalConsumption > 0);
  };

  const getDateOfWeek = (year: number, week: number): Date => {
    // Calculer le premier jour de la semaine donnée
    const simple = new Date(year, 0, 1 + (week - 1) * 7);
    const dow = simple.getDay();
    const ISOweekStart = simple;
    if (dow <= 4) {
      ISOweekStart.setDate(simple.getDate() - simple.getDay() + 1);
    } else {
      ISOweekStart.setDate(simple.getDate() + 8 - simple.getDay());
    }
    return ISOweekStart;
  };

  const generateAvailableMonths = () => {
    const months = [];

    // Générer tous les mois de 2020 à 2030
    for (let year = 2020; year <= 2030; year++) {
      for (let month = 1; month <= 12; month++) {
        const monthString = `${year}-${month.toString().padStart(2, '0')}`;
        months.push(monthString);
      }
    }

    // Trier par ordre décroissant (plus récent en premier)
    return months.sort((a, b) => b.localeCompare(a));
  };

  const generateAvailableYears = () => {
    const years = [];
    for (let year = 2020; year <= 2030; year++) {
      years.push(year.toString());
    }
    return years.sort((a, b) => b.localeCompare(a));
  };

  const generateAvailableWeeks = () => {
    const weeks = [];
    const currentYear = new Date().getFullYear();

    // Générer les semaines pour l'année actuelle et les 2 années précédentes/suivantes
    for (let year = currentYear - 2; year <= currentYear + 2; year++) {
      const weeksInYear = getWeeksInYear(year);
      for (let week = 1; week <= weeksInYear; week++) {
        weeks.push(`${year}-W${week.toString().padStart(2, '0')}`);
      }
    }

    return weeks.sort((a, b) => b.localeCompare(a));
  };

  const getWeeksInYear = (year: number): number => {
    // Une année a 52 ou 53 semaines
    const dec31 = new Date(year, 11, 31);
    const jan1 = new Date(year, 0, 1);
    return dec31.getDay() === 4 || jan1.getDay() === 4 ? 53 : 52;
  };

  const formatWeek = (weekString: string) => {
    const [year, week] = weekString.split('-W');
    const startDate = getDateOfWeek(parseInt(year), parseInt(week));
    const endDate = new Date(startDate);
    endDate.setDate(startDate.getDate() + 6);

    return `Semaine ${week}/${year} (${startDate.getDate()}/${startDate.getMonth() + 1} - ${endDate.getDate()}/${endDate.getMonth() + 1})`;
  };

  const getWeekNumber = (date: Date): number => {
    const d = new Date(Date.UTC(date.getFullYear(), date.getMonth(), date.getDate()));
    const dayNum = d.getUTCDay() || 7;
    d.setUTCDate(d.getUTCDate() + 4 - dayNum);
    const yearStart = new Date(Date.UTC(d.getUTCFullYear(), 0, 1));
    return Math.ceil((((d.getTime() - yearStart.getTime()) / 86400000) + 1) / 7);
  };

  const formatMonth = (monthString: string) => {
    const [year, month] = monthString.split('-');
    const monthNames = [
      'Janvier', 'Février', 'Mars', 'Avril', 'Mai', 'Juin',
      'Juillet', 'Août', 'Septembre', 'Octobre', 'Novembre', 'Décembre'
    ];
    return `${monthNames[parseInt(month) - 1]} ${year}`;
  };

  const showAllMonths = () => {
    const months = generateAvailableMonths();
    const monthsPerPage = 24;
    const totalPages = Math.ceil(months.length / monthsPerPage);

    const showPage = (page: number) => {
      const startIndex = page * monthsPerPage;
      const endIndex = Math.min(startIndex + monthsPerPage, months.length);
      const pageMonths = months.slice(startIndex, endIndex);

      const buttons = [
        ...pageMonths.map(month => ({
          text: formatMonth(month),
          onPress: () => setSelectedMonth(month)
        })),
        ...(page > 0 ? [{ text: '← Page précédente', onPress: () => showPage(page - 1) }] : []),
        ...(page < totalPages - 1 ? [{ text: 'Page suivante →', onPress: () => showPage(page + 1) }] : []),
        { text: 'Annuler', style: 'cancel' as const }
      ];

      Alert.alert(
        `Choisir un mois (Page ${page + 1}/${totalPages})`,
        'Sélectionnez le mois à analyser :',
        buttons
      );
    };

    showPage(0);
  };

  const renderFilterSelector = () => {
    switch (filterType) {
      case 'month':
        return (
          <View style={styles.selectorContainer}>
            <Text style={styles.selectorLabel}>📅 Mois sélectionné :</Text>
            <TouchableOpacity
              style={styles.selector}
              onPress={() => {
                const months = generateAvailableMonths();
                Alert.alert(
                  'Choisir un mois',
                  'Sélectionnez le mois à analyser :',
                  [
                    ...months.slice(0, 20).map(month => ({
                      text: formatMonth(month),
                      onPress: () => setSelectedMonth(month)
                    })),
                    { text: 'Voir plus...', onPress: () => showAllMonths() },
                    { text: 'Annuler', style: 'cancel' }
                  ]
                );
              }}
              activeOpacity={0.7}
            >
              <Text style={styles.selectorText}>{formatMonth(selectedMonth)}</Text>
              <Text style={styles.selectorArrow}>🗓️</Text>
            </TouchableOpacity>
          </View>
        );

      case 'year':
        return (
          <View style={styles.selectorContainer}>
            <Text style={styles.selectorLabel}>📆 Année sélectionnée :</Text>
            <TouchableOpacity
              style={styles.selector}
              onPress={() => {
                const years = generateAvailableYears();
                Alert.alert(
                  'Choisir une année',
                  'Sélectionnez l\'année à analyser :',
                  [
                    ...years.map(year => ({
                      text: year,
                      onPress: () => setSelectedYear(year)
                    })),
                    { text: 'Annuler', style: 'cancel' }
                  ]
                );
              }}
            >
              <Text style={styles.selectorText}>{selectedYear}</Text>
              <Text style={styles.selectorArrow}>📆</Text>
            </TouchableOpacity>
          </View>
        );

      case 'week':
        return (
          <View style={styles.selectorContainer}>
            <Text style={styles.selectorLabel}>📊 Semaine sélectionnée :</Text>
            <TouchableOpacity
              style={styles.selector}
              onPress={() => {
                const weeks = generateAvailableWeeks();
                Alert.alert(
                  'Choisir une semaine',
                  'Sélectionnez la semaine à analyser :',
                  [
                    ...weeks.slice(0, 20).map(week => ({
                      text: formatWeek(week),
                      onPress: () => setSelectedWeek(week)
                    })),
                    { text: 'Voir plus...', onPress: () => showAllWeeks() },
                    { text: 'Annuler', style: 'cancel' }
                  ]
                );
              }}
            >
              <Text style={styles.selectorText}>{selectedWeek ? formatWeek(selectedWeek) : 'Sélectionner...'}</Text>
              <Text style={styles.selectorArrow}>📊</Text>
            </TouchableOpacity>
          </View>
        );

      case 'custom':
        return (
          <View style={styles.customPeriodContainer}>
            <Text style={styles.selectorLabel}>🎯 Période personnalisée :</Text>
            <View style={styles.dateRangeContainer}>
              <View style={styles.dateInputContainer}>
                <Text style={styles.dateInputLabel}>Du :</Text>
                <TouchableOpacity
                  style={styles.dateInput}
                  onPress={() => showDatePicker('start')}
                >
                  <Text style={styles.dateInputText}>
                    {new Date(customStartDate).toLocaleDateString('fr-FR')}
                  </Text>
                </TouchableOpacity>
              </View>

              <View style={styles.dateInputContainer}>
                <Text style={styles.dateInputLabel}>Au :</Text>
                <TouchableOpacity
                  style={styles.dateInput}
                  onPress={() => showDatePicker('end')}
                >
                  <Text style={styles.dateInputText}>
                    {new Date(customEndDate).toLocaleDateString('fr-FR')}
                  </Text>
                </TouchableOpacity>
              </View>
            </View>
          </View>
        );

      default:
        return null;
    }
  };

  const showAllWeeks = () => {
    const weeks = generateAvailableWeeks();
    const weeksPerPage = 20;
    const totalPages = Math.ceil(weeks.length / weeksPerPage);

    const showPage = (page: number) => {
      const startIndex = page * weeksPerPage;
      const endIndex = Math.min(startIndex + weeksPerPage, weeks.length);
      const pageWeeks = weeks.slice(startIndex, endIndex);

      const buttons = [
        ...pageWeeks.map(week => ({
          text: formatWeek(week),
          onPress: () => setSelectedWeek(week)
        })),
        ...(page > 0 ? [{ text: '← Page précédente', onPress: () => showPage(page - 1) }] : []),
        ...(page < totalPages - 1 ? [{ text: 'Page suivante →', onPress: () => showPage(page + 1) }] : []),
        { text: 'Annuler', style: 'cancel' as const }
      ];

      Alert.alert(
        `Choisir une semaine (Page ${page + 1}/${totalPages})`,
        'Sélectionnez la semaine à analyser :',
        buttons
      );
    };

    showPage(0);
  };

  const showDatePicker = (type: 'start' | 'end') => {
    // Pour simplifier, on utilise un prompt pour saisir la date
    // Dans une vraie app, on utiliserait un DatePicker natif
    Alert.prompt(
      `Sélectionner la date ${type === 'start' ? 'de début' : 'de fin'}`,
      'Format: YYYY-MM-DD (ex: 2025-07-21)',
      (text) => {
        if (text && /^\d{4}-\d{2}-\d{2}$/.test(text)) {
          if (type === 'start') {
            setCustomStartDate(text);
          } else {
            setCustomEndDate(text);
          }
        } else {
          Alert.alert('Erreur', 'Format de date invalide. Utilisez YYYY-MM-DD');
        }
      },
      'plain-text',
      type === 'start' ? customStartDate : customEndDate
    );
  };

  const getFilterTitle = () => {
    switch (filterType) {
      case 'month':
        return formatMonth(selectedMonth);
      case 'year':
        return selectedYear;
      case 'week':
        return selectedWeek ? formatWeek(selectedWeek) : 'Semaine non sélectionnée';
      case 'custom':
        return `${new Date(customStartDate).toLocaleDateString('fr-FR')} - ${new Date(customEndDate).toLocaleDateString('fr-FR')}`;
      default:
        return 'Période non définie';
    }
  };

  const updateStockValue = (itemId: string, field: 'stockInitial' | 'entrees' | 'stockFinal', value: string) => {
    if (!stockData) return;

    // Remplacer la virgule par un point pour la conversion numérique
    const cleanValue = value.replace(',', '.');
    const numValue = parseFloat(cleanValue) || 0;
    
    const updatedItems = stockData.items.map(item => {
      if (item.id === itemId) {
        return { ...item, [field]: numValue };
      }
      return item;
    });

    setStockData({ ...stockData, items: updatedItems });
  };

  const handleSaveStock = async () => {
    if (!stockData) return;

    try {
      console.log(`💾 Sauvegarde du stock pour ${stockData.date}`);

      // Sauvegarder le stock actuel
      await StockService.saveStockForDate(stockData);

      // Synchroniser automatiquement avec le jour suivant
      const nextDate = new Date(selectedDate);
      nextDate.setDate(nextDate.getDate() + 1);
      const nextDateStr = nextDate.toISOString().split('T')[0];

      console.log(`🔄 Synchronisation ${stockData.date} → ${nextDateStr}`);
      await StockService.forceSyncBetweenDays(stockData.date, nextDateStr);

      // Recharger les données pour afficher les valeurs sauvegardées
      await loadStock(selectedDate);

      Alert.alert(
        'Succès',
        `Stock sauvegardé pour ${new Date(selectedDate).toLocaleDateString('fr-FR')} et synchronisé avec le lendemain`,
        [{ text: 'OK' }]
      );
    } catch (error) {
      console.error('Erreur lors de la sauvegarde:', error);
      Alert.alert('Erreur', 'Impossible de sauvegarder le stock. Vérifiez vos données.');
    }
  };

  const changeDate = async (direction: 'prev' | 'next') => {
    try {
      const currentDate = new Date(selectedDate);
      if (direction === 'prev') {
        currentDate.setDate(currentDate.getDate() - 1);
      } else {
        currentDate.setDate(currentDate.getDate() + 1);
      }

      const newDateStr = currentDate.toISOString().split('T')[0];
      const dateFormatted = currentDate.toLocaleDateString('fr-FR', {
        weekday: 'long',
        day: 'numeric',
        month: 'long'
      });

      console.log(`📅 Navigation vers ${direction === 'prev' ? 'hier' : 'demain'}: ${dateFormatted}`);

      // Changer la date (cela déclenchera useEffect qui chargera le stock)
      setSelectedDate(newDateStr);
    } catch (error) {
      console.error('Erreur lors du changement de date:', error);
      Alert.alert('Erreur', 'Impossible de changer de date');
    }
  };

  const formatValue = (value: number): string => {
    return value > 0 ? value.toString().replace('.', ',') : '';
  };

  if (loading) {
    return (
      <View style={[styles.container, styles.centered]}>
        <Text style={styles.loadingText}>Chargement du stock...</Text>
      </View>
    );
  }

  return (
    <KeyboardAvoidingView 
      style={styles.container} 
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
    >
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity onPress={() => navigation.goBack()} style={styles.backButton}>
          <Text style={styles.backButtonText}>← Retour</Text>
        </TouchableOpacity>
        <Text style={styles.title}>
          {currentView === 'stock' ? 'Stock du Jour' : 'Consommation'}
        </Text>
        {currentView === 'stock' && (
          <TouchableOpacity style={styles.saveButton} onPress={handleSaveStock}>
            <Text style={styles.saveButtonText}>💾 Sauver</Text>
          </TouchableOpacity>
        )}
      </View>

      {/* Onglets */}
      <View style={styles.tabContainer}>
        <TouchableOpacity
          style={[styles.tab, currentView === 'stock' && styles.activeTab]}
          onPress={() => setCurrentView('stock')}
          activeOpacity={0.7}
        >
          <Text style={[styles.tabText, currentView === 'stock' && styles.activeTabText]}>
            📦 Stock Quotidien
          </Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.tab, currentView === 'consumption' && styles.activeTab]}
          onPress={() => setCurrentView('consumption')}
          activeOpacity={0.7}
        >
          <Text style={[styles.tabText, currentView === 'consumption' && styles.activeTabText]}>
            📊 Consommation
          </Text>
        </TouchableOpacity>
      </View>

      {currentView === 'stock' ? (
        /* Navigation de date pour stock */
        <View style={styles.dateNavigation}>
          <TouchableOpacity style={styles.dateButton} onPress={() => changeDate('prev')} activeOpacity={0.7}>
            <Text style={styles.dateButtonText}>← Hier</Text>
          </TouchableOpacity>

          <View style={styles.dateContainer}>
            <Text style={styles.dateText}>
              {new Date(selectedDate).toLocaleDateString('fr-FR', {
                weekday: 'long',
                day: 'numeric',
                month: 'long'
              })}
            </Text>
            {stockData && (
              <Text style={styles.stockInfo}>
                {stockData.items.filter(item => item.stockInitial > 0).length > 0 && (
                  <Text style={styles.syncIndicator}>🔄 Synchronisé</Text>
                )}
                {stockData.items.filter(item => item.stockFinal > 0).length > 0 && (
                  <Text style={styles.dataIndicator}> • 📊 Données saisies</Text>
                )}
              </Text>
            )}
          </View>

          <TouchableOpacity style={styles.dateButton} onPress={() => changeDate('next')} activeOpacity={0.7}>
            <Text style={styles.dateButtonText}>Demain →</Text>
          </TouchableOpacity>
        </View>
      ) : (
        /* Filtrage avancé pour consommation */
        <View style={styles.filterContainer}>
          {/* Sélecteur de type de filtrage */}
          <View style={styles.filterTypeContainer}>
            <Text style={styles.filterLabel}>� Type de filtrage :</Text>
            <View style={styles.filterTypeButtons}>
              <TouchableOpacity
                style={[styles.filterTypeButton, filterType === 'month' && styles.activeFilterType]}
                onPress={() => setFilterType('month')}
                activeOpacity={0.7}
              >
                <Text style={[styles.filterTypeText, filterType === 'month' && styles.activeFilterTypeText]}>
                  📅 Mois
                </Text>
              </TouchableOpacity>

              <TouchableOpacity
                style={[styles.filterTypeButton, filterType === 'year' && styles.activeFilterType]}
                onPress={() => setFilterType('year')}
                activeOpacity={0.7}
              >
                <Text style={[styles.filterTypeText, filterType === 'year' && styles.activeFilterTypeText]}>
                  📆 Année
                </Text>
              </TouchableOpacity>

              <TouchableOpacity
                style={[styles.filterTypeButton, filterType === 'week' && styles.activeFilterType]}
                onPress={() => setFilterType('week')}
                activeOpacity={0.7}
              >
                <Text style={[styles.filterTypeText, filterType === 'week' && styles.activeFilterTypeText]}>
                  📊 Semaine
                </Text>
              </TouchableOpacity>

              <TouchableOpacity
                style={[styles.filterTypeButton, filterType === 'custom' && styles.activeFilterType]}
                onPress={() => setFilterType('custom')}
                activeOpacity={0.7}
              >
                <Text style={[styles.filterTypeText, filterType === 'custom' && styles.activeFilterTypeText]}>
                  🎯 Période
                </Text>
              </TouchableOpacity>
            </View>
          </View>

          {/* Sélecteur spécifique selon le type */}
          {renderFilterSelector()}
        </View>
      )}

      {/* Contenu principal */}
      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {currentView === 'stock' ? (
          /* Vue Stock Quotidien */
          <>
        {/* En-tête du tableau */}
        <View style={styles.tableHeader}>
          <Text style={styles.headerText}>Produit</Text>
          <Text style={styles.headerText}>Stock Initial</Text>
          <Text style={styles.headerText}>Entrées</Text>
          <Text style={styles.headerText}>Stock Final</Text>
        </View>

        {/* Lignes du tableau */}
        {stockData?.items.map(item => (
          <View key={item.id} style={styles.tableRow}>
            {/* Nom du produit */}
            <View style={styles.productCell}>
              <Text style={styles.productName}>{item.name}</Text>
              <Text style={styles.productUnit}>({item.unit})</Text>
            </View>
            
            {/* Stock Initial */}
            <View style={styles.inputCell}>
              <TextInput
                style={styles.input}
                value={formatValue(item.stockInitial)}
                onChangeText={(text) => updateStockValue(item.id, 'stockInitial', text)}
                keyboardType="numeric"
                placeholder="0"
              />
            </View>
            
            {/* Entrées */}
            <View style={styles.inputCell}>
              <TextInput
                style={styles.input}
                value={formatValue(item.entrees)}
                onChangeText={(text) => updateStockValue(item.id, 'entrees', text)}
                keyboardType="numeric"
                placeholder="0"
              />
            </View>
            
            {/* Stock Final */}
            <View style={styles.inputCell}>
              <TextInput
                style={styles.input}
                value={formatValue(item.stockFinal)}
                onChangeText={(text) => updateStockValue(item.id, 'stockFinal', text)}
                keyboardType="numeric"
                placeholder="0"
              />
            </View>
          </View>
        ))}
          </>
        ) : (
          /* Vue Consommation */
          <>
            <View style={styles.consumptionHeader}>
              <Text style={styles.consumptionTitle}>📊 Consommation - {getFilterTitle()}</Text>
              <Text style={styles.consumptionSubtitle}>Analyse des produits consommés durant la période</Text>
            </View>

            {loading ? (
              <View style={styles.centered}>
                <Text style={styles.loadingText}>Calcul de la consommation...</Text>
              </View>
            ) : consumptionData.length > 0 ? (
              <View style={styles.consumptionTable}>
                {/* En-tête du tableau */}
                <View style={styles.consumptionTableHeader}>
                  <Text style={styles.consumptionHeaderText}>Produit</Text>
                  <Text style={styles.consumptionHeaderText}>Consommation</Text>
                  <Text style={styles.consumptionHeaderText}>Catégorie</Text>
                </View>

                {/* Lignes du tableau */}
                {consumptionData.map((item, index) => (
                  <View key={index} style={styles.consumptionRow}>
                    <View style={styles.consumptionProductCell}>
                      <Text style={styles.consumptionProductName}>{item.name}</Text>
                    </View>
                    <View style={styles.consumptionValueCell}>
                      <Text style={styles.consumptionValue}>
                        {item.totalConsumption.toFixed(3)} {item.unit}
                      </Text>
                    </View>
                    <View style={styles.consumptionCategoryCell}>
                      <Text style={styles.consumptionCategory}>{item.category}</Text>
                    </View>
                  </View>
                ))}
              </View>
            ) : (
              <View style={styles.noDataContainer}>
                <Text style={styles.noDataIcon}>📊</Text>
                <Text style={styles.noDataTitle}>Aucune consommation</Text>
                <Text style={styles.noDataText}>
                  Aucune donnée de consommation trouvée pour {formatMonth(selectedMonth)}.
                  Assurez-vous d'avoir saisi les stocks quotidiens.
                </Text>
              </View>
            )}
          </>
        )}
      </ScrollView>
    </KeyboardAvoidingView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
  },
  centered: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    fontSize: FontSizes.lg,
    color: Colors.textSecondary,
  },
  header: {
    backgroundColor: Colors.secondary,
    paddingTop: 50,
    paddingBottom: 15,
    paddingHorizontal: 16,
    flexDirection: 'row',
    alignItems: 'center',
  },
  backButton: {
    marginRight: 10,
  },
  backButtonText: {
    color: Colors.textOnDark,
    fontSize: FontSizes.md,
    fontWeight: '600',
  },
  title: {
    fontSize: FontSizes.xl,
    fontWeight: 'bold',
    color: Colors.textOnDark,
    flex: 1,
  },
  saveButton: {
    backgroundColor: Colors.success,
    paddingHorizontal: 16,
    paddingVertical: 10,
    borderRadius: BorderRadius.medium,
  },
  saveButtonText: {
    color: Colors.textOnPrimary,
    fontSize: FontSizes.md,
    fontWeight: '600',
  },
  dateNavigation: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: Spacing.md,
    paddingVertical: Spacing.sm,
    backgroundColor: Colors.surface,
    borderBottomWidth: 1,
    borderBottomColor: Colors.border,
  },
  dateButton: {
    backgroundColor: Colors.light,
    paddingHorizontal: Spacing.md,
    paddingVertical: Spacing.sm,
    borderRadius: BorderRadius.medium,
    borderWidth: 1,
    borderColor: Colors.border,
  },
  dateButtonText: {
    fontSize: FontSizes.sm,
    fontWeight: '600',
    color: Colors.textPrimary,
  },
  dateContainer: {
    alignItems: 'center',
    flex: 1,
    marginHorizontal: Spacing.md,
  },
  dateText: {
    fontSize: FontSizes.md,
    fontWeight: 'bold',
    color: Colors.textPrimary,
    textAlign: 'center',
    textTransform: 'capitalize',
  },
  stockInfo: {
    fontSize: FontSizes.xs,
    color: Colors.textSecondary,
    textAlign: 'center',
    marginTop: 2,
  },
  syncIndicator: {
    color: Colors.success,
    fontSize: FontSizes.xs,
  },
  dataIndicator: {
    color: Colors.primary,
    fontSize: FontSizes.xs,
  },
  content: {
    flex: 1,
    padding: Spacing.md,
  },
  tableHeader: {
    flexDirection: 'row',
    backgroundColor: Colors.primary,
    paddingVertical: Spacing.md,
    borderRadius: BorderRadius.medium,
    marginBottom: Spacing.sm,
  },
  headerText: {
    flex: 1,
    color: Colors.textOnPrimary,
    fontSize: FontSizes.sm,
    fontWeight: 'bold',
    textAlign: 'center',
  },
  tableRow: {
    flexDirection: 'row',
    backgroundColor: Colors.surface,
    paddingVertical: Spacing.sm,
    marginBottom: Spacing.xs,
    borderRadius: BorderRadius.medium,
    borderWidth: 1,
    borderColor: Colors.border,
    alignItems: 'center',
  },
  productCell: {
    flex: 1,
    paddingHorizontal: Spacing.sm,
  },
  productName: {
    fontSize: FontSizes.sm,
    fontWeight: '600',
    color: Colors.textPrimary,
  },
  productUnit: {
    fontSize: FontSizes.xs,
    color: Colors.textSecondary,
  },
  inputCell: {
    flex: 1,
    paddingHorizontal: Spacing.xs,
  },
  input: {
    backgroundColor: Colors.light,
    borderRadius: BorderRadius.small,
    paddingHorizontal: Spacing.sm,
    paddingVertical: Spacing.sm,
    fontSize: FontSizes.md,
    borderWidth: 1,
    borderColor: Colors.border,
    textAlign: 'center',
  },

  // Styles pour les onglets
  tabContainer: {
    flexDirection: 'row',
    backgroundColor: Colors.surface,
    borderBottomWidth: 1,
    borderBottomColor: Colors.borderLight,
  },
  tab: {
    flex: 1,
    paddingVertical: Spacing.md,
    paddingHorizontal: Spacing.lg,
    alignItems: 'center',
    borderBottomWidth: 2,
    borderBottomColor: 'transparent',
  },
  activeTab: {
    borderBottomColor: Colors.primary,
  },
  tabText: {
    fontSize: FontSizes.md,
    color: Colors.textSecondary,
    fontWeight: '500',
  },
  activeTabText: {
    color: Colors.primary,
    fontWeight: 'bold',
  },

  // Styles pour le filtrage par mois
  monthFilter: {
    backgroundColor: Colors.surface,
    padding: Spacing.lg,
    borderBottomWidth: 1,
    borderBottomColor: Colors.borderLight,
  },
  monthLabel: {
    fontSize: FontSizes.sm,
    color: Colors.textSecondary,
    marginBottom: Spacing.sm,
  },
  monthSelector: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    backgroundColor: Colors.light,
    padding: Spacing.md,
    borderRadius: BorderRadius.medium,
    borderWidth: 1,
    borderColor: Colors.borderLight,
  },
  monthText: {
    fontSize: FontSizes.md,
    fontWeight: 'bold',
    color: Colors.textPrimary,
  },
  monthArrow: {
    fontSize: FontSizes.lg,
  },

  // Styles pour la vue consommation
  consumptionHeader: {
    backgroundColor: Colors.surface,
    padding: Spacing.lg,
    borderBottomWidth: 1,
    borderBottomColor: Colors.borderLight,
    alignItems: 'center',
  },
  consumptionTitle: {
    fontSize: FontSizes.lg,
    fontWeight: 'bold',
    color: Colors.textPrimary,
    marginBottom: Spacing.xs,
  },
  consumptionSubtitle: {
    fontSize: FontSizes.sm,
    color: Colors.textSecondary,
    textAlign: 'center',
  },
  consumptionTable: {
    backgroundColor: Colors.surface,
    margin: Spacing.md,
    borderRadius: BorderRadius.medium,
    ...Shadows.small,
  },
  consumptionTableHeader: {
    flexDirection: 'row',
    backgroundColor: Colors.primary,
    paddingVertical: Spacing.md,
    paddingHorizontal: Spacing.sm,
    borderTopLeftRadius: BorderRadius.medium,
    borderTopRightRadius: BorderRadius.medium,
  },
  consumptionHeaderText: {
    flex: 1,
    fontSize: FontSizes.sm,
    fontWeight: 'bold',
    color: Colors.textOnPrimary,
    textAlign: 'center',
  },
  consumptionRow: {
    flexDirection: 'row',
    paddingVertical: Spacing.md,
    paddingHorizontal: Spacing.sm,
    borderBottomWidth: 1,
    borderBottomColor: Colors.borderLight,
  },
  consumptionProductCell: {
    flex: 1,
    justifyContent: 'center',
  },
  consumptionProductName: {
    fontSize: FontSizes.sm,
    fontWeight: '600',
    color: Colors.textPrimary,
  },
  consumptionValueCell: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  consumptionValue: {
    fontSize: FontSizes.sm,
    fontWeight: 'bold',
    color: Colors.primary,
  },
  consumptionCategoryCell: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  consumptionCategory: {
    fontSize: FontSizes.xs,
    color: Colors.textSecondary,
    backgroundColor: Colors.light,
    paddingHorizontal: Spacing.sm,
    paddingVertical: Spacing.xs,
    borderRadius: BorderRadius.small,
  },
  noDataContainer: {
    backgroundColor: Colors.surface,
    margin: Spacing.lg,
    padding: Spacing.xl,
    borderRadius: BorderRadius.medium,
    alignItems: 'center',
    ...Shadows.small,
  },
  noDataIcon: {
    fontSize: 48,
    marginBottom: Spacing.md,
  },
  noDataTitle: {
    fontSize: FontSizes.lg,
    fontWeight: 'bold',
    color: Colors.textPrimary,
    marginBottom: Spacing.sm,
  },
  noDataText: {
    fontSize: FontSizes.sm,
    color: Colors.textSecondary,
    textAlign: 'center',
    lineHeight: 20,
  },

  // Styles pour le filtrage avancé
  filterContainer: {
    backgroundColor: Colors.surface,
    padding: Spacing.lg,
    borderBottomWidth: 1,
    borderBottomColor: Colors.borderLight,
  },
  filterTypeContainer: {
    marginBottom: Spacing.md,
  },
  filterLabel: {
    fontSize: FontSizes.sm,
    color: Colors.textSecondary,
    marginBottom: Spacing.sm,
  },
  filterTypeButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  filterTypeButton: {
    flex: 1,
    paddingVertical: Spacing.sm,
    paddingHorizontal: Spacing.xs,
    marginHorizontal: Spacing.xs,
    backgroundColor: Colors.light,
    borderRadius: BorderRadius.medium,
    borderWidth: 1,
    borderColor: Colors.borderLight,
    alignItems: 'center',
  },
  activeFilterType: {
    backgroundColor: Colors.primary,
    borderColor: Colors.primary,
  },
  filterTypeText: {
    fontSize: FontSizes.xs,
    color: Colors.textSecondary,
    fontWeight: '500',
    textAlign: 'center',
  },
  activeFilterTypeText: {
    color: Colors.textOnPrimary,
    fontWeight: 'bold',
  },

  // Styles pour les sélecteurs
  selectorContainer: {
    marginTop: Spacing.md,
  },
  selectorLabel: {
    fontSize: FontSizes.sm,
    color: Colors.textSecondary,
    marginBottom: Spacing.sm,
  },
  selector: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    backgroundColor: Colors.light,
    padding: Spacing.md,
    borderRadius: BorderRadius.medium,
    borderWidth: 1,
    borderColor: Colors.borderLight,
  },
  selectorText: {
    fontSize: FontSizes.md,
    fontWeight: 'bold',
    color: Colors.textPrimary,
  },
  selectorArrow: {
    fontSize: FontSizes.lg,
  },

  // Styles pour la période personnalisée
  customPeriodContainer: {
    marginTop: Spacing.md,
  },
  dateRangeContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  dateInputContainer: {
    flex: 1,
    marginHorizontal: Spacing.xs,
  },
  dateInputLabel: {
    fontSize: FontSizes.sm,
    color: Colors.textSecondary,
    marginBottom: Spacing.xs,
  },
  dateInput: {
    backgroundColor: Colors.light,
    padding: Spacing.md,
    borderRadius: BorderRadius.medium,
    borderWidth: 1,
    borderColor: Colors.borderLight,
    alignItems: 'center',
  },
  dateInputText: {
    fontSize: FontSizes.sm,
    fontWeight: '600',
    color: Colors.textPrimary,
  },
});
