import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  TextInput,
  Alert,
  KeyboardAvoidingView,
  Platform,
} from 'react-native';
import { Colors, FontSizes, Spacing, BorderRadius } from '../styles/theme';
import { StockService, DailyStock } from '../services/StockService';

interface StockScreenProps {
  navigation: any;
}

interface StockItem {
  id: string;
  name: string;
  unit: string;
  stockInitial: number;
  entrees: number;
  stockFinal: number;
}

export const StockScreen: React.FC<StockScreenProps> = ({ navigation }) => {
  const [stockData, setStockData] = useState<DailyStock | null>(null);
  const [selectedDate, setSelectedDate] = useState<string>(new Date().toISOString().split('T')[0]);
  const [loading, setLoading] = useState<boolean>(true);

  useEffect(() => {
    loadStock(selectedDate);
  }, [selectedDate]);

  const loadStock = async (date: string) => {
    try {
      setLoading(true);
      const stock = await StockService.getStockForDate(date);
      setStockData(stock);

      // Log pour debug
      console.log(`📅 Stock chargé pour ${date}:`, {
        nombreItems: stock.items.length,
        finalized: stock.isFinalized,
        exempleItem: stock.items[0] ? {
          name: stock.items[0].name,
          initial: stock.items[0].stockInitial,
          entrees: stock.items[0].entrees,
          final: stock.items[0].stockFinal
        } : null
      });
    } catch (error) {
      console.error('Erreur lors du chargement du stock:', error);
      Alert.alert('Erreur', 'Impossible de charger le stock');
    } finally {
      setLoading(false);
    }
  };

  const updateStockValue = (itemId: string, field: 'stockInitial' | 'entrees' | 'stockFinal', value: string) => {
    if (!stockData) return;

    // Remplacer la virgule par un point pour la conversion numérique
    const cleanValue = value.replace(',', '.');
    const numValue = parseFloat(cleanValue) || 0;
    
    const updatedItems = stockData.items.map(item => {
      if (item.id === itemId) {
        return { ...item, [field]: numValue };
      }
      return item;
    });

    setStockData({ ...stockData, items: updatedItems });
  };

  const handleSaveStock = async () => {
    if (!stockData) return;

    try {
      console.log(`💾 Sauvegarde du stock pour ${stockData.date}`);

      // Sauvegarder le stock actuel
      await StockService.saveStockForDate(stockData);

      // Synchroniser automatiquement avec le jour suivant
      const nextDate = new Date(selectedDate);
      nextDate.setDate(nextDate.getDate() + 1);
      const nextDateStr = nextDate.toISOString().split('T')[0];

      console.log(`🔄 Synchronisation ${stockData.date} → ${nextDateStr}`);
      await StockService.forceSyncBetweenDays(stockData.date, nextDateStr);

      // Recharger les données pour afficher les valeurs sauvegardées
      await loadStock(selectedDate);

      Alert.alert(
        'Succès',
        `Stock sauvegardé pour ${new Date(selectedDate).toLocaleDateString('fr-FR')} et synchronisé avec le lendemain`,
        [{ text: 'OK' }]
      );
    } catch (error) {
      console.error('Erreur lors de la sauvegarde:', error);
      Alert.alert('Erreur', 'Impossible de sauvegarder le stock. Vérifiez vos données.');
    }
  };

  const changeDate = async (direction: 'prev' | 'next') => {
    try {
      const currentDate = new Date(selectedDate);
      if (direction === 'prev') {
        currentDate.setDate(currentDate.getDate() - 1);
      } else {
        currentDate.setDate(currentDate.getDate() + 1);
      }

      const newDateStr = currentDate.toISOString().split('T')[0];
      const dateFormatted = currentDate.toLocaleDateString('fr-FR', {
        weekday: 'long',
        day: 'numeric',
        month: 'long'
      });

      console.log(`📅 Navigation vers ${direction === 'prev' ? 'hier' : 'demain'}: ${dateFormatted}`);

      // Changer la date (cela déclenchera useEffect qui chargera le stock)
      setSelectedDate(newDateStr);
    } catch (error) {
      console.error('Erreur lors du changement de date:', error);
      Alert.alert('Erreur', 'Impossible de changer de date');
    }
  };

  const formatValue = (value: number): string => {
    return value > 0 ? value.toString().replace('.', ',') : '';
  };

  if (loading) {
    return (
      <View style={[styles.container, styles.centered]}>
        <Text style={styles.loadingText}>Chargement du stock...</Text>
      </View>
    );
  }

  return (
    <KeyboardAvoidingView 
      style={styles.container} 
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
    >
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity onPress={() => navigation.goBack()} style={styles.backButton}>
          <Text style={styles.backButtonText}>← Retour</Text>
        </TouchableOpacity>
        <Text style={styles.title}>Stock du Jour</Text>
        <TouchableOpacity style={styles.saveButton} onPress={handleSaveStock}>
          <Text style={styles.saveButtonText}>💾 Sauver</Text>
        </TouchableOpacity>
      </View>

      {/* Navigation de date */}
      <View style={styles.dateNavigation}>
        <TouchableOpacity style={styles.dateButton} onPress={() => changeDate('prev')}>
          <Text style={styles.dateButtonText}>← Hier</Text>
        </TouchableOpacity>
        
        <View style={styles.dateContainer}>
          <Text style={styles.dateText}>
            {new Date(selectedDate).toLocaleDateString('fr-FR', {
              weekday: 'long',
              day: 'numeric',
              month: 'long'
            })}
          </Text>
          {stockData && (
            <Text style={styles.stockInfo}>
              {stockData.items.filter(item => item.stockInitial > 0).length > 0 && (
                <Text style={styles.syncIndicator}>🔄 Synchronisé</Text>
              )}
              {stockData.items.filter(item => item.stockFinal > 0).length > 0 && (
                <Text style={styles.dataIndicator}> • 📊 Données saisies</Text>
              )}
            </Text>
          )}
        </View>
        
        <TouchableOpacity style={styles.dateButton} onPress={() => changeDate('next')}>
          <Text style={styles.dateButtonText}>Demain →</Text>
        </TouchableOpacity>
      </View>

      {/* Tableau de stock */}
      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* En-tête du tableau */}
        <View style={styles.tableHeader}>
          <Text style={styles.headerText}>Produit</Text>
          <Text style={styles.headerText}>Stock Initial</Text>
          <Text style={styles.headerText}>Entrées</Text>
          <Text style={styles.headerText}>Stock Final</Text>
        </View>

        {/* Lignes du tableau */}
        {stockData?.items.map(item => (
          <View key={item.id} style={styles.tableRow}>
            {/* Nom du produit */}
            <View style={styles.productCell}>
              <Text style={styles.productName}>{item.name}</Text>
              <Text style={styles.productUnit}>({item.unit})</Text>
            </View>
            
            {/* Stock Initial */}
            <View style={styles.inputCell}>
              <TextInput
                style={styles.input}
                value={formatValue(item.stockInitial)}
                onChangeText={(text) => updateStockValue(item.id, 'stockInitial', text)}
                keyboardType="numeric"
                placeholder="0"
              />
            </View>
            
            {/* Entrées */}
            <View style={styles.inputCell}>
              <TextInput
                style={styles.input}
                value={formatValue(item.entrees)}
                onChangeText={(text) => updateStockValue(item.id, 'entrees', text)}
                keyboardType="numeric"
                placeholder="0"
              />
            </View>
            
            {/* Stock Final */}
            <View style={styles.inputCell}>
              <TextInput
                style={styles.input}
                value={formatValue(item.stockFinal)}
                onChangeText={(text) => updateStockValue(item.id, 'stockFinal', text)}
                keyboardType="numeric"
                placeholder="0"
              />
            </View>
          </View>
        ))}
      </ScrollView>
    </KeyboardAvoidingView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
  },
  centered: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    fontSize: FontSizes.lg,
    color: Colors.textSecondary,
  },
  header: {
    backgroundColor: Colors.secondary,
    paddingTop: 50,
    paddingBottom: 15,
    paddingHorizontal: 16,
    flexDirection: 'row',
    alignItems: 'center',
  },
  backButton: {
    marginRight: 10,
  },
  backButtonText: {
    color: Colors.textOnDark,
    fontSize: FontSizes.md,
    fontWeight: '600',
  },
  title: {
    fontSize: FontSizes.xl,
    fontWeight: 'bold',
    color: Colors.textOnDark,
    flex: 1,
  },
  saveButton: {
    backgroundColor: Colors.success,
    paddingHorizontal: 16,
    paddingVertical: 10,
    borderRadius: BorderRadius.medium,
  },
  saveButtonText: {
    color: Colors.textOnPrimary,
    fontSize: FontSizes.md,
    fontWeight: '600',
  },
  dateNavigation: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: Spacing.md,
    paddingVertical: Spacing.sm,
    backgroundColor: Colors.surface,
    borderBottomWidth: 1,
    borderBottomColor: Colors.border,
  },
  dateButton: {
    backgroundColor: Colors.light,
    paddingHorizontal: Spacing.md,
    paddingVertical: Spacing.sm,
    borderRadius: BorderRadius.medium,
    borderWidth: 1,
    borderColor: Colors.border,
  },
  dateButtonText: {
    fontSize: FontSizes.sm,
    fontWeight: '600',
    color: Colors.textPrimary,
  },
  dateContainer: {
    alignItems: 'center',
    flex: 1,
    marginHorizontal: Spacing.md,
  },
  dateText: {
    fontSize: FontSizes.md,
    fontWeight: 'bold',
    color: Colors.textPrimary,
    textAlign: 'center',
    textTransform: 'capitalize',
  },
  stockInfo: {
    fontSize: FontSizes.xs,
    color: Colors.textSecondary,
    textAlign: 'center',
    marginTop: 2,
  },
  syncIndicator: {
    color: Colors.success,
    fontSize: FontSizes.xs,
  },
  dataIndicator: {
    color: Colors.primary,
    fontSize: FontSizes.xs,
  },
  content: {
    flex: 1,
    padding: Spacing.md,
  },
  tableHeader: {
    flexDirection: 'row',
    backgroundColor: Colors.primary,
    paddingVertical: Spacing.md,
    borderRadius: BorderRadius.medium,
    marginBottom: Spacing.sm,
  },
  headerText: {
    flex: 1,
    color: Colors.textOnPrimary,
    fontSize: FontSizes.sm,
    fontWeight: 'bold',
    textAlign: 'center',
  },
  tableRow: {
    flexDirection: 'row',
    backgroundColor: Colors.surface,
    paddingVertical: Spacing.sm,
    marginBottom: Spacing.xs,
    borderRadius: BorderRadius.medium,
    borderWidth: 1,
    borderColor: Colors.border,
    alignItems: 'center',
  },
  productCell: {
    flex: 1,
    paddingHorizontal: Spacing.sm,
  },
  productName: {
    fontSize: FontSizes.sm,
    fontWeight: '600',
    color: Colors.textPrimary,
  },
  productUnit: {
    fontSize: FontSizes.xs,
    color: Colors.textSecondary,
  },
  inputCell: {
    flex: 1,
    paddingHorizontal: Spacing.xs,
  },
  input: {
    backgroundColor: Colors.light,
    borderRadius: BorderRadius.small,
    paddingHorizontal: Spacing.sm,
    paddingVertical: Spacing.sm,
    fontSize: FontSizes.md,
    borderWidth: 1,
    borderColor: Colors.border,
    textAlign: 'center',
  },
});
