# 🍕 YassinApp - Application de Gestion de Pizzeria

## 📱 Vue d'ensemble

YassinApp est une application mobile complète de gestion de pizzeria développée avec React Native et Expo. Elle offre une interface d'administration moderne et intuitive pour gérer tous les aspects d'une pizzeria.

## ✨ Fonctionnalités

### 🔐 Authentification
- Écran de connexion sécurisé
- Identifiants par défaut : `admin` / `123`
- Interface élégante avec design moderne

### 🍕 Gestion des Pizzas
- Catalogue de pizzas avec images et descriptions
- Différentes tailles disponibles (Petite, Moyenne, Grande, Familiale)
- Système de prix dynamique selon la taille
- Ajout facile au panier

### 🛒 Gestion des Commandes
- Panier flottant avec compteur d'articles
- Formulaire de commande complet
- Informations client (nom, téléphone, adresse)
- Choix du mode de paiement (Espèces, Carte, Mobile)
- Instructions spéciales pour la commande
- Modification des quantités en temps réel

### 📊 Statistiques Avancées
- Vue d'ensemble des performances
- Statistiques par période (jour, semaine, mois)
- Pizzas les plus populaires avec graphiques
- Indicateurs de performance (croissance, satisfaction)
- Chiffre d'affaires et panier moyen
- Actions rapides (export, graphiques détaillés)

### 📦 Gestion du Stock
- Inventaire complet des ingrédients
- Alertes de stock bas automatiques
- Modification en temps réel des quantités
- Catégorisation des produits
- Recherche et filtrage
- Unités de mesure personnalisées
- Mode édition sécurisé

### 🎯 Système de Promotions
- Création de promotions personnalisées
- Types de réductions : pourcentage, montant fixe, achat groupé
- Codes promo automatiques ou personnalisés
- Gestion des dates de validité
- Suivi des utilisations
- Activation/désactivation en temps réel
- Statistiques d'utilisation

## 🎨 Design et Interface

### Thème Moderne
- Palette de couleurs professionnelle
- Design Material Design inspiré
- Animations fluides et transitions
- Interface tactile optimisée

### Responsive Design
- Adaptation automatique aux différentes tailles d'écran
- Navigation intuitive
- Boutons et zones tactiles optimisés
- Feedback visuel immédiat

## 🚀 Installation et Lancement

### Prérequis
- Node.js installé
- Expo CLI
- Expo Go sur votre téléphone (pour les tests)

### Lancement Rapide
```bash
# Méthode 1 : Script automatique
double-cliquez sur start-yassinapp.bat

# Méthode 2 : Commandes manuelles
cd YassinApp
npm install
npx expo start
```

### Identifiants de Test
- **Nom d'utilisateur** : `admin`
- **Mot de passe** : `123`

## 📱 Structure de l'Application

### Écrans Principaux
1. **LoginScreen** - Authentification
2. **AdminScreen** - Tableau de bord principal
3. **OrderFormScreen** - Formulaire de commande
4. **StatisticsScreen** - Statistiques et analyses
5. **StockScreen** - Gestion du stock
6. **PromotionScreen** - Gestion des promotions

### Architecture
```
YassinApp/
├── src/
│   ├── components/          # Composants réutilisables
│   ├── screens/            # Écrans de l'application
│   ├── data/               # Données mock (pizzas)
│   ├── types/              # Types TypeScript
│   ├── styles/             # Thème et styles
│   └── utils/              # Utilitaires
├── assets/                 # Images et icônes
└── App.tsx                 # Point d'entrée
```

## 🔧 Technologies Utilisées

- **React Native** - Framework mobile
- **Expo** - Plateforme de développement
- **TypeScript** - Typage statique
- **React Hooks** - Gestion d'état moderne
- **AsyncStorage** - Stockage local (simulation)

## 📊 Données Mock

L'application utilise des données simulées pour la démonstration :
- 6 pizzas avec différentes catégories
- Statistiques générées dynamiquement
- Stock d'ingrédients réaliste
- Promotions d'exemple

## 🎯 Fonctionnalités Avancées

### Gestion d'État
- État local avec React Hooks
- Navigation entre écrans fluide
- Persistance des données du panier
- Synchronisation en temps réel

### Validation et Sécurité
- Validation des formulaires
- Gestion des erreurs
- Confirmations pour actions critiques
- Données sécurisées

### Expérience Utilisateur
- Feedback immédiat sur les actions
- Messages de confirmation
- Alertes informatives
- Interface intuitive

## 🚀 Prochaines Améliorations

### Fonctionnalités Futures
- [ ] Intégration base de données réelle
- [ ] Notifications push
- [ ] Mode hors ligne
- [ ] Rapports PDF
- [ ] Intégration paiement
- [ ] Géolocalisation livraisons
- [ ] Chat client
- [ ] Système de fidélité

### Optimisations Techniques
- [ ] Cache intelligent
- [ ] Optimisation performances
- [ ] Tests automatisés
- [ ] CI/CD pipeline
- [ ] Monitoring erreurs

## 📞 Support

Pour toute question ou suggestion d'amélioration, n'hésitez pas à nous contacter.

---

**🍕 YassinApp - Votre solution complète de gestion de pizzeria !**
