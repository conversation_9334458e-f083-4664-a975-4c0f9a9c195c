import AsyncStorage from '@react-native-async-storage/async-storage';

export class DataResetService {
  
  // Clés de stockage utilisées dans l'application
  private static readonly STORAGE_KEYS = [
    'orders',           // Commandes
    'statistics',       // Statistiques
    'daily_stats',      // Statistiques quotidiennes
    'stock_data',       // Données de stock
    'promotions',       // Promotions
    'offers',          // Offres
    'user_data',       // Données utilisateur
    'app_settings',    // Paramètres de l'app
    'last_order_number', // Dernier numéro de commande
  ];

  // Vider toutes les données de l'application
  static async clearAllData(): Promise<void> {
    try {
      console.log('🗑️ Début du nettoyage de toutes les données...');
      
      // Supprimer toutes les clés une par une
      for (const key of this.STORAGE_KEYS) {
        try {
          await AsyncStorage.removeItem(key);
          console.log(`✅ Supprimé: ${key}`);
        } catch (error) {
          console.log(`⚠️ Erreur lors de la suppression de ${key}:`, error);
        }
      }

      // Méthode alternative : supprimer toutes les clés existantes
      try {
        const allKeys = await AsyncStorage.getAllKeys();
        if (allKeys.length > 0) {
          await AsyncStorage.multiRemove(allKeys);
          console.log(`🗑️ Suppression alternative: ${allKeys.length} clés supprimées`);
        }
      } catch (error) {
        console.log('⚠️ Erreur lors de la suppression alternative:', error);
      }

      console.log('✅ Nettoyage terminé - Toutes les données supprimées');
      
    } catch (error) {
      console.error('❌ Erreur lors du nettoyage des données:', error);
      throw error;
    }
  }

  // Vider seulement les commandes
  static async clearOrders(): Promise<void> {
    try {
      await AsyncStorage.removeItem('orders');
      await AsyncStorage.removeItem('last_order_number');
      console.log('✅ Commandes supprimées');
    } catch (error) {
      console.error('❌ Erreur lors de la suppression des commandes:', error);
      throw error;
    }
  }

  // Vider seulement les statistiques
  static async clearStatistics(): Promise<void> {
    try {
      await AsyncStorage.removeItem('statistics');
      await AsyncStorage.removeItem('daily_stats');
      console.log('✅ Statistiques supprimées');
    } catch (error) {
      console.error('❌ Erreur lors de la suppression des statistiques:', error);
      throw error;
    }
  }

  // Vider seulement le stock
  static async clearStock(): Promise<void> {
    try {
      await AsyncStorage.removeItem('stock_data');
      console.log('✅ Stock supprimé');
    } catch (error) {
      console.error('❌ Erreur lors de la suppression du stock:', error);
      throw error;
    }
  }

  // Vérifier quelles données existent
  static async checkExistingData(): Promise<{[key: string]: boolean}> {
    const dataStatus: {[key: string]: boolean} = {};
    
    try {
      for (const key of this.STORAGE_KEYS) {
        const data = await AsyncStorage.getItem(key);
        dataStatus[key] = data !== null;
      }
      
      console.log('📊 État des données:', dataStatus);
      return dataStatus;
    } catch (error) {
      console.error('❌ Erreur lors de la vérification des données:', error);
      return {};
    }
  }

  // Obtenir la taille des données stockées
  static async getDataSize(): Promise<{[key: string]: number}> {
    const dataSizes: {[key: string]: number} = {};
    
    try {
      for (const key of this.STORAGE_KEYS) {
        const data = await AsyncStorage.getItem(key);
        dataSizes[key] = data ? data.length : 0;
      }
      
      console.log('📏 Taille des données:', dataSizes);
      return dataSizes;
    } catch (error) {
      console.error('❌ Erreur lors du calcul de la taille:', error);
      return {};
    }
  }

  // Réinitialiser l'application avec des données par défaut
  static async resetToDefault(): Promise<void> {
    try {
      console.log('🔄 Réinitialisation avec données par défaut...');
      
      // Vider toutes les données
      await this.clearAllData();
      
      // Attendre un peu pour s'assurer que le nettoyage est terminé
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Initialiser avec des données par défaut si nécessaire
      await AsyncStorage.setItem('last_order_number', '0');
      
      console.log('✅ Réinitialisation terminée');
    } catch (error) {
      console.error('❌ Erreur lors de la réinitialisation:', error);
      throw error;
    }
  }

  // Créer un rapport de nettoyage
  static async generateCleanupReport(): Promise<string> {
    try {
      const beforeData = await this.checkExistingData();
      const beforeSizes = await this.getDataSize();
      
      await this.clearAllData();
      
      const afterData = await this.checkExistingData();
      const afterSizes = await this.getDataSize();
      
      const report = `
📋 RAPPORT DE NETTOYAGE
======================

AVANT NETTOYAGE:
${Object.entries(beforeData).map(([key, exists]) => 
  `${exists ? '✅' : '❌'} ${key}: ${beforeSizes[key] || 0} caractères`
).join('\n')}

APRÈS NETTOYAGE:
${Object.entries(afterData).map(([key, exists]) => 
  `${exists ? '✅' : '❌'} ${key}: ${afterSizes[key] || 0} caractères`
).join('\n')}

RÉSULTAT: ${Object.values(afterData).every(exists => !exists) ? '✅ NETTOYAGE RÉUSSI' : '⚠️ NETTOYAGE PARTIEL'}
      `;
      
      console.log(report);
      return report;
    } catch (error) {
      console.error('❌ Erreur lors de la génération du rapport:', error);
      return 'Erreur lors de la génération du rapport';
    }
  }
}
