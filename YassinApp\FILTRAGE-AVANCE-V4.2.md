# Filtrage Avancé Consommation - YassinApp V4.2

## 🎯 Nouvelles Fonctionnalités de Filtrage

### **4 Types de Filtrage Disponibles**
- ✅ **📅 Mois** : Analyse mensuelle (2020-2030)
- ✅ **📆 Année** : Analyse annuelle complète
- ✅ **📊 Semaine** : Analyse hebdomadaire précise
- ✅ **🎯 Période** : Période personnalisée (date X à Z)

## 📱 Interface de Filtrage Avancé

### **Sélecteur de Type de Filtrage**
```
🔍 Type de filtrage :
┌─────────────────────────────────────┐
│ 📅 Mois │ 📆 Année │ 📊 Semaine │ 🎯 Période │
└─────────────────────────────────────┘
```

### **1. Filtrage par Mois (Existant Amélioré)**
```
📅 Mois sélectionné :
┌─────────────────────────────────────┐
│ Juillet 2025                    🗓️  │
└─────────────────────────────────────┘

📊 Consommation - Juillet 2025
```

### **2. Filtrage par Année (NOUVEAU)**
```
📆 Année sélectionnée :
┌─────────────────────────────────────┐
│ 2025                            📆  │
└─────────────────────────────────────┘

📊 Consommation - 2025
Analyse des produits consommés durant l'année

┌─────────────────────────────────────┐
│ Produit        │ Consommation │ Catégorie │
├─────────────────────────────────────┤
│ Pâte à pizza   │  546.000 kg  │ Ingrédients│
│ Mozzarella     │  345.000 kg  │ Fromages   │
│ Sauce tomate   │  182.400 L   │ Sauces     │
│ Poulet         │  147.600 kg  │ Viandes    │
│ ...            │              │            │
└─────────────────────────────────────┘
```

### **3. Filtrage par Semaine (NOUVEAU)**
```
📊 Semaine sélectionnée :
┌─────────────────────────────────────┐
│ Semaine 30/2025 (21/7 - 27/7)  📊  │
└─────────────────────────────────────┘

📊 Consommation - Semaine 30/2025 (21/7 - 27/7)
Analyse des produits consommés durant la semaine

┌─────────────────────────────────────┐
│ Produit        │ Consommation │ Catégorie │
├─────────────────────────────────────┤
│ Pâte à pizza   │   10.500 kg  │ Ingrédients│
│ Mozzarella     │    6.750 kg  │ Fromages   │
│ Sauce tomate   │    3.600 L   │ Sauces     │
│ Poulet         │    2.900 kg  │ Viandes    │
│ ...            │              │            │
└─────────────────────────────────────┘
```

### **4. Filtrage par Période Personnalisée (NOUVEAU)**
```
🎯 Période personnalisée :

Du :                    Au :
┌─────────────────┐    ┌─────────────────┐
│   15/07/2025    │    │   25/07/2025    │
└─────────────────┘    └─────────────────┘

📊 Consommation - 15/07/2025 - 25/07/2025
Analyse des produits consommés durant la période

┌─────────────────────────────────────┐
│ Produit        │ Consommation │ Catégorie │
├─────────────────────────────────────┤
│ Pâte à pizza   │   16.500 kg  │ Ingrédients│
│ Mozzarella     │   10.250 kg  │ Fromages   │
│ Sauce tomate   │    5.400 L   │ Sauces     │
│ Poulet         │    4.300 kg  │ Viandes    │
│ ...            │              │            │
└─────────────────────────────────────┘
```

## 🔧 Fonctionnalités Techniques

### **Calcul par Année**
```javascript
const calculateYearlyConsumption = async (year: string) => {
  // Calculer pour tous les mois de l'année (Janvier à Décembre)
  for (let month = 1; month <= 12; month++) {
    const monthStr = `${year}-${month.toString().padStart(2, '0')}`;
    const monthConsumption = await calculateMonthlyConsumption(monthStr);
    
    // Additionner les consommations de chaque mois
    monthConsumption.forEach(item => {
      totalConsumption[item.name] += item.totalConsumption;
    });
  }
};
```

### **Calcul par Semaine**
```javascript
const calculateWeeklyConsumption = async (weekString: string) => {
  // Format: "2025-W30" (année-semaine)
  const [year, weekNum] = weekString.split('-W');
  
  // Calculer les dates de début et fin de semaine
  const startDate = getDateOfWeek(parseInt(year), parseInt(weekNum));
  const endDate = new Date(startDate);
  endDate.setDate(startDate.getDate() + 6); // +6 jours = 7 jours total
  
  // Utiliser le calcul de période personnalisée
  return await calculateCustomPeriodConsumption(startDate, endDate);
};
```

### **Calcul par Période Personnalisée**
```javascript
const calculateCustomPeriodConsumption = async (startDate: string, endDate: string) => {
  const start = new Date(startDate);
  const end = new Date(endDate);
  
  // Parcourir chaque jour de la période
  for (let date = new Date(start); date <= end; date.setDate(date.getDate() + 1)) {
    const dateStr = date.toISOString().split('T')[0];
    
    // Calculer la consommation du jour
    const dayStock = await StockService.getStockForDate(dateStr);
    // Consommation = Stock Initial + Entrées - Stock Final
    const consumption = item.stockInitial + item.entrees - item.stockFinal;
  }
};
```

## 📊 Sélection des Périodes

### **Années Disponibles (2020-2030)**
```
Choisir une année

• 2030
• 2029
• 2028
• 2027
• 2026
• 2025  ← Année actuelle
• 2024
• 2023
• 2022
• 2021
• 2020

[Annuler]
```

### **Semaines Disponibles (5 années)**
```
Choisir une semaine

• Semaine 30/2025 (21/7 - 27/7)  ← Semaine actuelle
• Semaine 29/2025 (14/7 - 20/7)
• Semaine 28/2025 (7/7 - 13/7)
• Semaine 27/2025 (30/6 - 6/7)
...
• Semaine 01/2023 (2/1 - 8/1)

[Voir plus...] [Annuler]
```

### **Période Personnalisée**
```
Sélectionner la date de début
Format: YYYY-MM-DD (ex: 2025-07-21)
[2025-07-15]

Sélectionner la date de fin  
Format: YYYY-MM-DD (ex: 2025-07-21)
[2025-07-25]
```

## 🎯 Utilisation

### **Filtrage par Année**
1. **Cliquer** sur "📆 Année"
2. **Sélectionner** une année (ex: 2024)
3. **Voir** la consommation totale de l'année
4. **Analyser** les tendances annuelles

### **Filtrage par Semaine**
1. **Cliquer** sur "📊 Semaine"
2. **Choisir** une semaine (ex: Semaine 30/2025)
3. **Voir** la consommation de 7 jours
4. **Analyser** les patterns hebdomadaires

### **Filtrage par Période**
1. **Cliquer** sur "🎯 Période"
2. **Définir** date de début (ex: 15/07/2025)
3. **Définir** date de fin (ex: 25/07/2025)
4. **Voir** la consommation sur 11 jours
5. **Analyser** une période spécifique

## 💡 Cas d'Usage

### **Analyse Annuelle**
- **Bilan de fin d'année** : Consommation totale 2024
- **Comparaison inter-annuelle** : 2023 vs 2024 vs 2025
- **Planification budgétaire** : Prévisions pour 2026
- **Tendances long terme** : Évolution sur plusieurs années

### **Analyse Hebdomadaire**
- **Patterns de semaine** : Lundi vs Vendredi vs Weekend
- **Optimisation des commandes** : Livraisons hebdomadaires
- **Gestion des pics** : Semaines de forte activité
- **Saisonnalité** : Semaines d'été vs hiver

### **Analyse de Période Spécifique**
- **Événements spéciaux** : Ramadan, Noël, vacances
- **Promotions** : Impact d'une campagne marketing
- **Problèmes techniques** : Période de panne ou problème
- **Tests** : Nouvelle recette ou nouveau produit

## 🚀 Exemples d'Analyse

### **Comparaison Hebdomadaire**
```
Semaine 29/2025 : 45.5 kg pâte à pizza
Semaine 30/2025 : 52.3 kg pâte à pizza (+15%)
→ Tendance à la hausse en été
```

### **Analyse Annuelle**
```
2024 : 520 kg pâte à pizza
2025 : 580 kg pâte à pizza (+11.5%)
→ Croissance constante de l'activité
```

### **Période Spécifique**
```
Ramadan 2025 (10/03 - 09/04) :
- Pâte à pizza : +25% vs période normale
- Viandes : +40% (plus de garnitures)
- Fromages : +15%
→ Adapter les stocks pour Ramadan 2026
```

## 📈 Avantages

### **Flexibilité Totale**
✅ **4 types de filtrage** : Mois, Année, Semaine, Période  
✅ **Périodes personnalisées** : Date X à Z au choix  
✅ **Granularité variable** : Du jour à l'année  
✅ **Comparaisons multiples** : Entre différentes périodes  

### **Analyse Précise**
✅ **Calculs automatiques** : Basés sur stocks quotidiens  
✅ **Données réelles** : Pas de saisie manuelle  
✅ **Précision garantie** : 3 décimales pour les quantités  
✅ **Cohérence parfaite** : Même logique de calcul  

### **Interface Intuitive**
✅ **Sélection simple** : Boutons clairs par type  
✅ **Navigation fluide** : Dialogues de sélection  
✅ **Feedback immédiat** : Titre mis à jour automatiquement  
✅ **Format adapté** : Affichage selon le type de période  

## 🎉 Résultat Final

Le filtrage de consommation est maintenant **complet et professionnel** avec :

1. **4 types de filtrage** : Mois, Année, Semaine, Période personnalisée
2. **Flexibilité totale** : De 1 jour à plusieurs années
3. **Interface intuitive** : Sélection simple par boutons
4. **Calculs automatiques** : Basés sur les stocks quotidiens réels
5. **Analyses variées** : Tendances, comparaisons, saisonnalité
6. **Périodes étendues** : 2020-2030 pour tous les types
7. **Précision garantie** : Données cohérentes et fiables

**Testez maintenant : Allez dans Stock → Consommation et explorez tous les types de filtrage !** 📊🔍

---

**Version** : YassinApp 4.2  
**Date** : 21 juillet 2025  
**Ajouts** : Filtrage par Année, Semaine, Période personnalisée, Interface avancée  
**Statut** : ✅ Système de filtrage complet et professionnel
