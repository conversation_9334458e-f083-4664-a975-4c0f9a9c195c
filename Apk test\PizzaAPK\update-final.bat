@echo off
setlocal enabledelayedexpansion

echo ========================================
echo   PIZZA CAISSE MANAGER - VERSION 1.6.0
echo ========================================
echo.
echo 🍕 Demarrage du serveur Expo...
echo 📱 Generation du QR code pour Expo Go
echo.

REM Aller dans le bon repertoire
cd /d "C:\Users\<USER>\Documents\AppCaisse\Apk test\PizzaAPK"
if errorlevel 1 (
    echo ❌ Erreur: Impossible d'acceder au repertoire
    pause
    exit /b 1
)

echo � Repertoire actuel: %CD%
echo.

REM Verifier si Node.js est installe
node --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Node.js n'est pas installe ou non accessible
    echo 💡 Installez Node.js depuis https://nodejs.org
    pause
    exit /b 1
)

echo ✅ Node.js detecte
echo.

REM Installer les dependances si necessaire
echo 📦 Verification des dependances...
if not exist "node_modules" (
    echo 📥 Installation des dependances...
    call npm install
    if errorlevel 1 (
        echo ❌ Erreur lors de l'installation des dependances
        pause
        exit /b 1
    )
)

echo ✅ Dependances OK
echo.

REM Essayer de demarrer Expo
echo � Demarrage d'Expo...
echo 📱 Le QR code va apparaitre dans quelques secondes...
echo.
echo ⏳ Patientez pendant le chargement...
echo.

REM Essayer plusieurs methodes pour demarrer Expo
echo 🔄 Tentative 1: Expo start normal...
call npx expo start --clear
if not errorlevel 1 goto :success

echo.
echo � Tentative 2: Expo start avec tunnel...
call npx expo start --tunnel --clear
if not errorlevel 1 goto :success

echo.
echo � Tentative 3: Expo start sur port different...
call npx expo start --port 8081 --clear
if not errorlevel 1 goto :success

echo.
echo � Tentative 4: Installation globale d'Expo CLI...
call npm install -g @expo/cli
call npx expo start --clear
if not errorlevel 1 goto :success

echo.
echo ❌ Toutes les tentatives ont echoue
echo.
echo 🔧 Solutions possibles:
echo    1. Redemarrez votre ordinateur
echo    2. Verifiez votre connexion internet
echo    3. Desactivez temporairement l'antivirus
echo    4. Executez en tant qu'administrateur
echo.
pause
exit /b 1

:success
echo.
echo ========================================
echo           QR CODE GENERE !
echo ========================================
echo.
echo ✅ Serveur Expo demarre avec succes !
echo 📱 Scannez le QR code avec Expo Go
echo.
echo 📋 Instructions:
echo    1. Ouvrez Expo Go sur votre telephone
echo    2. Scannez le QR code affiche
echo    3. L'application va se charger
echo    4. Testez les nouvelles fonctionnalites
echo.
echo � Nouvelles fonctionnalites:
echo    � Boutons mieux positionnes
echo    📱 Manipulation tactile amelioree
echo    🚪 Deconnexion repositionnee
echo.
echo ⚠️  Gardez cette fenetre ouverte pendant les tests
echo.
pause
